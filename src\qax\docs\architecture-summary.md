# QAX Next Edit 架构设计总结

## 项目概述

QAX Next Edit 是一个为 VSCode 扩展设计的智能代码编辑建议系统，提供两种推荐模式：
- **AST+LSP 快速分析**：0.1秒响应，专注语法级别修改
- **AI 模型深度分析**：5秒响应，提供逻辑完整性和代码质量建议

## 核心特性

### 1. 建议数组维护
- 为每个文档维护独立的建议数组
- 文档变更时自动过滤失效建议
- 建议生命周期管理（创建→验证→显示→执行/忽略）

### 2. 精确触发机制
- **行末输入** → 触发 Autocomplete
- **行中间输入** → 触发 AST+LSP 分析（1秒防抖）
- **文档空闲3秒** + 无未执行建议 → 触发模型推荐

### 3. 双模式分析引擎
- **AST+LSP 模式**：增量解析、缓存优化、符号引用分析
- **模型分析模式**：上下文收集、AI 推理、深度代码分析

### 4. 增量缓存系统
- AST 缓存：语法树增量更新，非持久化
- LSP 缓存：符号引用缓存，智能失效策略
- 内存管理：LRU 淘汰、大小限制、自动清理

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    NextEditManager                          │
│                     (主管理器)                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│ SuggestionManager│  TriggerManager │    AnalysisEngine      │
│   (建议管理)     │   (触发控制)    │     (分析引擎)          │
├─────────────────┼─────────────────┼─────────────────────────┤
│ • 建议数组维护   │ • 触发条件检查  │ • AST+LSP 快速分析      │
│ • 过滤失效建议   │ • 防抖机制      │ • 模型深度分析          │
│ • 建议验证排序   │ • 互斥控制      │ • 上下文收集            │
│ • 生命周期管理   │ • 位置检测      │ • 结果合并处理          │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 数据模型

### 建议结构（符合用户要求）
```json
{
  "type": "add|modify|delete",
  "description": "Change: xxx -> yyy | Change: Add xxx | Change: Del xxx",
  "location": {
    "anchor": "unique code pattern to locate the position",
    "position": "before|after|replace"
  },
  "patch": {
    "new_content": "new content to insert/replace with"
  }
}
```

### 关键设计决策

1. **完整 Diff 生成**：累积变更，生成语义完整的差异，而非单字符 diff
2. **唯一锚点定位**：使用代码模式而非行号，确保文档变更后仍能准确定位
3. **增量分析**：AST 和 LSP 数据的增量更新，避免全量重新解析
4. **非持久化缓存**：内存缓存，应用关闭时清理，避免数据一致性问题

## 性能目标

### AST+LSP 分析（0.1秒目标）
- 变更类型检测：10ms
- 增量 AST 解析：30ms  
- LSP 查询优化：50ms
- 建议生成：10ms

### 模型分析（5秒目标）
- 上下文构建：500ms
- 提示词生成：200ms
- 模型 API 调用：4000ms
- 响应解析验证：300ms

## 实现优先级

### 第一阶段：核心功能
1. SuggestionManager - 建议数组维护
2. TriggerManager - 触发机制实现
3. 完整 Diff 生成器
4. AST+LSP 快速分析器

### 第二阶段：深度分析
1. 模型分析器实现
2. 上下文收集优化
3. 提示词模板系统
4. 响应解析验证

### 第三阶段：性能优化
1. 增量缓存系统
2. 性能监控指标
3. 内存管理优化
4. 并发控制机制

## 技术难点与解决方案

### 1. 建议数组维护
**难点**：文档变更时准确识别哪些建议失效
**解决方案**：
- 使用代码模式锚点而非行号定位
- 实现智能的建议验证算法
- 建立建议与代码区域的依赖关系

### 2. 完整 Diff 生成
**难点**：累积多次变更，生成语义完整的差异
**解决方案**：
- 维护文档状态快照
- 实现变更累积算法
- 语义边界检测和扩展

### 3. 0.1秒响应时间
**难点**：AST+LSP 分析的极致性能优化
**解决方案**：
- 增量 AST 解析
- LSP 查询缓存和批处理
- 并行处理和异步优化
- 启发式快速检测

### 4. 触发精确性
**难点**：与 autocomplete 的完美互斥
**解决方案**：
- 精确的位置检测算法
- 状态机管理触发条件
- 防抖机制避免冲突
- 优先级和互斥控制

## 集成策略

### 与现有系统集成
1. **AutocompleteProvider**：共享防抖配置，互斥触发
2. **TreeSitterService**：复用现有解析器，扩展缓存
3. **TaskManager**：状态同步，避免冲突
4. **配置系统**：统一配置管理

### VSCode 集成
1. **事件监听**：文档变更、光标移动、配置变更
2. **命令注册**：建议执行、忽略、配置等命令
3. **UI 集成**：建议显示、状态指示、错误提示
4. **生命周期**：扩展激活、停用、资源清理

## 测试策略

### 单元测试重点
- 建议数组维护逻辑
- 触发条件判断准确性
- Diff 生成完整性
- 建议格式验证

### 集成测试重点
- 端到端工作流验证
- 性能基准测试
- 与 autocomplete 集成测试
- 多文档并发处理

### 用户体验测试
- 响应时间验证
- 建议质量评估
- UI 交互流畅性
- 错误处理友好性

## 部署和维护

### 配置管理
- 分层配置：全局、工作区、文件级
- 动态配置更新
- 配置验证和默认值
- 性能参数调优

### 监控和调试
- 性能指标收集
- 错误日志记录
- 分析结果追踪
- 用户行为统计

### 扩展性设计
- 插件化架构支持自定义分析器
- 多语言支持框架
- API 开放供其他扩展集成
- 配置化行为控制

## 总结

QAX Next Edit 通过精心设计的架构，实现了智能、高效、用户友好的代码编辑建议系统。核心创新包括：

1. **双模式分析引擎**：平衡速度与深度
2. **精确触发机制**：避免与现有功能冲突
3. **智能建议管理**：自动维护建议生命周期
4. **增量缓存系统**：优化性能和内存使用
5. **标准化数据格式**：符合用户要求的 JSON 结构

该架构设计充分考虑了用户需求、性能要求、系统集成和未来扩展，为实现高质量的代码编辑助手奠定了坚实基础。
