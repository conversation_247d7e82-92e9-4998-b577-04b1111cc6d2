/**
 * QAX Next Edit - 入口文件
 * 导出所有核心组件和类型
 */

// 主管理器
export { NextEditManager, NextEditError } from "./NextEditManager"
import { NextEditManager } from "./NextEditManager"

// 管理器组件
export { SuggestionManager } from "./managers/SuggestionManager"
export { TriggerManager } from "./managers/TriggerManager"

// 分析器组件
export { DiffGenerator } from "./analyzers/DiffGenerator"

// 工具类
export { DebounceManager, ThrottleManager, createDebouncedFunction, createThrottledFunction } from "./utils/DebounceManager"

// 类型定义
export * from "./types/Suggestion"
export * from "./types/AnalysisContext"
export * from "./types/NextEditSettings"

// 默认配置
export { DEFAULT_NEXT_EDIT_SETTINGS } from "./types/NextEditSettings"

/**
 * 创建 NextEditManager 实例的工厂函数
 */
export function createNextEditManager(settings?: Partial<import("./types/NextEditSettings").NextEditSettings>): NextEditManager {
	const { NextEditConfigValidator, DEFAULT_NEXT_EDIT_SETTINGS } = require("./types/NextEditSettings")

	// 合并配置
	const finalSettings = settings
		? NextEditConfigValidator.merge(DEFAULT_NEXT_EDIT_SETTINGS, settings)
		: DEFAULT_NEXT_EDIT_SETTINGS

	// 验证配置
	const validation = NextEditConfigValidator.validate(finalSettings)
	if (!validation.isValid) {
		console.warn("NextEdit configuration validation failed:", validation.errors)
		// 可以选择抛出错误或使用默认配置
	}

	if (validation.warnings.length > 0) {
		console.warn("NextEdit configuration warnings:", validation.warnings)
	}

	return new NextEditManager(finalSettings)
}

/**
 * 版本信息
 */
export const VERSION = "1.0.0"

/**
 * 功能特性标识
 */
export const FEATURES = {
	/** 建议数组维护 */
	SUGGESTION_MANAGEMENT: true,
	/** 双模式分析引擎 */
	DUAL_MODE_ANALYSIS: true,
	/** 精确触发机制 */
	PRECISE_TRIGGERING: true,
	/** 增量缓存系统 */
	INCREMENTAL_CACHING: true,
	/** 与 Autocomplete 集成 */
	AUTOCOMPLETE_INTEGRATION: true,
	/** 模型深度分析 */
	MODEL_ANALYSIS: false, // 待实现
	/** AST+LSP 快速分析 */
	AST_LSP_ANALYSIS: false, // 待实现
	/** 性能监控 */
	PERFORMANCE_MONITORING: false, // 待实现
	/** UI 集成 */
	UI_INTEGRATION: false, // 待实现
} as const

/**
 * 系统状态
 */
export interface NextEditSystemStatus {
	/** 是否已初始化 */
	initialized: boolean
	/** 活跃文档数量 */
	activeDocuments: number
	/** 总建议数量 */
	totalSuggestions: number
	/** 正在进行的分析数量 */
	activeAnalyses: number
	/** 系统版本 */
	version: string
	/** 启用的功能 */
	enabledFeatures: string[]
}

/**
 * 获取系统状态
 */
export function getSystemStatus(manager: NextEditManager): NextEditSystemStatus {
	// 这里需要从 manager 中获取实际状态
	// 目前返回模拟数据
	return {
		initialized: true, // manager.isInitialized
		activeDocuments: 0, // manager.getActiveDocumentCount()
		totalSuggestions: 0, // manager.getTotalSuggestionCount()
		activeAnalyses: 0, // manager.getActiveAnalysisCount()
		version: VERSION,
		enabledFeatures: Object.entries(FEATURES)
			.filter(([_, enabled]) => enabled)
			.map(([feature, _]) => feature),
	}
}

/**
 * 日志工具
 */
export class NextEditLogger {
	private static instance: NextEditLogger
	private logLevel: "error" | "warn" | "info" | "debug" = "info"

	static getInstance(): NextEditLogger {
		if (!NextEditLogger.instance) {
			NextEditLogger.instance = new NextEditLogger()
		}
		return NextEditLogger.instance
	}

	setLogLevel(level: "error" | "warn" | "info" | "debug"): void {
		this.logLevel = level
	}

	error(message: string, ...args: any[]): void {
		console.error(`[NextEdit ERROR] ${message}`, ...args)
	}

	warn(message: string, ...args: any[]): void {
		if (this.shouldLog("warn")) {
			console.warn(`[NextEdit WARN] ${message}`, ...args)
		}
	}

	info(message: string, ...args: any[]): void {
		if (this.shouldLog("info")) {
			console.info(`[NextEdit INFO] ${message}`, ...args)
		}
	}

	debug(message: string, ...args: any[]): void {
		if (this.shouldLog("debug")) {
			console.debug(`[NextEdit DEBUG] ${message}`, ...args)
		}
	}

	private shouldLog(level: "error" | "warn" | "info" | "debug"): boolean {
		const levels = ["error", "warn", "info", "debug"]
		return levels.indexOf(level) <= levels.indexOf(this.logLevel)
	}
}

/**
 * 性能监控工具
 */
export class NextEditPerformanceMonitor {
	private static instance: NextEditPerformanceMonitor
	private metrics: Map<string, number[]> = new Map()

	static getInstance(): NextEditPerformanceMonitor {
		if (!NextEditPerformanceMonitor.instance) {
			NextEditPerformanceMonitor.instance = new NextEditPerformanceMonitor()
		}
		return NextEditPerformanceMonitor.instance
	}

	/**
	 * 记录性能指标
	 */
	recordMetric(name: string, value: number): void {
		if (!this.metrics.has(name)) {
			this.metrics.set(name, [])
		}
		this.metrics.get(name)!.push(value)

		// 保持最近100个记录
		const values = this.metrics.get(name)!
		if (values.length > 100) {
			values.shift()
		}
	}

	/**
	 * 获取性能统计
	 */
	getMetricStats(name: string): { avg: number; min: number; max: number; count: number } | null {
		const values = this.metrics.get(name)
		if (!values || values.length === 0) {
			return null
		}

		const sum = values.reduce((a, b) => a + b, 0)
		return {
			avg: sum / values.length,
			min: Math.min(...values),
			max: Math.max(...values),
			count: values.length,
		}
	}

	/**
	 * 获取所有指标
	 */
	getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
		const result: Record<string, { avg: number; min: number; max: number; count: number }> = {}

		for (const name of Array.from(this.metrics.keys())) {
			const stats = this.getMetricStats(name)
			if (stats) {
				result[name] = stats
			}
		}

		return result
	}

	/**
	 * 清除指标
	 */
	clearMetrics(): void {
		this.metrics.clear()
	}
}

/**
 * 工具函数：测量执行时间
 */
export function measureTime<T>(fn: () => T | Promise<T>, metricName?: string): Promise<T> {
	const start = performance.now()

	const result = fn()

	if (result instanceof Promise) {
		return result.then((value) => {
			const duration = performance.now() - start
			if (metricName) {
				NextEditPerformanceMonitor.getInstance().recordMetric(metricName, duration)
			}
			return value
		})
	} else {
		const duration = performance.now() - start
		if (metricName) {
			NextEditPerformanceMonitor.getInstance().recordMetric(metricName, duration)
		}
		return Promise.resolve(result)
	}
}

/**
 * 工具函数：创建带性能监控的函数
 */
export function withPerformanceMonitoring<T extends (...args: any[]) => any>(fn: T, metricName: string): T {
	return ((...args: any[]) => {
		return measureTime(() => fn(...args), metricName)
	}) as T
}
