# QAX Next Edit 详细实现任务

## 阶段一：核心基础设施（已完成架构设计）

### 1. 核心数据结构和接口定义 ✅
- [x] 定义符合用户要求的 Suggestion JSON 结构
  - type: 'add' | 'modify' | 'delete'
  - description: "Change: xxx -> yyy" 格式
  - location: { anchor, position: 'before'|'after'|'replace' }
  - patch: { new_content }
- [x] 创建完整的配置接口 NextEditSettings
- [x] 定义缓存数据结构（ASTCache, LSPCache）
- [x] 创建分析上下文接口
- [x] 定义错误处理接口

## 阶段二：建议管理系统

### 2. SuggestionManager 建议管理器
- [ ] 2.1 创建 DocumentSuggestionStore 存储结构
  - [ ] 实现每个文档的建议数组维护
  - [ ] 添加文档元数据管理（版本、统计等）
  - [ ] 实现建议生命周期状态管理

- [ ] 2.2 实现建议的增删改查功能
  - [ ] addSuggestions(documentUri, suggestions[])
  - [ ] getSuggestions(documentUri): Suggestion[]
  - [ ] removeSuggestion(documentUri, suggestionId)
  - [ ] clearSuggestions(documentUri)

- [ ] 2.3 实现文档变更时的建议过滤逻辑
  - [ ] filterObsoleteSuggestions() - 核心功能
  - [ ] 验证建议的 anchor 是否仍然有效
  - [ ] 处理文档版本变更导致的建议失效

- [ ] 2.4 实现建议验证和质量控制
  - [ ] validateSuggestion() - 验证单个建议
  - [ ] validateAllSuggestions() - 批量验证
  - [ ] 建议去重逻辑
  - [ ] 建议优先级排序

- [ ] 2.5 实现建议统计和查询功能
  - [ ] hasUnexecutedSuggestions() - 检查未执行建议
  - [ ] getSuggestionCount()
  - [ ] getSuggestionsByType/Source/Category
  - [ ] 建议执行统计

- [ ] 2.6 编写完整的单元测试
  - [ ] 建议数组维护测试
  - [ ] 过滤逻辑测试
  - [ ] 验证机制测试
  - [ ] 并发操作测试

## 阶段三：触发机制系统

### 3. TriggerManager 触发管理器
- [ ] 3.1 实现精确的触发条件检查
  - [ ] shouldTriggerModelRecommendation() - 文档空闲3秒 + 无未执行建议
  - [ ] shouldTriggerASTLSPAnalysis() - 输入位置在行中间 + 1秒防抖
  - [ ] shouldTriggerAutocomplete() - 输入位置在行末

- [ ] 3.2 实现位置检测逻辑
  - [ ] isPositionAtLineEnd() - 行末检测
  - [ ] isPositionInLineMiddle() - 行中间检测
  - [ ] getInputContext() - 获取输入上下文

- [ ] 3.3 实现防抖机制
  - [ ] DebounceManager 类
  - [ ] 模型推荐防抖（3秒）
  - [ ] AST+LSP分析防抖（1秒）
  - [ ] 防止重复触发逻辑

- [ ] 3.4 实现与 autocomplete 的互斥控制
  - [ ] 触发条件互斥逻辑
  - [ ] 状态管理和同步
  - [ ] 集成现有 AutocompleteProvider

- [ ] 3.5 编写触发机制测试
  - [ ] 触发条件测试
  - [ ] 防抖机制测试
  - [ ] 互斥控制测试

## 阶段四：分析引擎系统

### 4. AnalysisEngine 分析引擎
- [ ] 4.1 实现分析任务调度
  - [ ] scheduleAnalysis() - 分析任务调度
  - [ ] 优先级队列管理
  - [ ] 并发控制（最大并发数限制）
  - [ ] 任务取消机制

- [ ] 4.2 实现完整的 Diff 生成
  - [ ] generateDocumentDiff() - 生成完整diff（非单字符）
  - [ ] generateIncrementalDiff() - 增量diff生成
  - [ ] 累积变更逻辑
  - [ ] 变更类型检测

- [ ] 4.3 实现上下文收集
  - [ ] collectRecentChanges() - 收集最近所有修改
  - [ ] collectRelatedFiles() - 收集相关文件
  - [ ] buildAnalysisContext() - 构建分析上下文

- [ ] 4.4 实现分析结果处理
  - [ ] mergeAnalysisResults() - 合并分析结果
  - [ ] filterDuplicateSuggestions() - 去重
  - [ ] 结果质量评估

### 5. ASTLSPAnalyzer AST+LSP分析器（0.1秒目标）
- [ ] 5.1 集成 TreeSitter 解析器
  - [ ] 使用现有 src/services/tree-sitter 服务
  - [ ] 支持多语言解析
  - [ ] AST缓存机制

- [ ] 5.2 实现增量AST解析
  - [ ] updateASTIncremental() - 增量更新
  - [ ] 缓存命中优化
  - [ ] 解析性能优化（目标30ms）

- [ ] 5.3 实现变更类型检测
  - [ ] detectChangeType() - 快速检测变更类型
  - [ ] 符号重命名检测
  - [ ] 函数签名变更检测
  - [ ] 变量声明检测

- [ ] 5.4 实现LSP集成和引用查找
  - [ ] findAllReferences() - 查找所有引用
  - [ ] findDefinition() - 查找定义
  - [ ] LSP缓存优化
  - [ ] 跨文件引用分析

- [ ] 5.5 实现建议生成
  - [ ] generateRenameSuggestions() - 重命名建议
  - [ ] generateSignatureChangeSuggestions() - 签名变更建议
  - [ ] 生成符合JSON格式的建议

- [ ] 5.6 性能优化和测试
  - [ ] 响应时间优化（目标0.1秒）
  - [ ] 内存使用优化
  - [ ] 并发处理测试

### 6. ModelAnalyzer 模型分析器（5秒目标）
- [ ] 6.1 实现模型API集成
  - [ ] ModelAPIClient 实现
  - [ ] API调用优化
  - [ ] 错误处理和重试机制

- [ ] 6.2 实现上下文构建
  - [ ] buildAnalysisContext() - 构建丰富上下文
  - [ ] 包含最近变更和相关文件
  - [ ] 上下文大小优化

- [ ] 6.3 实现提示词生成
  - [ ] 针对用户要求的分析目标：
    - 不完整的逻辑
    - 未实现的函数
    - 未调用的变量
    - 格式问题
    - lint问题
  - [ ] 提示词模板管理
  - [ ] 动态提示词优化

- [ ] 6.4 实现响应解析
  - [ ] parseModelResponse() - 解析模型响应
  - [ ] 提取符合JSON格式的建议
  - [ ] 响应验证和错误处理

- [ ] 6.5 实现缓存和优化
  - [ ] 分析结果缓存
  - [ ] 上下文哈希优化
  - [ ] 性能监控

## 阶段五：缓存系统

### 7. 缓存管理系统
- [ ] 7.1 实现AST缓存
  - [ ] ASTCache 数据结构
  - [ ] 增量更新机制
  - [ ] 缓存失效策略

- [ ] 7.2 实现LSP缓存
  - [ ] LSPCache 数据结构
  - [ ] 符号引用缓存
  - [ ] 诊断信息缓存

- [ ] 7.3 实现缓存管理
  - [ ] 内存限制控制
  - [ ] LRU淘汰策略
  - [ ] 缓存性能监控

- [ ] 7.4 非持久化设计
  - [ ] 内存缓存实现
  - [ ] 应用关闭时清理
  - [ ] 缓存重建机制

## 阶段六：集成和优化

### 8. 系统集成
- [ ] 8.1 NextEditManager 主管理器
  - [ ] 协调所有子组件
  - [ ] 文档生命周期管理
  - [ ] 事件处理和分发

- [ ] 8.2 VSCode 集成
  - [ ] 文档变更事件监听
  - [ ] 命令注册
  - [ ] UI集成

- [ ] 8.3 与现有系统集成
  - [ ] AutocompleteProvider 集成
  - [ ] TaskManager 集成
  - [ ] 配置系统集成

### 9. 测试和验证
- [ ] 9.1 端到端测试
  - [ ] 完整工作流测试
  - [ ] 性能基准测试
  - [ ] 用户体验测试

- [ ] 9.2 性能验证
  - [ ] AST+LSP分析响应时间（0.1秒）
  - [ ] 模型分析响应时间（5秒）
  - [ ] 内存使用监控

- [ ] 9.3 质量验证
  - [ ] 建议准确性测试
  - [ ] 建议过滤效果测试
  - [ ] 错误处理测试

## 实现优先级

### 高优先级（核心功能）
1. SuggestionManager - 建议数组维护
2. TriggerManager - 触发机制
3. 完整Diff生成
4. AST+LSP快速分析

### 中优先级（性能优化）
1. 缓存系统
2. 模型分析器
3. 性能优化

### 低优先级（增强功能）
1. UI优化
2. 高级配置
3. 监控和调试

## 关键技术难点

1. **建议数组维护**：文档变更时准确过滤失效建议
2. **完整Diff生成**：累积变更，生成语义完整的diff
3. **0.1秒响应**：AST+LSP分析的极致性能优化
4. **触发精确性**：与autocomplete的完美互斥
5. **增量缓存**：高效的AST和LSP缓存更新
