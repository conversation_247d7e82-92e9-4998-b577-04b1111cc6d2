/**
 * QAX Next Edit - TriggerManager 单元测试
 * 测试触发条件检测、防抖机制、互斥控制、位置检测逻辑
 */

import * as vscode from "vscode"
import { TriggerManager } from "../managers/TriggerManager"
import { SuggestionManager } from "../managers/SuggestionManager"
import { AnalysisType } from "../types/AnalysisContext"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { expect, createMockPosition } from "./test-utils"

// 模拟VSCode API
const mockVscode = {
	Range: class Range {
		constructor(
			public startLine: number,
			public startChar: number,
			public endLine: number,
			public endChar: number,
		) {
			this.start = { line: startLine, character: startChar }
			this.end = { line: endLine, character: endChar }
		}
		start: { line: number; character: number }
		end: { line: number; character: number }
	},
	Position: class Position {
		constructor(
			public line: number,
			public character: number,
		) {}
	},
	Uri: {
		parse: (uri: string) => ({
			toString: () => uri,
			fsPath: uri.replace("file://", ""),
		}),
	},
}

// 设置全局模拟
Object.assign(global, { vscode: mockVscode })

/**
 * 创建模拟文档
 */
function createMockDocument(uri: string, content: string, languageId: string = "typescript"): vscode.TextDocument {
	const lines = content.split("\n")

	return {
		uri: mockVscode.Uri.parse(uri),
		fileName: uri,
		isUntitled: false,
		languageId,
		version: 1,
		isDirty: false,
		isClosed: false,
		save: () => Promise.resolve(true),
		eol: 1,
		lineCount: lines.length,
		lineAt: (lineOrPosition: number | vscode.Position) => {
			const lineNumber = typeof lineOrPosition === "number" ? lineOrPosition : lineOrPosition.line
			return {
				lineNumber,
				text: lines[lineNumber] || "",
				range: new mockVscode.Range(lineNumber, 0, lineNumber, (lines[lineNumber] || "").length),
				rangeIncludingLineBreak: new mockVscode.Range(lineNumber, 0, lineNumber + 1, 0),
				firstNonWhitespaceCharacterIndex: 0,
				isEmptyOrWhitespace: (lines[lineNumber] || "").trim().length === 0,
			}
		},
		offsetAt: () => 0,
		positionAt: () => createMockPosition(0, 0),
		getText: () => content,
		getWordRangeAtPosition: () => undefined,
		validateRange: (range: vscode.Range) => range,
		validatePosition: (position: vscode.Position) => position,
	} as any as vscode.TextDocument
}

/**
 * 创建模拟文档（从行数组）
 */
function createMockDocumentFromLines(uri: string, lines: string[]): vscode.TextDocument {
	const content = lines.join("\n")
	return createMockDocument(uri, content)
}

describe("TriggerManager 测试", () => {
	let triggerManager: TriggerManager
	let suggestionManager: SuggestionManager
	const testDocumentUri = "file:///test/example.ts"

	beforeEach(() => {
		suggestionManager = new SuggestionManager(DEFAULT_NEXT_EDIT_SETTINGS)
		triggerManager = new TriggerManager(DEFAULT_NEXT_EDIT_SETTINGS, suggestionManager)
	})

	afterEach(() => {
		triggerManager.dispose()
		suggestionManager.dispose()
	})

	describe("基本功能测试", () => {
		test("应该正确初始化", () => {
			expect(triggerManager).toBeDefined()
		})

		test("应该能够检查触发条件", async () => {
			const document = createMockDocument(testDocumentUri, 'console.log("hello");')
			const position = createMockPosition(0, 10)

			const result = await triggerManager.checkTriggerConditions(document, position, "text_change")
			expect(typeof result).toBe("boolean")
		})

		test("应该能够处理位置检测", () => {
			const document = createMockDocument(testDocumentUri, 'console.log("test");')
			const position = createMockPosition(0, 10)

			const isAtEnd = triggerManager.isPositionAtLineEnd(document, position)
			const isInMiddle = triggerManager.isPositionInLineMiddle(document, position)

			expect(typeof isAtEnd).toBe("boolean")
			expect(typeof isInMiddle).toBe("boolean")
		})
	})

	describe("错误处理测试", () => {
		test("应该处理无效文档", async () => {
			const invalidDocument = null as any
			const position = createMockPosition(0, 0)

			await expect(triggerManager.checkTriggerConditions(invalidDocument, position, "text_change")).rejects.toThrow()
		})

		test("应该处理空文档", async () => {
			const emptyDocument = createMockDocument(testDocumentUri, "")
			const position = createMockPosition(0, 0)

			// 应该不会抛出错误
			await triggerManager.checkTriggerConditions(emptyDocument, position, "text_change")
			expect(true).toBe(true)
		})
	})

	describe("性能测试", () => {
		test("触发条件检查应该在合理时间内完成", async () => {
			const document = createMockDocument(testDocumentUri, "function test() {}")
			const position = createMockPosition(0, 10)

			const startTime = performance.now()
			await triggerManager.checkTriggerConditions(document, position, "text_change")
			const duration = performance.now() - startTime

			expect(duration).toBeLessThan(100) // 应该在100ms内完成
		})
	})
})

console.log("✅ TriggerManager 测试文件已修复")
