/**
 * 简单的 Next Edit 服务测试
 * 不依赖 VS Code API，只测试核心功能
 */

import { NextEditManager } from "./NextEditManager"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "./types/NextEditSettings"
import { NextEditLogger } from "./index"

/**
 * 运行简单测试
 */
async function runSimpleTest() {
	console.log("🚀 开始运行 Next Edit 服务简单测试...")

	try {
		// 设置日志级别
		const logger = NextEditLogger.getInstance()
		logger.setLogLevel("debug")

		// 1. 创建管理器实例
		console.log("📦 创建 NextEditManager 实例...")
		const manager = new NextEditManager(DEFAULT_NEXT_EDIT_SETTINGS)

		// 2. 测试基本属性
		console.log("🔍 测试基本属性...")
		console.log(`- 服务已启用: ${DEFAULT_NEXT_EDIT_SETTINGS.enabled}`)
		console.log(`- 模型推荐已启用: ${DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation.enabled}`)
		console.log(`- AST+LSP分析已启用: ${DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis.enabled}`)
		console.log(`- 日志级别: ${DEFAULT_NEXT_EDIT_SETTINGS.debug.logLevel}`)

		// 3. 测试事件监听器
		console.log("🔗 设置事件监听器...")
		let suggestionCount = 0
		let analysisCount = 0
		let errorCount = 0

		manager.onSuggestionGenerated((suggestions) => {
			suggestionCount += suggestions.length
			console.log(`✅ 生成了 ${suggestions.length} 个建议 (总计: ${suggestionCount})`)
		})

		manager.onAnalysisCompleted((documentUri, duration) => {
			analysisCount++
			console.log(`⚡ 分析完成 #${analysisCount}: ${documentUri} (耗时: ${duration}ms)`)
		})

		manager.onError((error) => {
			errorCount++
			console.error(`❌ 错误 #${errorCount}: ${error.message}`)
		})

		// 4. 测试配置更新
		console.log("⚙️ 测试配置更新...")
		const newSettings = {
			...DEFAULT_NEXT_EDIT_SETTINGS,
			debug: {
				...DEFAULT_NEXT_EDIT_SETTINGS.debug,
				logLevel: "info" as const,
			},
		}
		manager.updateConfiguration(newSettings)
		console.log("✅ 配置更新成功")

		// 5. 测试建议管理
		console.log("💡 测试建议管理...")
		const testDocumentUri = "file:///test/example.ts"
		const suggestions = manager.getSuggestions(testDocumentUri)
		console.log(`当前建议数量: ${suggestions.length}`)

		// 6. 测试分析状态
		console.log("🔍 测试分析状态...")
		const isAnalyzing = manager.isAnalyzing(testDocumentUri)
		console.log(`正在分析: ${isAnalyzing}`)

		// 7. 测试清理
		console.log("🧹 测试清理...")
		await manager.dispose()
		console.log("✅ 清理完成")

		// 8. 输出测试结果
		console.log("\n📊 测试结果:")
		console.log(`- 建议生成次数: ${suggestionCount}`)
		console.log(`- 分析完成次数: ${analysisCount}`)
		console.log(`- 错误次数: ${errorCount}`)

		console.log("\n✅ Next Edit 服务简单测试完成！")
		return true

	} catch (error) {
		console.error("❌ Next Edit 服务测试失败:", error)
		return false
	}
}

/**
 * 测试日志功能
 */
function testLogging() {
	console.log("\n🔍 测试日志功能...")

	const logger = NextEditLogger.getInstance()

	// 测试不同日志级别
	logger.setLogLevel("debug")
	logger.debug("这是一条调试信息")
	logger.info("这是一条信息")
	logger.warn("这是一条警告")
	logger.error("这是一条错误信息")

	// 测试日志级别过滤
	logger.setLogLevel("warn")
	console.log("设置日志级别为 warn，以下应该只显示 warn 和 error:")
	logger.debug("这条调试信息不应该显示")
	logger.info("这条信息不应该显示")
	logger.warn("这条警告应该显示")
	logger.error("这条错误应该显示")

	console.log("✅ 日志功能测试完成")
}

/**
 * 主函数
 */
async function main() {
	console.log("=".repeat(50))
	console.log("QAX Next Edit 服务测试")
	console.log("=".repeat(50))

	// 测试日志功能
	testLogging()

	// 运行简单测试
	const success = await runSimpleTest()

	console.log("\n" + "=".repeat(50))
	if (success) {
		console.log("🎉 所有测试通过！")
	} else {
		console.log("💥 测试失败！")
		process.exit(1)
	}
}

// 如果直接运行此文件
if (require.main === module) {
	main().catch((error) => {
		console.error("测试运行失败:", error)
		process.exit(1)
	})
}

export { runSimpleTest, testLogging }
