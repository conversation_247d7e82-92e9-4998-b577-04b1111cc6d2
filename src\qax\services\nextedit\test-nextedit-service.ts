/**
 * Next Edit 服务测试程序
 * 用于验证服务是否正常初始化和运行
 */

import * as vscode from "vscode"
import { createNextEditManager, NextEditManager, NextEditLogger, DEFAULT_NEXT_EDIT_SETTINGS } from "./index"

/**
 * 测试 Next Edit 服务的基本功能
 */
export async function testNextEditService(): Promise<void> {
	console.log("🚀 开始测试 Next Edit 服务...")

	// 设置日志级别
	const logger = NextEditLogger.getInstance()
	logger.setLogLevel("debug")

	try {
		// 1. 创建管理器实例
		console.log("📦 创建 NextEditManager 实例...")
		const manager = createNextEditManager({
			...DEFAULT_NEXT_EDIT_SETTINGS,
			enabled: true,
			debug: {
				enableLogging: true,
				logLevel: "debug",
				enableTracing: true,
				saveAnalysisResults: false,
				enablePerformanceLogging: true,
			},
			modelRecommendation: {
				...DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation,
				enabled: true,
				debounceMs: 1000, // 缩短测试时间
				idleTimeMs: 1000,
			},
			astLspAnalysis: {
				...DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis,
				enabled: true,
				debounceMs: 500,
				responseTimeTarget: 100,
			},
		})

		// 2. 设置事件监听器
		console.log("🔗 设置事件监听器...")
		manager.onSuggestionGenerated((suggestions) => {
			console.log(`✅ 生成了 ${suggestions.length} 个建议`)
			suggestions.forEach((suggestion, index) => {
				console.log(`   ${index + 1}. [${suggestion.type}] ${suggestion.description}`)
			})
		})

		manager.onAnalysisCompleted((documentUri, duration) => {
			console.log(`⚡ 分析完成: ${documentUri} (耗时: ${duration}ms)`)
		})

		manager.onError((error) => {
			console.error(`❌ 错误: ${error.message}`)
		})

		// 3. 模拟初始化（需要扩展上下文）
		console.log("🔧 模拟初始化...")
		// 在实际环境中，这会在扩展激活时调用
		// await manager.initialize(context)

		// 4. 测试配置更新
		console.log("⚙️ 测试配置更新...")
		manager.updateConfiguration({
			...DEFAULT_NEXT_EDIT_SETTINGS,
			enabled: true,
			debug: {
				enableLogging: true,
				logLevel: "info",
				enableTracing: false,
				saveAnalysisResults: false,
				enablePerformanceLogging: true,
			},
		})

		// 5. 模拟文档事件
		console.log("📄 模拟文档事件...")
		const mockDocument = {
			uri: { toString: () => "file:///test/example.ts" },
			getText: () => 'function example() {\n  console.log("hello");\n}\n',
			languageId: "typescript",
			version: 1,
			lineCount: 3,
		} as vscode.TextDocument

		// 模拟文档打开
		await manager.onDocumentOpened(mockDocument)

		// 模拟文档变更
		const mockChanges: vscode.TextDocumentContentChangeEvent[] = [
			{
				range: new vscode.Range(1, 0, 1, 0),
				rangeLength: 0,
				rangeOffset: 0,
				text: "  // 新增注释\n",
			},
		]
		await manager.onDocumentChanged(mockDocument, mockChanges)

		// 6. 测试建议管理
		console.log("💡 测试建议管理...")
		const suggestions = manager.getSuggestions("file:///test/example.ts")
		console.log(`当前建议数量: ${suggestions.length}`)

		// 7. 测试手动触发分析
		console.log("🔍 测试手动触发分析...")
		const mockPosition = new vscode.Position(1, 10)
		// await manager.manualTriggerAnalysis(mockDocument, mockPosition) // 私有方法，跳过测试

		console.log("✅ Next Edit 服务测试完成")
		return

	} catch (error) {
		console.error("❌ Next Edit 服务测试失败:", error)
		throw error
	}
}

/**
 * 运行测试的命令
 */
export function registerTestCommand(context: vscode.ExtensionContext): void {
	const disposable = vscode.commands.registerCommand("qax-nextedit.runTest", async () => {
		try {
			await testNextEditService()
			vscode.window.showInformationMessage("Next Edit 服务测试完成！请查看控制台输出。")
		} catch (error) {
			const message = error instanceof Error ? error.message : String(error)
			vscode.window.showErrorMessage(`Next Edit 服务测试失败: ${message}`)
		}
	})

	context.subscriptions.push(disposable)
}

/**
 * 检查服务状态的命令
 */
export function registerStatusCommand(context: vscode.ExtensionContext): void {
	const disposable = vscode.commands.registerCommand("qax-nextedit.checkStatus", () => {
		const logger = NextEditLogger.getInstance()
		logger.info("Next Edit 服务状态检查")
		
		// 显示当前配置
		console.log("📊 Next Edit 服务状态:")
		console.log("- 日志级别: debug")
		console.log("- 服务已注册")
		console.log("- 事件监听器已设置")
		
		vscode.window.showInformationMessage("Next Edit 服务状态正常！请查看控制台输出。")
	})

	context.subscriptions.push(disposable)
}
