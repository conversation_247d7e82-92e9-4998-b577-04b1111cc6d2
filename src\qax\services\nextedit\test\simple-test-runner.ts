/**
 * 简单的测试运行器 - 验证代码能否正常实例化和运行
 */

import { NextEditManager } from "../NextEditManager"
import { CacheManager } from "../cache/CacheManager"
import { SuggestionManager } from "../managers/SuggestionManager"
import { ModelAnalyzer } from "../analyzers/ModelAnalyzer"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"

async function runSimpleTests() {
	console.log("🚀 开始运行简单测试...")

	try {
		// 测试 1: CacheManager 实例化
		console.log("📦 测试 CacheManager...")
		const cacheManager = new CacheManager(DEFAULT_NEXT_EDIT_SETTINGS)
		console.log("✅ CacheManager 实例化成功")

		// 测试缓存统计
		const stats = cacheManager.getCacheStats()
		console.log("✅ 缓存统计获取成功:", stats)

		// 测试 2: SuggestionManager 实例化
		console.log("💡 测试 SuggestionManager...")
		const suggestionManager = new SuggestionManager(DEFAULT_NEXT_EDIT_SETTINGS)
		console.log("✅ SuggestionManager 实例化成功")

		// 测试获取建议
		const suggestions = suggestionManager.getSuggestions("file:///test.ts")
		console.log("✅ 建议获取成功，数量:", suggestions.length)

		// 测试 3: ModelAnalyzer 实例化
		console.log("🧠 测试 ModelAnalyzer...")
		const modelAnalyzer = new ModelAnalyzer(DEFAULT_NEXT_EDIT_SETTINGS)
		console.log("✅ ModelAnalyzer 实例化成功")

		// 测试 4: NextEditManager 实例化
		console.log("⚙️ 测试 NextEditManager...")
		const nextEditManager = new NextEditManager(DEFAULT_NEXT_EDIT_SETTINGS)
		console.log("✅ NextEditManager 实例化成功")

		// 测试系统状态
		const systemStatus = await nextEditManager.getSystemStatus()
		console.log("✅ 系统状态获取成功:", systemStatus)

		// 清理资源
		console.log("🧹 清理资源...")
		nextEditManager.dispose()
		suggestionManager.dispose()
		modelAnalyzer.dispose()
		cacheManager.dispose()

		console.log("🎉 所有测试通过！")
		return true
	} catch (error) {
		console.error("❌ 测试失败:", error)
		return false
	}
}

// 运行测试
if (require.main === module) {
	runSimpleTests().then((success) => {
		process.exit(success ? 0 : 1)
	})
}

export { runSimpleTests }
