/**
 * QAX Next Edit - 建议数据结构
 * 严格按照用户要求的 JSON 格式定义
 */

import * as vscode from "vscode"

/**
 * 建议类型
 */
export type SuggestionType = "add" | "modify" | "delete"

/**
 * 建议位置
 */
export type SuggestionPosition = "before" | "after" | "replace"

/**
 * 建议来源
 */
export enum SuggestionSource {
	MODEL = "model",
	AST_LSP = "ast-lsp",
	HYBRID = "hybrid",
}

/**
 * 建议分类
 */
export enum SuggestionCategory {
	LOGIC_COMPLETION = "logic_completion", // 逻辑完整性
	FUNCTION_IMPLEMENTATION = "function_impl", // 函数实现
	VARIABLE_USAGE = "variable_usage", // 变量使用
	CODE_FORMATTING = "formatting", // 代码格式
	LINT_ISSUES = "lint", // 代码规范
	REFACTORING = "refactoring", // 重构建议
	PERFORMANCE = "performance", // 性能优化
	SECURITY = "security", // 安全问题
	DOCUMENTATION = "documentation", // 文档完善
}

/**
 * 影响级别
 */
export enum ImpactLevel {
	LOW = "low", // 低影响：格式、注释等
	MEDIUM = "medium", // 中等影响：变量重命名、小重构
	HIGH = "high", // 高影响：函数签名变更、大重构
	CRITICAL = "critical", // 关键影响：API变更、架构调整
}

/**
 * 建议位置信息（符合用户要求）
 */
export interface SuggestionLocation {
	/** 用于定位的唯一代码模式 */
	anchor: string
	/** 相对位置 */
	position: SuggestionPosition
	/** 可选的精确范围 */
	range?: vscode.Range
	/** 行号（用于快速定位） */
	lineNumber?: number
	/** 列号 */
	columnNumber?: number
	/** 上下文行（用于验证） */
	contextLines?: string[]
}

/**
 * 建议修改内容（符合用户要求）
 */
export interface SuggestionPatch {
	/** 新内容（符合用户要求的字段名） */
	new_content: string
	/** 原内容（用于replace类型） */
	old_content?: string
	/** 编码格式 */
	encoding?: string
	/** 编程语言 */
	language?: string
}

/**
 * 核心建议数据结构（严格按照用户要求）
 */
export interface Suggestion {
	/** 唯一标识符 */
	id: string
	/** 建议类型 */
	type: SuggestionType
	/** 变更描述，格式：Change: xxx -> yyy | Change: Add xxx | Change: Del xxx */
	description: string
	/** 位置信息 */
	location: SuggestionLocation
	/** 修改内容 */
	patch: SuggestionPatch

	// 扩展字段
	/** 置信度 0-1 */
	confidence: number
	/** 建议来源 */
	source: SuggestionSource
	/** 优先级 1-10 */
	priority: number
	/** 创建时间戳 */
	timestamp: number
	/** 建议分类 */
	category: SuggestionCategory
	/** 影响级别 */
	impact: ImpactLevel
	/** 标签 */
	tags: string[]
	/** 相关文件 */
	relatedFiles?: string[]
	/** 依赖的其他建议ID */
	dependencies?: string[]
}

/**
 * 建议状态
 */
export enum SuggestionState {
	CREATED = "created",
	VALIDATED = "validated",
	DISPLAYED = "displayed",
	EXECUTED = "executed",
	DISMISSED = "dismissed",
	OBSOLETE = "obsolete",
	INVALID = "invalid",
}

/**
 * 文档建议数据
 */
export interface DocumentSuggestionData {
	/** 建议数组 */
	suggestions: Suggestion[]
	/** 最后更新时间 */
	lastUpdate: number
	/** 文档版本号 */
	documentVersion: number
	/** 分析历史 */
	analysisHistory: AnalysisRecord[]
	/** 文档元数据 */
	metadata: DocumentMetadata
}

/**
 * 分析记录
 */
export interface AnalysisRecord {
	/** 分析ID */
	id: string
	/** 分析类型 */
	type: "model" | "ast-lsp"
	/** 开始时间 */
	startTime: number
	/** 结束时间 */
	endTime: number
	/** 生成的建议数量 */
	suggestionsCount: number
	/** 分析状态 */
	status: "success" | "error" | "cancelled"
	/** 错误信息 */
	error?: string
}

/**
 * 文档元数据
 */
export interface DocumentMetadata {
	/** 编程语言 */
	language: string
	/** 文件大小 */
	fileSize: number
	/** 行数 */
	lineCount: number
	/** 最后分析时间 */
	lastAnalysisTime: number
	/** 分析次数 */
	analysisCount: number
	/** 建议统计 */
	suggestionStats: SuggestionStats
}

/**
 * 建议统计
 */
export interface SuggestionStats {
	/** 总生成数 */
	totalGenerated: number
	/** 总执行数 */
	totalExecuted: number
	/** 总忽略数 */
	totalDismissed: number
	/** 平均置信度 */
	averageConfidence: number
	/** 分类分布 */
	categoryDistribution: Record<SuggestionCategory, number>
}

/**
 * 文档建议存储
 */
export interface DocumentSuggestionStore {
	[documentUri: string]: DocumentSuggestionData
}

/**
 * 建议生成器工具函数
 */
export class SuggestionBuilder {
	/**
	 * 创建建议
	 */
	static create(
		type: SuggestionType,
		description: string,
		anchor: string,
		position: SuggestionPosition,
		newContent: string,
		options: {
			oldContent?: string
			confidence?: number
			source?: SuggestionSource
			category?: SuggestionCategory
			impact?: ImpactLevel
			priority?: number
			tags?: string[]
		} = {},
	): Suggestion {
		return {
			id: this.generateId(),
			type,
			description: this.formatDescription(type, description),
			location: {
				anchor,
				position,
				lineNumber: this.extractLineNumber(anchor),
				contextLines: this.extractContextLines(anchor),
			},
			patch: {
				new_content: newContent,
				old_content: options.oldContent,
				encoding: "utf-8",
			},
			confidence: options.confidence ?? 0.8,
			source: options.source ?? SuggestionSource.AST_LSP,
			priority: options.priority ?? 5,
			timestamp: Date.now(),
			category: options.category ?? SuggestionCategory.LOGIC_COMPLETION,
			impact: options.impact ?? ImpactLevel.MEDIUM,
			tags: options.tags ?? [],
			relatedFiles: [],
			dependencies: [],
		}
	}

	/**
	 * 格式化描述信息
	 */
	private static formatDescription(type: SuggestionType, description: string): string {
		switch (type) {
			case "add":
				return `Change: Add ${description}`
			case "delete":
				return `Change: Del ${description}`
			case "modify":
				return description.includes("->") ? `Change: ${description}` : `Change: ${description}`
			default:
				return description
		}
	}

	/**
	 * 生成唯一ID
	 */
	private static generateId(): string {
		return `suggestion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
	}

	/**
	 * 从锚点提取行号
	 */
	private static extractLineNumber(anchor: string): number | undefined {
		// 简单实现，实际可能需要更复杂的逻辑
		return undefined
	}

	/**
	 * 从锚点提取上下文行
	 */
	private static extractContextLines(anchor: string): string[] {
		return anchor
			.split("|")
			.map((line) => line.trim())
			.filter((line) => line.length > 0)
	}
}
