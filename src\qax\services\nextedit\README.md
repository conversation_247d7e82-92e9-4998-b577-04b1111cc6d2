# QAX Next Edit

QAX Next Edit 是一个智能代码编辑建议系统，为 VSCode 扩展提供两种推荐模式：基于 AST+LSP 的快速语法推荐和基于 AI 模型的深度分析推荐。

## 核心特性

### 🎯 精确触发机制
- **行末输入** → 触发 Autocomplete
- **行中间输入** → 触发 AST+LSP 分析（1秒防抖）
- **文档空闲3秒** + 无未执行建议 → 触发模型推荐

### 🔄 建议数组维护
- 为每个文档维护独立的建议数组
- 文档变更时自动过滤失效建议
- 完整的建议生命周期管理

### ⚡ 双模式分析引擎
- **AST+LSP 快速模式**：0.1秒响应，语法级别分析
- **AI 模型深度模式**：5秒响应，逻辑完整性分析

### 📊 智能建议格式
严格按照用户要求的 JSON 格式：
```json
{
  "type": "add|modify|delete",
  "description": "Change: xxx -> yyy | Change: Add xxx | Change: Del xxx",
  "location": {
    "anchor": "unique code pattern to locate the position",
    "position": "before|after|replace"
  },
  "patch": {
    "new_content": "new content to insert/replace with"
  }
}
```

## 快速开始

### 基本使用

```typescript
import { createNextEditManager, DEFAULT_NEXT_EDIT_SETTINGS } from './src/qax/services/nextedit'

// 创建管理器实例
const nextEditManager = createNextEditManager({
  enabled: true,
  modelRecommendation: {
    enabled: true,
    debounceMs: 3000,
    idleTimeMs: 3000
  },
  astLspAnalysis: {
    enabled: true,
    debounceMs: 1000,
    responseTimeTarget: 100
  }
})

// 在扩展激活时初始化
export async function activate(context: vscode.ExtensionContext) {
  await nextEditManager.initialize(context)
  
  // 注册事件监听器
  nextEditManager.onSuggestionGenerated((suggestions) => {
    console.log('New suggestions generated:', suggestions.length)
  })
  
  nextEditManager.onAnalysisCompleted((documentUri, duration) => {
    console.log(`Analysis completed for ${documentUri} in ${duration}ms`)
  })
  
  nextEditManager.onError((error) => {
    console.error('NextEdit error:', error)
  })
}

// 在扩展停用时清理
export async function deactivate() {
  await nextEditManager.dispose()
}
```

### 手动触发分析

```typescript
// 获取当前文档的建议
const editor = vscode.window.activeTextEditor
if (editor) {
  const suggestions = nextEditManager.getSuggestions(editor.document.uri.toString())
  console.log('Current suggestions:', suggestions)
}

// 执行建议
const suggestion = suggestions[0]
if (suggestion) {
  const success = await nextEditManager.executeSuggestion(suggestion)
  console.log('Suggestion executed:', success)
}

// 忽略建议
await nextEditManager.dismissSuggestion(suggestion.id)

// 忽略所有建议
await nextEditManager.dismissAllSuggestions(editor.document.uri.toString())
```

## 配置选项

### 完整配置示例

```typescript
import { NextEditSettings } from './src/qax/services/nextedit'

const settings: NextEditSettings = {
  enabled: true,
  
  // 模型推荐配置
  modelRecommendation: {
    enabled: true,
    debounceMs: 3000,        // 防抖时间
    idleTimeMs: 3000,        // 空闲时间要求
    maxContextLines: 100,    // 最大上下文行数
    apiEndpoint: "https://aip.b.qianxin-inc.cn/v2",
    modelId: "Qwen3-Coder-480B-A35B-Instruct",
    maxTokens: 2000,
    temperature: 0.1,
    timeout: 30000,
    retryAttempts: 2,
    enableCache: true,
    analysisGoals: [
      'logic_completion',      // 逻辑完整性
      'function_implementation', // 函数实现
      'variable_usage',        // 变量使用
      'code_formatting',       // 代码格式
      'lint_issues'           // Lint问题
    ]
  },
  
  // AST+LSP分析配置
  astLspAnalysis: {
    enabled: true,
    debounceMs: 1000,        // 防抖时间
    responseTimeTarget: 100, // 响应时间目标
    enableIncrementalCache: true,
    maxCacheSize: 50,        // 最大缓存大小(MB)
    cacheExpirationMs: 300000, // 缓存过期时间
    enableParallelParsing: true,
    supportedLanguages: [
      'typescript', 'javascript', 'python', 'rust', 'go',
      'cpp', 'c', 'csharp', 'java', 'php', 'swift', 'kotlin'
    ]
  },
  
  // 建议过滤配置
  suggestionFilter: {
    minConfidence: 0.6,      // 最小置信度
    maxSuggestionsPerDocument: 10, // 每个文档最大建议数
    enableDuplicateFilter: true,
    enableQualityFilter: true
  },
  
  // 触发机制配置
  triggerConfig: {
    enableAutocompleteIntegration: true,
    lineEndTriggerAutocomplete: true,
    lineMiddleTriggerASTLSP: true,
    documentIdleTriggerModel: true,
    preventDuplicateTriggers: true,
    maxConcurrentAnalyses: 3
  }
}
```

### VSCode 配置集成

在 `package.json` 中添加配置项：

```json
{
  "contributes": {
    "configuration": {
      "title": "QAX Next Edit",
      "properties": {
        "qax-codegen.nextEdit.enabled": {
          "type": "boolean",
          "default": true,
          "description": "Enable QAX Next Edit suggestions"
        },
        "qax-codegen.nextEdit.modelRecommendation.enabled": {
          "type": "boolean",
          "default": true,
          "description": "Enable model-based recommendations"
        },
        "qax-codegen.nextEdit.modelRecommendation.debounceMs": {
          "type": "number",
          "default": 3000,
          "description": "Model recommendation debounce time in milliseconds"
        },
        "qax-codegen.nextEdit.astLspAnalysis.enabled": {
          "type": "boolean",
          "default": true,
          "description": "Enable AST+LSP analysis"
        },
        "qax-codegen.nextEdit.astLspAnalysis.debounceMs": {
          "type": "number",
          "default": 1000,
          "description": "AST+LSP analysis debounce time in milliseconds"
        }
      }
    }
  }
}
```

## 架构设计

### 核心组件

1. **NextEditManager** - 主管理器，协调所有子组件
2. **SuggestionManager** - 建议管理器，维护建议数组
3. **TriggerManager** - 触发管理器，控制分析触发时机
4. **DiffGenerator** - 差异生成器，生成完整的文档差异
5. **AnalysisEngine** - 分析引擎（待实现）
6. **ASTLSPAnalyzer** - AST+LSP分析器（待实现）
7. **ModelAnalyzer** - 模型分析器（待实现）

### 数据流

```
用户输入 → 触发条件检查 → 防抖处理 → 分析引擎 → 建议生成 → 建议过滤 → 建议显示
```

### 触发机制

```
输入位置检测:
├── 行末 → 触发 Autocomplete
├── 行中间 → 1秒防抖 → AST+LSP分析
└── 文档空闲3秒 + 无未执行建议 → 模型推荐
```

## 开发指南

### 运行测试

```bash
# 运行单元测试
npm test

# 运行特定测试文件
npm test -- --grep "NextEditManager"
```

### 调试

```typescript
import { NextEditLogger } from './src/qax/services/nextedit'

// 设置日志级别
const logger = NextEditLogger.getInstance()
logger.setLogLevel('debug')

// 使用日志
logger.info('NextEdit initialized')
logger.debug('Analysis triggered', { documentUri, analysisType })
logger.error('Analysis failed', error)
```

### 性能监控

```typescript
import { NextEditPerformanceMonitor, measureTime } from './src/qax/services/nextedit'

// 记录性能指标
const monitor = NextEditPerformanceMonitor.getInstance()

// 测量函数执行时间
await measureTime(async () => {
  // 执行分析
}, 'analysis_duration')

// 获取性能统计
const stats = monitor.getMetricStats('analysis_duration')
console.log('Analysis performance:', stats)
```

## 扩展开发

### 自定义分析器

```typescript
import { AnalysisEngine, AnalysisContext, Suggestion } from './src/qax/services/nextedit'

class CustomAnalyzer {
  async analyze(context: AnalysisContext): Promise<Suggestion[]> {
    // 实现自定义分析逻辑
    return []
  }
}
```

### 自定义建议过滤器

```typescript
import { Suggestion, SuggestionManager } from './src/qax/services/nextedit'

class CustomSuggestionFilter {
  filter(suggestions: Suggestion[]): Suggestion[] {
    // 实现自定义过滤逻辑
    return suggestions.filter(suggestion => {
      // 自定义过滤条件
      return suggestion.confidence > 0.8
    })
  }
}
```

## 故障排除

### 常见问题

1. **建议不显示**
   - 检查配置是否启用
   - 检查文档语言是否支持
   - 查看控制台错误信息

2. **性能问题**
   - 调整防抖时间
   - 减少最大建议数量
   - 启用缓存优化

3. **与 Autocomplete 冲突**
   - 检查触发条件配置
   - 确保互斥控制正常工作

### 日志分析

```typescript
// 启用详细日志
const logger = NextEditLogger.getInstance()
logger.setLogLevel('debug')

// 查看性能指标
const monitor = NextEditPerformanceMonitor.getInstance()
console.log('All metrics:', monitor.getAllMetrics())
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 创建 Pull Request

## 许可证

MIT License
