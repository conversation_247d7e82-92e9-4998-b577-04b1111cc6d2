/**
 * QAX Next Edit - 模型分析器
 * 负责深度的AI模型分析，目标响应时间5秒
 */

import * as vscode from "vscode"
import { ModelAnalysisContext, AnalysisGoal, ProjectContext, CodebaseStats } from "../types/AnalysisContext"
import { Suggestion, SuggestionBuilder, SuggestionSource, SuggestionCategory, ImpactLevel } from "../types/Suggestion"
import { NextEditSettings } from "../types/NextEditSettings"
import { AutocompleteProviderAdapter } from "../adapters/AutocompleteProviderAdapter"

/**
 * 现有 API Handler 的接口定义
 */
interface ExistingApiHandler {
	createMessage(systemPrompt: string, messages: any[]): AsyncIterable<any>
	getModel(): { id: string; info: any }
}

/**
 * 模型API客户端 - 使用当前配置的 autocomplete provider 构造 apiHandler
 */
class ModelAPIClient {
	private settings: NextEditSettings
	private apiHandler: ExistingApiHandler | null = null

	constructor(settings: NextEditSettings) {
		this.settings = settings
		// 异步初始化 API Handler
		this.initializeApiHandler().catch((error) => {
			console.error("Failed to initialize API handler:", error)
		})
	}

	/**
	 * 初始化 API Handler - 使用当前配置的 autocomplete provider
	 */
	private async initializeApiHandler(): Promise<void> {
		try {
			// 尝试动态导入 AutocompleteConfigManager 和相关模块
			// 注意：这些导入在实际环境中才会成功
			let AutocompleteConfigManager: any = null
			let buildApiHandler: any = null

			try {
				const autocompleteModule = await import("../../../../services/autocomplete/AutocompleteConfigManager")
				AutocompleteConfigManager = autocompleteModule.AutocompleteConfigManager
			} catch (error) {
				console.warn("Failed to import AutocompleteConfigManager:", error.message)
			}

			try {
				const apiModule = await import("../../../../api")
				buildApiHandler = apiModule.buildApiHandler
			} catch (error) {
				console.warn("Failed to import buildApiHandler:", error.message)
			}

			// 如果导入失败，使用降级处理
			if (!AutocompleteConfigManager || !buildApiHandler) {
				console.warn("Required modules not available, using fallback configuration")
				this.createFallbackHandler()
				return
			}

			// 获取当前 autocomplete 配置
			const configManager = AutocompleteConfigManager.instance
			const autocompleteSettings = configManager.getSettings()

			console.log("Using autocomplete provider configuration:", {
				provider: autocompleteSettings.provider,
				modelId: autocompleteSettings.modelId,
				apiBaseUrl: autocompleteSettings.apiBaseUrl,
			})

			// 根据 autocomplete provider 配置构造 apiHandler
			if (autocompleteSettings.provider === "fim") {
				// FIM provider 不适用于分析任务，降级到默认配置
				console.warn("FIM provider not suitable for analysis, using fallback configuration")
				this.createFallbackHandler()
			} else if (autocompleteSettings.apiKey) {
				// 使用 autocomplete 的 OpenAI 兼容配置
				this.apiHandler = buildApiHandler(
					{
						actModeApiProvider: "openai",
						openAiApiKey: autocompleteSettings.apiKey,
						openAiBaseUrl: autocompleteSettings.apiBaseUrl || "https://aip.b.qianxin-inc.cn/v2",
						actModeOpenAiModelId: autocompleteSettings.modelId || "DeepSeek-V3-0324",
						requestTimeoutMs: autocompleteSettings.requestTimeoutMs || 30000,
					},
					"act" as any, // Mode.ACT
				)

				console.log("API Handler initialized with autocomplete provider configuration")
			} else {
				console.warn("No valid autocomplete API key found, using fallback configuration")
				this.createFallbackHandler()
			}
		} catch (error) {
			console.warn("Failed to initialize API handler with autocomplete config:", error.message)
			this.createFallbackHandler()
		}
	}

	/**
	 * 创建降级处理器
	 */
	private createFallbackHandler(): void {
		this.apiHandler = {
			createMessage: async function* (_systemPrompt: string, _messages: any[]) {
				// 模拟流式响应
				const response = `{
  "suggestions": [
    {
      "type": "modify",
      "description": "Change: Add error handling for potential null values",
      "location": {
        "anchor": "function processData",
        "position": "after"
      },
      "patch": {
        "new_content": "  if (!data) {\\n    throw new Error('Data is required');\\n  }"
      }
    }
  ]
}`

				// 分块返回响应
				const chunks = response.split(" ")
				for (const chunk of chunks) {
					yield { type: "text", text: chunk + " " }
					await new Promise((resolve) => setTimeout(resolve, 5))
				}
			},
			getModel: () => ({
				id: "fallback-model",
				info: { maxTokens: 4096 },
			}),
		}
	}

	/**
	 * 调用模型API - 使用英文提示词，控制输出格式
	 */
	async callModel(prompt: string): Promise<string> {
		if (!this.apiHandler) {
			throw new Error("API Handler not initialized")
		}

		let lastError: Error | null = null
		const retryAttempts = this.settings.modelRecommendation.retryAttempts

		for (let attempt = 0; attempt < retryAttempts; attempt++) {
			try {
				const response = await this.makeAPICall(prompt)
				return this.processResponse(response)
			} catch (error) {
				lastError = error as Error
				console.warn(`Model API call attempt ${attempt + 1} failed:`, error)

				if (attempt < retryAttempts - 1) {
					// 指数退避
					const delay = Math.pow(2, attempt) * 1000
					await new Promise((resolve) => setTimeout(resolve, delay))
				}
			}
		}

		throw new Error(`Model API call failed after ${retryAttempts} attempts. Last error: ${lastError?.message}`)
	}

	/**
	 * 实际的API调用 - 集成现有的 apiHandler
	 */
	private async makeAPICall(prompt: string): Promise<string> {
		if (!this.apiHandler) {
			throw new Error("API Handler not available")
		}

		try {
			// 使用固定的英文系统提示词
			const systemPrompt = this.getSystemPrompt()

			// 构建消息格式
			const messages = [
				{
					role: "user",
					content: [{ type: "text", text: prompt }],
				},
			]

			// 调用现有的 API Handler
			const stream = this.apiHandler.createMessage(systemPrompt, messages)

			let response = ""
			for await (const chunk of stream) {
				if (chunk.type === "text") {
					response += chunk.text
				}
			}

			return response.trim()
		} catch (error) {
			console.error("API call error:", error)
			throw error
		}
	}

	/**
	 * 获取固定的英文系统提示词
	 */
	private getSystemPrompt(): string {
		return `You are a professional code analysis assistant specialized in providing code improvement suggestions.

Your task is to analyze code and provide specific, actionable suggestions in JSON format.

Focus on:
1. **Incomplete Logic**: Identify incomplete logic, missing edge case handling
2. **Unimplemented Functions**: Find declared but unimplemented functions, methods, or interfaces
3. **Unused Variables**: Detect defined but unused variables, constants, or imports
4. **Formatting Issues**: Discover inconsistent code formatting, indentation errors
5. **Lint Issue Fixes**: Provide specific fixes for detected lint problems

IMPORTANT: Always respond with ONLY valid JSON in the following format and NOTHING ELSE:

{
  "suggestions": [
    {
      "type": "add|modify|delete",
      "description": "Change: xxx -> yyy | Change: Add xxx | Change: Del xxx",
      "location": {
        "anchor": "unique code pattern to locate the position",
        "position": "before|after|replace"
      },
      "patch": {
        "new_content": "new content to insert/replace with"
      }
    }
  ]
}

Do not include any explanations, markdown formatting, or additional text outside the JSON.`
	}

	/**
	 * 处理响应 - 确保只返回需要的结果
	 */
	private processResponse(response: string): string {
		try {
			// 尝试提取JSON部分
			const jsonMatch = response.match(/\{[\s\S]*\}/)
			if (jsonMatch) {
				const jsonStr = jsonMatch[0]
				// 验证JSON格式
				JSON.parse(jsonStr)
				return jsonStr
			}

			// 如果没有找到JSON，返回原始响应
			return response
		} catch (error) {
			console.warn("Failed to process response as JSON:", error)
			return response
		}
	}

	/**
	 * 更新配置
	 */
	updateSettings(settings: NextEditSettings): void {
		this.settings = settings
		this.initializeApiHandler()
	}
}

/**
 * 分析结果缓存
 */
interface AnalysisCacheEntry {
	/** 上下文哈希 */
	contextHash: string
	/** 分析结果 */
	suggestions: Suggestion[]
	/** 创建时间 */
	createdAt: number
	/** 命中次数 */
	hitCount: number
}

/**
 * 模型分析器
 */
export class ModelAnalyzer {
	private settings: NextEditSettings
	private apiClient: ModelAPIClient
	private analysisCache = new Map<string, AnalysisCacheEntry>()
	private autocompleteAdapter: AutocompleteProviderAdapter

	constructor(settings: NextEditSettings) {
		this.settings = settings
		this.apiClient = new ModelAPIClient(settings)
		this.autocompleteAdapter = new AutocompleteProviderAdapter()
	}

	/**
	 * 更新配置
	 */
	updateSettings(settings: NextEditSettings): void {
		this.settings = settings
		this.apiClient.updateSettings(settings)
	}

	/**
	 * 释放资源
	 */
	dispose(): void {
		this.clearCache()
		this.autocompleteAdapter.dispose()
	}

	/**
	 * 执行深度分析
	 */
	async performDeepAnalysis(context: ModelAnalysisContext): Promise<Suggestion[]> {
		const startTime = performance.now()

		try {
			// 1. 构建分析上下文（目标：500ms）
			const enrichedContext = await this.buildRichContext(context)

			// 2. 检查缓存
			const contextHash = this.calculateContextHash(enrichedContext)
			const cached = this.analysisCache.get(contextHash)

			if (cached && this.isCacheValid(cached)) {
				cached.hitCount++
				console.log(`Model analysis cache hit for ${context.document.uri.toString()}`)
				return cached.suggestions
			}

			// 3. 生成优化的提示词（目标：200ms）
			const prompt = await this.buildOptimizedPrompt(enrichedContext)

			// 4. 调用模型API（目标：4000ms）
			const response = await this.apiClient.callModel(prompt)

			// 5. 解析和验证响应（目标：300ms）
			const suggestions = await this.parseAndValidateResponse(response, context)

			// 6. 更新缓存
			this.updateCache(contextHash, suggestions)

			const duration = performance.now() - startTime
			console.log(`Model analysis completed in ${duration.toFixed(2)}ms`)

			return suggestions
		} catch (error) {
			console.error("Model analysis error:", error)
			return []
		}
	}

	/**
	 * 构建丰富的分析上下文
	 */
	async buildRichContext(context: ModelAnalysisContext): Promise<ModelAnalysisContext> {
		const enrichedContext = { ...context }

		try {
			// 收集项目上下文
			if (!enrichedContext.projectContext || Object.keys(enrichedContext.projectContext).length === 0) {
				enrichedContext.projectContext = await this.collectProjectContext(context.document)
			}

			// 收集代码库统计
			if (!enrichedContext.codebaseStats || Object.keys(enrichedContext.codebaseStats).length === 0) {
				enrichedContext.codebaseStats = await this.collectCodebaseStats()
			}

			// 限制上下文大小
			enrichedContext.currentContent = this.limitContentSize(
				enrichedContext.currentContent,
				this.settings.modelRecommendation.maxContextLines,
			)
		} catch (error) {
			console.error("Error building rich context:", error)
		}

		return enrichedContext
	}

	/**
	 * 生成针对性提示词 - 集成 AutocompleteProvider 上下文
	 */
	async buildOptimizedPrompt(context: ModelAnalysisContext): Promise<string> {
		const template = this.loadPromptTemplate()

		// 收集当前文件的 lint 问题
		const lintIssues = await this.collectLintIssues(context.document)

		// 使用 AutocompleteProvider 适配器增强上下文
		let enhancedCurrentContent = context.currentContent
		try {
			// 构建代码上下文
			const codeContext = await this.autocompleteAdapter.buildCodeContext(
				context.document,
				new vscode.Position(0, 0), // 使用文档开始位置作为默认
			)

			// 如果有更丰富的上下文，使用它
			if (codeContext.contextCode && codeContext.contextCode.length > context.currentContent.length) {
				enhancedCurrentContent = codeContext.contextCode
			}

			console.log(
				`Enhanced context using AutocompleteProvider: strategy=${codeContext.strategy}, usedFullFile=${codeContext.usedFullFile}`,
			)
		} catch (error) {
			console.warn("Failed to enhance context with AutocompleteProvider:", error)
		}

		return template
			.replace("{{LANGUAGE}}", context.document.languageId)
			.replace("{{CURRENT_FILE}}", enhancedCurrentContent)
			.replace("{{RECENT_CHANGES}}", this.formatRecentChanges(context.recentChanges))
			.replace("{{RELATED_FILES}}", this.formatRelatedFiles(context.relatedFiles))
			.replace("{{PROJECT_CONTEXT}}", this.formatProjectContext(context.projectContext))
			.replace("{{ANALYSIS_GOALS}}", this.formatAnalysisGoals(context.analysisGoals))
			.replace("{{LINT_ISSUES}}", this.formatLintIssues(lintIssues))
	}

	/**
	 * 解析和验证模型响应
	 */
	async parseAndValidateResponse(response: string, context: ModelAnalysisContext): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		try {
			// 尝试解析JSON响应
			const parsed = this.extractJSONFromResponse(response)

			if (parsed && parsed.suggestions && Array.isArray(parsed.suggestions)) {
				for (const suggestionData of parsed.suggestions) {
					const suggestion = this.validateAndCreateSuggestion(suggestionData, context)
					if (suggestion) {
						suggestions.push(suggestion)
					}
				}
			}
		} catch (error) {
			console.error("Error parsing model response:", error)
		}

		return suggestions
	}

	/**
	 * 清理缓存
	 */
	clearCache(): void {
		this.analysisCache.clear()
	}

	/**
	 * 获取缓存统计
	 */
	getCacheStats(): { size: number; hitRate: number; totalHits: number } {
		const entries = Array.from(this.analysisCache.values())
		const totalHits = entries.reduce((sum, entry) => sum + entry.hitCount, 0)
		const totalRequests = entries.length + totalHits

		return {
			size: this.analysisCache.size,
			hitRate: totalRequests > 0 ? totalHits / totalRequests : 0,
			totalHits,
		}
	}

	// 私有方法

	/**
	 * 收集当前文件的 lint 问题
	 */
	private async collectLintIssues(document: vscode.TextDocument): Promise<vscode.Diagnostic[]> {
		try {
			// 获取当前文档的所有诊断信息
			const diagnostics = vscode.languages.getDiagnostics(document.uri)

			// 过滤出 lint 相关的问题
			const lintIssues = diagnostics.filter((diagnostic) => {
				// 根据诊断来源过滤 lint 问题
				const source = diagnostic.source?.toLowerCase() || ""

				// 常见的 lint 工具来源
				const lintSources = [
					"eslint",
					"tslint",
					"pylint",
					"flake8",
					"mypy",
					"rustc",
					"clippy",
					"golint",
					"gofmt",
					"cppcheck",
					"clang-tidy",
					"sonarjs",
					"jshint",
					"jscs",
					"standardjs",
					"prettier",
					"stylelint",
					"phpcs",
					"psalm",
					"phpstan",
				]

				// 检查是否是 lint 工具的诊断
				const isLintSource = lintSources.some((lintSource) => source.includes(lintSource))

				// 或者根据严重程度过滤（Warning 和 Error 通常是 lint 问题）
				const isLintSeverity =
					diagnostic.severity === vscode.DiagnosticSeverity.Warning ||
					diagnostic.severity === vscode.DiagnosticSeverity.Error

				return isLintSource || isLintSeverity
			})

			// 按行号排序
			lintIssues.sort((a, b) => a.range.start.line - b.range.start.line)

			console.log(`Collected ${lintIssues.length} lint issues for ${document.uri.toString()}`)

			return lintIssues
		} catch (error) {
			console.error("Error collecting lint issues:", error)
			return []
		}
	}

	/**
	 * 格式化 lint 问题为提示词文本
	 */
	private formatLintIssues(lintIssues: vscode.Diagnostic[]): string {
		if (!lintIssues || lintIssues.length === 0) {
			return "无检测到的 lint 问题"
		}

		const formattedIssues = lintIssues.map((diagnostic, index) => {
			const line = diagnostic.range.start.line + 1 // 转换为1基索引
			const column = diagnostic.range.start.character + 1
			const severity = this.getSeverityText(diagnostic.severity)
			const source = diagnostic.source || "未知来源"
			const code = diagnostic.code ? ` (${diagnostic.code})` : ""

			return `${index + 1}. **第${line}行:${column}列** [${severity}] ${source}${code}
   问题: ${diagnostic.message}
   范围: 第${diagnostic.range.start.line + 1}-${diagnostic.range.end.line + 1}行`
		})

		return `检测到 ${lintIssues.length} 个 lint 问题：

${formattedIssues.join("\n\n")}`
	}

	/**
	 * 获取诊断严重程度的文本描述
	 */
	private getSeverityText(severity: vscode.DiagnosticSeverity | undefined): string {
		switch (severity) {
			case vscode.DiagnosticSeverity.Error:
				return "错误"
			case vscode.DiagnosticSeverity.Warning:
				return "警告"
			case vscode.DiagnosticSeverity.Information:
				return "信息"
			case vscode.DiagnosticSeverity.Hint:
				return "提示"
			default:
				return "未知"
		}
	}

	/**
	 * 加载提示词模板
	 */
	private loadPromptTemplate(): string {
		return `Please analyze the following code and provide specific modification suggestions.

## Analysis Goals
Focus on these aspects:
1. **Incomplete Logic**: Identify incomplete logic, missing edge case handling
2. **Unimplemented Functions**: Find declared but unimplemented functions, methods, or interfaces
3. **Unused Variables**: Detect defined but unused variables, constants, or imports
4. **Formatting Issues**: Discover inconsistent code formatting, indentation errors
5. **Lint Issue Fixes**: Provide specific fixes for VS Code detected lint problems listed below

## Current File Content
\`\`\`{{LANGUAGE}}
{{CURRENT_FILE}}
\`\`\`

## Recent Changes
{{RECENT_CHANGES}}

## Related Files
{{RELATED_FILES}}

## Project Context
{{PROJECT_CONTEXT}}

## VS Code Detected Lint Issues
{{LINT_ISSUES}}

Provide your analysis in the required JSON format.`
	}

	/**
	 * 收集项目上下文
	 */
	private async collectProjectContext(document: vscode.TextDocument): Promise<ProjectContext> {
		const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri)

		const context: ProjectContext = {
			packageInfo: { name: "", version: "" },
			dependencies: [],
			buildConfig: { tool: "" },
			testFiles: [],
			documentationFiles: [],
		}

		if (!workspaceFolder) {
			return context
		}

		try {
			// 查找package.json
			const packageJsonFiles = await vscode.workspace.findFiles(
				new vscode.RelativePattern(workspaceFolder, "package.json"),
				null,
				1,
			)

			if (packageJsonFiles.length > 0) {
				const packageJson = await vscode.workspace.fs.readFile(packageJsonFiles[0])
				const packageData = JSON.parse(packageJson.toString())

				context.packageInfo = {
					name: packageData.name || "",
					version: packageData.version || "",
					description: packageData.description,
					main: packageData.main,
					scripts: packageData.scripts,
				}

				// 提取依赖信息
				const deps = packageData.dependencies || {}
				const devDeps = packageData.devDependencies || {}

				for (const [name, version] of Object.entries(deps)) {
					context.dependencies.push({ name, version: version as string, type: "dependency" })
				}

				for (const [name, version] of Object.entries(devDeps)) {
					context.dependencies.push({ name, version: version as string, type: "devDependency" })
				}
			}

			// 查找测试文件
			const testFiles = await vscode.workspace.findFiles(
				new vscode.RelativePattern(workspaceFolder, "**/*.{test,spec}.{ts,js,tsx,jsx,py}"),
				null,
				20,
			)
			context.testFiles = testFiles.map((file) => file.toString())
		} catch (error) {
			console.error("Error collecting project context:", error)
		}

		return context
	}

	/**
	 * 收集代码库统计
	 */
	private async collectCodebaseStats(): Promise<CodebaseStats> {
		const stats: CodebaseStats = {
			totalFiles: 0,
			totalLines: 0,
			languageDistribution: {},
			complexityMetrics: {
				averageCyclomaticComplexity: 0,
				maxFunctionLength: 0,
				averageFunctionLength: 0,
			},
			qualityMetrics: {
				testCoverage: 0,
				duplicationRate: 0,
				technicalDebtIndex: 0,
			},
		}

		try {
			// 统计文件数量和语言分布
			const allFiles = await vscode.workspace.findFiles("**/*.{ts,js,tsx,jsx,py,rs,go,cpp,c,cs,java,php}", null, 1000)
			stats.totalFiles = allFiles.length

			for (const file of allFiles) {
				const ext = file.fsPath.split(".").pop()?.toLowerCase() || "unknown"
				stats.languageDistribution[ext] = (stats.languageDistribution[ext] || 0) + 1
			}
		} catch (error) {
			console.error("Error collecting codebase stats:", error)
		}

		return stats
	}

	/**
	 * 限制内容大小
	 */
	private limitContentSize(content: string, maxLines: number): string {
		const lines = content.split("\n")
		if (lines.length <= maxLines) {
			return content
		}

		// 保留前面和后面的行，中间用省略号
		const halfLines = Math.floor(maxLines / 2)
		const startLines = lines.slice(0, halfLines)
		const endLines = lines.slice(-halfLines)

		return [...startLines, `... (${lines.length - maxLines} lines omitted) ...`, ...endLines].join("\n")
	}

	/**
	 * 计算上下文哈希
	 */
	private calculateContextHash(context: ModelAnalysisContext): string {
		const hashInput = [
			context.currentContent,
			JSON.stringify(context.recentChanges),
			JSON.stringify(context.relatedFiles),
			JSON.stringify(context.analysisGoals),
		].join("|")

		// 简单哈希算法
		let hash = 0
		for (let i = 0; i < hashInput.length; i++) {
			const char = hashInput.charCodeAt(i)
			hash = (hash << 5) - hash + char
			hash = hash & hash // 转换为32位整数
		}

		return hash.toString(36)
	}

	/**
	 * 检查缓存是否有效
	 */
	private isCacheValid(cached: AnalysisCacheEntry): boolean {
		const maxAge = 5 * 60 * 1000 // 5分钟
		return Date.now() - cached.createdAt < maxAge
	}

	/**
	 * 更新缓存
	 */
	private updateCache(contextHash: string, suggestions: Suggestion[]): void {
		this.analysisCache.set(contextHash, {
			contextHash,
			suggestions,
			createdAt: Date.now(),
			hitCount: 0,
		})

		// 限制缓存大小
		if (this.analysisCache.size > 100) {
			const oldestKey = Array.from(this.analysisCache.keys())[0]
			this.analysisCache.delete(oldestKey)
		}
	}

	/**
	 * 从响应中提取JSON
	 */
	private extractJSONFromResponse(response: string): any {
		try {
			// 尝试直接解析
			return JSON.parse(response)
		} catch {
			// 尝试从代码块中提取
			const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/)
			if (jsonMatch) {
				return JSON.parse(jsonMatch[1])
			}

			// 尝试查找JSON对象
			const objectMatch = response.match(/\{[\s\S]*\}/)
			if (objectMatch) {
				return JSON.parse(objectMatch[0])
			}

			return null
		}
	}

	/**
	 * 验证并创建建议
	 */
	private validateAndCreateSuggestion(data: any, _context: ModelAnalysisContext): Suggestion | null {
		try {
			// 验证必需字段
			if (!data.type || !data.description || !data.location || !data.patch) {
				return null
			}

			if (!data.location.anchor || !data.location.position || !data.patch.new_content) {
				return null
			}

			// 验证类型值
			if (!["add", "modify", "delete"].includes(data.type)) {
				return null
			}

			if (!["before", "after", "replace"].includes(data.location.position)) {
				return null
			}

			// 创建建议
			return SuggestionBuilder.create(
				data.type,
				data.description,
				data.location.anchor,
				data.location.position,
				data.patch.new_content,
				{
					confidence: 0.8, // 模型分析的默认置信度
					source: SuggestionSource.MODEL,
					category: this.inferCategory(data.description),
					impact: this.inferImpact(data.type),
					tags: ["model-analysis", "ai-suggestion"],
				},
			)
		} catch (error) {
			console.error("Error validating suggestion:", error)
			return null
		}
	}

	private formatRecentChanges(changes: any[]): string {
		if (!changes || changes.length === 0) {
			return "无最近变更"
		}

		return changes
			.slice(0, 5)
			.map((change) => `- ${change.filePath}: ${change.changeType} (${new Date(change.timestamp).toLocaleString()})`)
			.join("\n")
	}

	private formatRelatedFiles(files: string[]): string {
		if (!files || files.length === 0) {
			return "无相关文件"
		}

		return files
			.slice(0, 10)
			.map((file) => `- ${file}`)
			.join("\n")
	}

	private formatProjectContext(context: ProjectContext): string {
		return `项目：${context.packageInfo.name} v${context.packageInfo.version}\n依赖数量：${context.dependencies.length}`
	}

	private formatAnalysisGoals(goals: AnalysisGoal[]): string {
		return goals.map((goal) => `- ${goal}`).join("\n")
	}

	private inferCategory(description: string): SuggestionCategory {
		const desc = description.toLowerCase()

		if (desc.includes("function") || desc.includes("method")) {
			return SuggestionCategory.FUNCTION_IMPLEMENTATION
		}

		if (desc.includes("variable") || desc.includes("unused")) {
			return SuggestionCategory.VARIABLE_USAGE
		}

		if (desc.includes("format") || desc.includes("indent")) {
			return SuggestionCategory.CODE_FORMATTING
		}

		if (desc.includes("logic") || desc.includes("condition")) {
			return SuggestionCategory.LOGIC_COMPLETION
		}

		return SuggestionCategory.LINT_ISSUES
	}

	private inferImpact(type: string): ImpactLevel {
		switch (type) {
			case "delete":
				return ImpactLevel.HIGH
			case "modify":
				return ImpactLevel.MEDIUM
			case "add":
				return ImpactLevel.LOW
			default:
				return ImpactLevel.MEDIUM
		}
	}
}
