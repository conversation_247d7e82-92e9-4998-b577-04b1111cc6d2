/**
 * QAX Next Edit - 缓存管理器
 * 统一管理AST缓存和LSP缓存，实现LRU淘汰策略和内存限制
 */

import * as vscode from "vscode"
import { ASTNode } from "../types/AnalysisContext"
import { NextEditSettings } from "../types/NextEditSettings"

/**
 * 缓存条目基类
 */
interface CacheEntry {
	/** 缓存键 */
	key: string
	/** 创建时间 */
	createdAt: number
	/** 最后访问时间 */
	lastAccessedAt: number
	/** 访问次数 */
	accessCount: number
	/** 数据大小（字节） */
	size: number
}

/**
 * AST缓存条目
 */
export interface ASTCacheEntry extends CacheEntry {
	/** AST根节点 */
	ast: ASTNode
	/** 文档版本 */
	documentVersion: number
	/** 文档URI */
	documentUri: string
	/** 语言类型 */
	languageId: string
}

/**
 * LSP缓存条目
 */
export interface LSPCacheEntry extends CacheEntry {
	/** 符号引用映射 */
	references: Map<string, vscode.Location[]>
	/** 符号定义映射 */
	definitions: Map<string, vscode.Location>
	/** 诊断信息 */
	diagnostics: vscode.Diagnostic[]
	/** 文档版本 */
	documentVersion: number
	/** 文档URI */
	documentUri: string
}

/**
 * LRU缓存实现
 */
class LRUCache<T extends CacheEntry> {
	private cache = new Map<string, T>()
	private maxSize: number
	private maxMemoryMB: number
	private currentMemoryBytes = 0

	constructor(maxSize: number, maxMemoryMB: number) {
		this.maxSize = maxSize
		this.maxMemoryMB = maxMemoryMB
	}

	/**
	 * 获取缓存项
	 */
	get(key: string): T | undefined {
		const entry = this.cache.get(key)
		if (entry) {
			// 更新访问时间和次数
			entry.lastAccessedAt = Date.now()
			entry.accessCount++

			// 移到最后（最近使用）
			this.cache.delete(key)
			this.cache.set(key, entry)
		}
		return entry
	}

	/**
	 * 设置缓存项
	 */
	set(key: string, value: T): void {
		// 如果已存在，先删除旧的
		if (this.cache.has(key)) {
			const oldEntry = this.cache.get(key)!
			this.currentMemoryBytes -= oldEntry.size
			this.cache.delete(key)
		}

		// 检查内存限制
		const maxMemoryBytes = this.maxMemoryMB * 1024 * 1024
		while (this.currentMemoryBytes + value.size > maxMemoryBytes && this.cache.size > 0) {
			this.evictLRU()
		}

		// 检查数量限制
		while (this.cache.size >= this.maxSize && this.cache.size > 0) {
			this.evictLRU()
		}

		// 添加新项
		value.lastAccessedAt = Date.now()
		this.cache.set(key, value)
		this.currentMemoryBytes += value.size
	}

	/**
	 * 删除缓存项
	 */
	delete(key: string): boolean {
		const entry = this.cache.get(key)
		if (entry) {
			this.currentMemoryBytes -= entry.size
			return this.cache.delete(key)
		}
		return false
	}

	/**
	 * 清空缓存
	 */
	clear(): void {
		this.cache.clear()
		this.currentMemoryBytes = 0
	}

	/**
	 * 获取缓存大小
	 */
	size(): number {
		return this.cache.size
	}

	/**
	 * 获取内存使用量（MB）
	 */
	getMemoryUsageMB(): number {
		return this.currentMemoryBytes / (1024 * 1024)
	}

	/**
	 * 获取所有键
	 */
	keys(): string[] {
		return Array.from(this.cache.keys())
	}

	/**
	 * 获取所有值
	 */
	values(): T[] {
		return Array.from(this.cache.values())
	}

	/**
	 * 获取所有键值对
	 */
	getAll(): [string, T][] {
		return Array.from(this.cache.entries())
	}

	/**
	 * 获取缓存统计
	 */
	getStats(): {
		size: number
		memoryUsageMB: number
		hitRate: number
		totalAccesses: number
	} {
		const entries = Array.from(this.cache.values())
		const totalAccesses = entries.reduce((sum, entry) => sum + entry.accessCount, 0)
		const hits = entries.filter((entry) => entry.accessCount > 1).length

		return {
			size: this.cache.size,
			memoryUsageMB: this.getMemoryUsageMB(),
			hitRate: totalAccesses > 0 ? hits / totalAccesses : 0,
			totalAccesses,
		}
	}

	/**
	 * 淘汰最近最少使用的项
	 */
	private evictLRU(): void {
		if (this.cache.size === 0) {
			return
		}

		// 找到最久未访问的项
		let oldestKey: string | null = null
		let oldestTime = Date.now()

		for (const [key, entry] of Array.from(this.cache.entries())) {
			if (entry.lastAccessedAt < oldestTime) {
				oldestTime = entry.lastAccessedAt
				oldestKey = key
			}
		}

		if (oldestKey) {
			this.delete(oldestKey)
		}
	}
}

/**
 * 缓存管理器
 */
export class CacheManager {
	private settings: NextEditSettings
	private astCache: LRUCache<ASTCacheEntry>
	private lspCache: LRUCache<LSPCacheEntry>
	private cleanupInterval: NodeJS.Timeout | null = null

	constructor(settings: NextEditSettings) {
		this.settings = settings

		// 初始化缓存
		const maxCacheSizeMB = settings.astLspAnalysis.maxCacheSize
		this.astCache = new LRUCache<ASTCacheEntry>(100, maxCacheSizeMB / 2) // AST缓存占一半
		this.lspCache = new LRUCache<LSPCacheEntry>(200, maxCacheSizeMB / 2) // LSP缓存占一半

		// 启动定期清理（在测试环境中跳过）
		if (!this.isTestEnvironment()) {
			this.startPeriodicCleanup()
		}
	}

	/**
	 * 更新配置
	 */
	updateSettings(settings: NextEditSettings): void {
		const oldSettings = this.settings
		this.settings = settings

		// 根据新配置调整缓存大小
		if (oldSettings.astLspAnalysis.maxCacheSize !== settings.astLspAnalysis.maxCacheSize) {
			const newMaxMemoryBytes = settings.astLspAnalysis.maxCacheSize * 1024 * 1024
			console.log(`🔧 Cache memory limit updated: ${settings.astLspAnalysis.maxCacheSize}MB`)

			// 如果新的内存限制更小，立即清理缓存
			if (newMaxMemoryBytes < oldSettings.astLspAnalysis.maxCacheSize * 1024 * 1024) {
				this.enforceMemoryLimit()
			}
		}

		// 更新缓存启用状态
		if (oldSettings.astLspAnalysis.enableIncrementalCache !== settings.astLspAnalysis.enableIncrementalCache) {
			if (!settings.astLspAnalysis.enableIncrementalCache) {
				this.astCache.clear()
				this.lspCache.clear()
				console.log("🔧 Incremental cache disabled and cleared")
			} else {
				console.log("🔧 Incremental cache enabled")
			}
		}

		// 更新缓存过期时间
		if (oldSettings.astLspAnalysis.cacheExpirationMs !== settings.astLspAnalysis.cacheExpirationMs) {
			console.log(`🔧 Cache expiration updated: ${settings.astLspAnalysis.cacheExpirationMs}ms`)
			// 清理过期的缓存项
			this.cleanup()
		}
	}

	/**
	 * 获取AST缓存
	 */
	getAST(documentUri: string): ASTCacheEntry | undefined {
		return this.astCache.get(documentUri)
	}

	/**
	 * 设置AST缓存
	 */
	setAST(documentUri: string, ast: ASTNode, documentVersion: number, languageId: string): void {
		const entry: ASTCacheEntry = {
			key: documentUri,
			ast,
			documentVersion,
			documentUri,
			languageId,
			createdAt: Date.now(),
			lastAccessedAt: Date.now(),
			accessCount: 1,
			size: this.estimateASTSize(ast),
		}

		this.astCache.set(documentUri, entry)
	}

	/**
	 * 获取LSP缓存
	 */
	getLSP(documentUri: string): LSPCacheEntry | undefined {
		return this.lspCache.get(documentUri)
	}

	/**
	 * 设置LSP缓存
	 */
	setLSP(
		documentUri: string,
		references: Map<string, vscode.Location[]>,
		definitions: Map<string, vscode.Location>,
		diagnostics: vscode.Diagnostic[],
		documentVersion: number,
	): void {
		const entry: LSPCacheEntry = {
			key: documentUri,
			references,
			definitions,
			diagnostics,
			documentVersion,
			documentUri,
			createdAt: Date.now(),
			lastAccessedAt: Date.now(),
			accessCount: 1,
			size: this.estimateLSPSize(references, definitions, diagnostics),
		}

		this.lspCache.set(documentUri, entry)
	}

	/**
	 * 删除文档缓存
	 */
	deleteDocument(documentUri: string): void {
		this.astCache.delete(documentUri)
		this.lspCache.delete(documentUri)
	}

	/**
	 * 清空所有缓存
	 */
	clearAll(): void {
		this.astCache.clear()
		this.lspCache.clear()
	}

	/**
	 * 检查AST缓存是否有效
	 */
	isASTCacheValid(documentUri: string, currentVersion: number): boolean {
		const cached = this.astCache.get(documentUri)
		if (!cached) {
			return false
		}

		// 检查版本是否匹配
		if (cached.documentVersion !== currentVersion) {
			return false
		}

		// 检查是否过期
		const maxAge = this.settings.astLspAnalysis.cacheExpirationMs
		const age = Date.now() - cached.createdAt
		return age < maxAge
	}

	/**
	 * 检查LSP缓存是否有效
	 */
	isLSPCacheValid(documentUri: string, currentVersion: number): boolean {
		const cached = this.lspCache.get(documentUri)
		if (!cached) {
			return false
		}

		// 检查版本是否匹配
		if (cached.documentVersion !== currentVersion) {
			return false
		}

		// 检查是否过期
		const maxAge = this.settings.astLspAnalysis.cacheExpirationMs
		const age = Date.now() - cached.createdAt
		return age < maxAge
	}

	/**
	 * 获取缓存统计信息
	 */
	getCacheStats(): {
		ast: ReturnType<LRUCache<ASTCacheEntry>["getStats"]>
		lsp: ReturnType<LRUCache<LSPCacheEntry>["getStats"]>
		totalMemoryMB: number
	} {
		const astStats = this.astCache.getStats()
		const lspStats = this.lspCache.getStats()

		return {
			ast: astStats,
			lsp: lspStats,
			totalMemoryMB: astStats.memoryUsageMB + lspStats.memoryUsageMB,
		}
	}

	/**
	 * 执行缓存清理
	 */
	performCleanup(): void {
		const now = Date.now()
		const maxAge = this.settings.astLspAnalysis.cacheExpirationMs

		// 清理过期的AST缓存
		for (const key of this.astCache.keys()) {
			const entry = this.astCache.get(key)
			if (entry && now - entry.createdAt > maxAge) {
				this.astCache.delete(key)
			}
		}

		// 清理过期的LSP缓存
		for (const key of this.lspCache.keys()) {
			const entry = this.lspCache.get(key)
			if (entry && now - entry.createdAt > maxAge) {
				this.lspCache.delete(key)
			}
		}
	}

	/**
	 * 销毁缓存管理器
	 */
	dispose(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval)
			this.cleanupInterval = null
		}

		this.clearAll()
	}

	// 私有方法

	/**
	 * 检测是否在测试环境中
	 */
	private isTestEnvironment(): boolean {
		return (
			process.env.NODE_ENV === "test" || process.env.JEST_WORKER_ID !== undefined || typeof global.describe === "function"
		)
	}

	/**
	 * 启动定期清理
	 */
	private startPeriodicCleanup(): void {
		// 每5分钟清理一次过期缓存
		this.cleanupInterval = setInterval(
			() => {
				this.performCleanup()
			},
			5 * 60 * 1000,
		)
	}

	/**
	 * 估算AST大小
	 */
	private estimateASTSize(ast: ASTNode): number {
		// 简单估算：每个节点约100字节
		return this.countASTNodes(ast) * 100
	}

	/**
	 * 计算AST节点数量
	 */
	private countASTNodes(node: ASTNode): number {
		let count = 1 // 当前节点

		for (const child of node.children || []) {
			count += this.countASTNodes(child)
		}

		return count
	}

	/**
	 * 估算LSP缓存大小
	 */
	private estimateLSPSize(
		references: Map<string, vscode.Location[]>,
		definitions: Map<string, vscode.Location>,
		diagnostics: vscode.Diagnostic[],
	): number {
		let size = 0

		// 引用大小
		for (const [symbol, locations] of Array.from(references.entries())) {
			size += symbol.length * 2 // 字符串大小
			size += locations.length * 200 // 每个位置约200字节
		}

		// 定义大小
		for (const [symbol, _location] of Array.from(definitions.entries())) {
			size += symbol.length * 2
			size += 200 // 位置大小
		}

		// 诊断大小
		size += diagnostics.length * 300 // 每个诊断约300字节

		return size
	}

	/**
	 * 清理过期的缓存项
	 */
	cleanup(): void {
		const now = Date.now()
		const maxAge = this.settings.astLspAnalysis.cacheExpirationMs

		// 清理过期的 AST 缓存
		const astKeysToDelete: string[] = []
		for (const [key, entry] of this.astCache.getAll()) {
			if (now - entry.lastAccessedAt > maxAge) {
				astKeysToDelete.push(key)
			}
		}

		for (const key of astKeysToDelete) {
			this.astCache.delete(key)
		}

		// 清理过期的 LSP 缓存
		const lspKeysToDelete: string[] = []
		for (const [key, entry] of this.lspCache.getAll()) {
			if (now - entry.lastAccessedAt > maxAge) {
				lspKeysToDelete.push(key)
			}
		}

		for (const key of lspKeysToDelete) {
			this.lspCache.delete(key)
		}

		if (astKeysToDelete.length > 0 || lspKeysToDelete.length > 0) {
			console.log(`🧹 Cleaned up ${astKeysToDelete.length} AST and ${lspKeysToDelete.length} LSP cache entries`)
		}
	}

	/**
	 * 强制执行内存限制
	 */
	private enforceMemoryLimit(): void {
		const maxMemoryBytes = this.settings.astLspAnalysis.maxCacheSize * 1024 * 1024

		// 获取当前内存使用
		let currentMemory = 0
		for (const [, entry] of this.astCache.getAll()) {
			currentMemory += entry.size
		}
		for (const [, entry] of this.lspCache.getAll()) {
			currentMemory += entry.size
		}

		// 如果超过限制，清理最久未使用的项
		while (currentMemory > maxMemoryBytes) {
			// 找到最久未使用的缓存项
			let oldestKey: string | null = null
			let oldestTime = Date.now()
			let oldestCache: "ast" | "lsp" | null = null

			// 检查 AST 缓存
			for (const [key, entry] of this.astCache.getAll()) {
				if (entry.lastAccessedAt < oldestTime) {
					oldestTime = entry.lastAccessedAt
					oldestKey = key
					oldestCache = "ast"
				}
			}

			// 检查 LSP 缓存
			for (const [key, entry] of this.lspCache.getAll()) {
				if (entry.lastAccessedAt < oldestTime) {
					oldestTime = entry.lastAccessedAt
					oldestKey = key
					oldestCache = "lsp"
				}
			}

			// 删除最久未使用的项
			if (oldestKey && oldestCache) {
				if (oldestCache === "ast") {
					const entry = this.astCache.get(oldestKey)
					if (entry) {
						currentMemory -= entry.size
						this.astCache.delete(oldestKey)
					}
				} else {
					const entry = this.lspCache.get(oldestKey)
					if (entry) {
						currentMemory -= entry.size
						this.lspCache.delete(oldestKey)
					}
				}
			} else {
				break // 没有更多项可删除
			}
		}
	}
}
