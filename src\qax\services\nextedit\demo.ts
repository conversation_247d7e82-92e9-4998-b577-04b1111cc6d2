/**
 * QAX Next Edit - 演示文件
 * 展示如何使用 QAX Next Edit 系统
 */

import * as vscode from "vscode"
import {
	createNextEditManager,
	NextEditManager,
	SuggestionBuilder,
	SuggestionType,
	SuggestionSource,
	SuggestionCategory,
	ImpactLevel,
	DEFAULT_NEXT_EDIT_SETTINGS,
	NextEditLogger,
	NextEditPerformanceMonitor,
	measureTime,
} from "./index"

/**
 * 演示基本使用
 */
export async function demoBasicUsage() {
	console.log("=== QAX Next Edit 基本使用演示 ===")

	// 1. 创建管理器实例
	const manager = createNextEditManager({
		enabled: true,
		modelRecommendation: {
			...DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation,
			enabled: true,
			debounceMs: 2000, // 缩短演示时间
			idleTimeMs: 2000,
		},
		astLspAnalysis: {
			...DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis,
			enabled: true,
			debounceMs: 500,
			responseTimeTarget: 100,
		},
	})

	// 2. 设置事件监听器
	manager.onSuggestionGenerated((suggestions) => {
		console.log(`✅ 生成了 ${suggestions.length} 个建议`)
		suggestions.forEach((suggestion, index) => {
			console.log(`   ${index + 1}. ${suggestion.description}`)
		})
	})

	manager.onAnalysisCompleted((documentUri, duration) => {
		console.log(`⚡ 分析完成: ${documentUri} (耗时: ${duration}ms)`)
	})

	manager.onError((error) => {
		console.error(`❌ 错误: ${error.message}`)
	})

	// 3. 模拟初始化（在实际使用中，这会在扩展激活时调用）
	console.log("📦 初始化管理器...")
	// await manager.initialize(context) // 在实际环境中使用

	console.log("✅ 基本使用演示完成")
	return manager
}

/**
 * 演示建议创建和管理
 */
export async function demoSuggestionManagement() {
	console.log("\n=== 建议管理演示 ===")

	// 1. 创建各种类型的建议
	const suggestions = [
		// 添加类型的建议
		SuggestionBuilder.create(
			"add",
			"error handling for API call",
			'fetch("/api/data")',
			"after",
			'try {\n  const response = await fetch("/api/data");\n  if (!response.ok) throw new Error("API call failed");\n} catch (error) {\n  console.error("Error:", error);\n}',
			{
				confidence: 0.9,
				source: SuggestionSource.MODEL,
				category: SuggestionCategory.LOGIC_COMPLETION,
				impact: ImpactLevel.HIGH,
				tags: ["error-handling", "api", "best-practice"],
			},
		),

		// 修改类型的建议
		SuggestionBuilder.create(
			"modify",
			"var -> const for immutable variable",
			'var userName = "john";',
			"replace",
			'const userName = "john";',
			{
				confidence: 0.95,
				source: SuggestionSource.AST_LSP,
				category: SuggestionCategory.CODE_FORMATTING,
				impact: ImpactLevel.LOW,
				tags: ["es6", "const", "best-practice"],
			},
		),

		// 删除类型的建议
		SuggestionBuilder.create(
			"delete",
			"unused import statement",
			'import { unusedFunction } from "./utils";',
			"replace",
			"",
			{
				confidence: 0.8,
				source: SuggestionSource.AST_LSP,
				category: SuggestionCategory.VARIABLE_USAGE,
				impact: ImpactLevel.LOW,
				tags: ["cleanup", "imports"],
			},
		),
	]

	console.log(`📝 创建了 ${suggestions.length} 个建议:`)
	suggestions.forEach((suggestion, index) => {
		console.log(`   ${index + 1}. [${suggestion.type.toUpperCase()}] ${suggestion.description}`)
		console.log(`      置信度: ${suggestion.confidence}, 来源: ${suggestion.source}`)
		console.log(`      分类: ${suggestion.category}, 影响: ${suggestion.impact}`)
	})

	return suggestions
}

/**
 * 演示触发机制
 */
export function demoTriggerMechanism() {
	console.log("\n=== 触发机制演示 ===")

	console.log("🎯 触发条件说明:")
	console.log("   • 行末输入 → 触发 Autocomplete")
	console.log("   • 行中间输入 → 1秒防抖后触发 AST+LSP 分析")
	console.log("   • 文档空闲3秒 + 无未执行建议 → 触发模型推荐")

	console.log("\n📋 建议JSON格式:")
	const exampleSuggestion = {
		type: "add",
		description: "Change: Add error handling",
		location: {
			anchor: "function processData() {",
			position: "after",
		},
		patch: {
			new_content: "  try {\n    // existing code\n  } catch (error) {\n    console.error(error);\n  }",
		},
	}
	console.log(JSON.stringify(exampleSuggestion, null, 2))
}

/**
 * 演示性能监控
 */
export async function demoPerformanceMonitoring() {
	console.log("\n=== 性能监控演示 ===")

	const logger = NextEditLogger.getInstance()
	const monitor = NextEditPerformanceMonitor.getInstance()

	// 设置日志级别
	logger.setLogLevel("debug")

	// 模拟一些性能测量
	await measureTime(async () => {
		// 模拟 AST 解析
		await new Promise((resolve) => setTimeout(resolve, 50))
	}, "ast_parsing")

	await measureTime(async () => {
		// 模拟 LSP 查询
		await new Promise((resolve) => setTimeout(resolve, 30))
	}, "lsp_query")

	await measureTime(async () => {
		// 模拟模型分析
		await new Promise((resolve) => setTimeout(resolve, 200))
	}, "model_analysis")

	// 获取性能统计
	console.log("📊 性能统计:")
	const allMetrics = monitor.getAllMetrics()
	for (const [name, stats] of Object.entries(allMetrics)) {
		console.log(`   ${name}: 平均 ${stats.avg.toFixed(2)}ms, 最小 ${stats.min}ms, 最大 ${stats.max}ms`)
	}
}

/**
 * 演示配置管理
 */
export function demoConfigurationManagement() {
	console.log("\n=== 配置管理演示 ===")

	console.log("⚙️ 默认配置:")
	console.log(`   启用状态: ${DEFAULT_NEXT_EDIT_SETTINGS.enabled}`)
	console.log(`   模型推荐防抖: ${DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation.debounceMs}ms`)
	console.log(`   AST+LSP防抖: ${DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis.debounceMs}ms`)
	console.log(`   支持语言: ${DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis.supportedLanguages.join(", ")}`)

	console.log("\n🎛️ 自定义配置示例:")
	const customConfig = {
		enabled: true,
		modelRecommendation: {
			...DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation,
			enabled: true,
			debounceMs: 5000, // 更长的防抖时间
			idleTimeMs: 5000,
			analysisGoals: ["logic_completion", "function_implementation"] as any,
		},
		astLspAnalysis: {
			...DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis,
			enabled: true,
			debounceMs: 2000,
			responseTimeTarget: 50, // 更快的响应目标
			supportedLanguages: ["typescript", "javascript", "python"],
		},
	}
	console.log(JSON.stringify(customConfig, null, 2))
}

/**
 * 运行完整演示
 */
export async function runFullDemo() {
	console.log("🚀 QAX Next Edit 完整功能演示\n")

	try {
		// 1. 基本使用
		const manager = await demoBasicUsage()

		// 2. 建议管理
		const suggestions = await demoSuggestionManagement()

		// 3. 触发机制
		demoTriggerMechanism()

		// 4. 性能监控
		await demoPerformanceMonitoring()

		// 5. 配置管理
		demoConfigurationManagement()

		console.log("\n✅ 演示完成！")
		console.log("\n📚 更多信息请查看 README.md 文档")

		// 清理
		await manager.dispose()
	} catch (error) {
		console.error("❌ 演示过程中出现错误:", error)
	}
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
	runFullDemo().catch(console.error)
}
