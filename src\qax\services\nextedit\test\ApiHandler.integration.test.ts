/**
 * QAX Next Edit - API Handler 和 AutocompleteProvider 集成测试
 * 测试与现有 apiHandler 和 AutocompleteProvider 的集成功能
 */

import * as vscode from "vscode"
import { ModelAnalyzer } from "../analyzers/ModelAnalyzer"
import { AutocompleteProviderAdapter } from "../adapters/AutocompleteProviderAdapter"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { createMockDocument, createMockAnalysisContext, createMockPosition, expect, describe, test } from "./test-utils"

describe("API Handler 和 AutocompleteProvider 集成", () => {
	let analyzer: ModelAnalyzer
	let autocompleteAdapter: AutocompleteProviderAdapter
	const testDocumentUri = "file:///test/example.ts"

	beforeEach(() => {
		analyzer = new ModelAnalyzer(DEFAULT_NEXT_EDIT_SETTINGS)
		autocompleteAdapter = new AutocompleteProviderAdapter()
	})

	afterEach(() => {
		analyzer.dispose()
		autocompleteAdapter.dispose()
	})

	describe("API Handler 集成", () => {
		test("应该能够初始化 API Handler", () => {
			// ModelAnalyzer 应该成功创建，说明 API Handler 初始化成功
			expect(analyzer).toBeDefined()

			console.log("API Handler 初始化测试通过")
		})

		test("应该能够使用英文提示词调用模型", async () => {
			const content = `
function calculateTotal(items) {
    let total = 0;
    // TODO: implement calculation
    return total;
}
`
			const document = createMockDocument(testDocumentUri, content)

			const context = createMockAnalysisContext(document, content)

			const suggestions = await analyzer.performDeepAnalysis(context)

			expect(Array.isArray(suggestions)).toBe(true)
			console.log(`API Handler 调用成功，生成了 ${suggestions.length} 个建议`)
		})

		test("应该能够生成英文提示词", async () => {
			const content = `
class TestClass {
    constructor() {
        // incomplete implementation
    }
}
`
			const document = createMockDocument(testDocumentUri, content)

			const context = createMockAnalysisContext(document, content)

			const prompt = await analyzer.buildOptimizedPrompt(context)

			expect(typeof prompt).toBe("string")
			expect(prompt).toContain("You are a professional code analysis assistant")
			expect(prompt).toContain("Analysis Goals")
			expect(prompt).toContain("Incomplete Logic")
			expect(prompt).toContain("JSON format")
			expect(prompt).toContain("ONLY the JSON format")

			console.log("英文提示词生成测试通过")
		})
	})

	describe("AutocompleteProvider 集成", () => {
		test("应该能够初始化 AutocompleteProvider 适配器", () => {
			expect(autocompleteAdapter).toBeDefined()

			console.log("AutocompleteProvider 适配器初始化测试通过")
		})

		test("应该能够提取智能上下文", async () => {
			const content = `
import { Component } from 'react';

class MyComponent extends Component {
    constructor(props) {
        super(props);
        this.state = { count: 0 };
    }
    
    render() {
        return <div>Count: {this.state.count}</div>;
    }
}
`
			const document = createMockDocument(testDocumentUri, content)
			const position = createMockPosition(5, 10)

			const context = await autocompleteAdapter.extractContext(document, position)

			expect(context).toBeDefined()
			expect(typeof context.contextCode).toBe("string")
			expect(typeof context.strategy).toBe("string")
			expect(typeof context.usedFullFile).toBe("boolean")

			console.log(`上下文提取成功: strategy=${context.strategy}, usedFullFile=${context.usedFullFile}`)
		})

		test("应该能够构建代码上下文对象", async () => {
			const content = `
import React from 'react';

function App() {
    const [count, setCount] = useState(0);
    
    return (
        <div>
            <button onClick={() => setCount(count + 1)}>
                Count: {count}
            </button>
        </div>
    );
}
`
			const document = createMockDocument(testDocumentUri, content)
			const position = createMockPosition(4, 20)

			const codeContext = await autocompleteAdapter.buildCodeContext(document, position)

			expect(codeContext).toBeDefined()
			expect(typeof codeContext.currentLine).toBe("string")
			expect(Array.isArray(codeContext.precedingLines)).toBe(true)
			expect(Array.isArray(codeContext.followingLines)).toBe(true)
			expect(Array.isArray(codeContext.imports)).toBe(true)

			console.log(`代码上下文构建成功: imports=${codeContext.imports.length}, strategy=${codeContext.strategy}`)
		})

		test("应该能够生成英文提示词模板", async () => {
			const content = `
function processData(data) {
    // TODO: implement data processing
    return data;
}
`
			const document = createMockDocument(testDocumentUri, content)
			const position = createMockPosition(2, 10)

			const codeContext = await autocompleteAdapter.buildCodeContext(document, position)
			const prompt = await autocompleteAdapter.generateEnglishPrompt(codeContext, document, position)

			expect(typeof prompt).toBe("string")
			expect(prompt).toContain("You are a professional code completion assistant")
			expect(prompt).toContain("Rules:")
			expect(prompt).toContain("Focus on:")
			expect(prompt).toContain("ONLY the completion code")

			console.log("AutocompleteProvider 英文提示词生成测试通过")
		})

		test("应该能够处理补全响应", () => {
			const rawResponse = `
Here is the code completion:

\`\`\`typescript
if (!data) {
    throw new Error('Data is required');
}
\`\`\`

This code adds error handling for the data parameter.
`

			const processed = autocompleteAdapter.processCompletionResponse(rawResponse)

			expect(typeof processed).toBe("string")
			expect(processed).not.toContain("```")
			expect(processed).not.toContain("Here is")
			expect(processed).not.toContain("This code")
			expect(processed).toContain("if (!data)")

			console.log("补全响应处理测试通过")
		})
	})

	describe("集成工作流程", () => {
		test("应该能够在 ModelAnalyzer 中使用 AutocompleteProvider 增强上下文", async () => {
			const content = `
function fibonacci(n) {
    // TODO: implement fibonacci sequence
}

const result = fibonacci(10);
console.log(result);
`
			const document = createMockDocument(testDocumentUri, content)

			const context = createMockAnalysisContext(document, content)

			// 生成增强的提示词
			const prompt = await analyzer.buildOptimizedPrompt(context)

			expect(prompt).toContain("You are a professional code analysis assistant")
			expect(prompt).toContain("fibonacci")
			expect(prompt).toContain("TODO: implement")

			console.log("ModelAnalyzer 和 AutocompleteProvider 集成测试通过")
		})

		test("应该能够控制输出格式，只返回需要的结果", async () => {
			const content = `
function validateEmail(email) {
    // missing validation logic
    return true;
}
`
			const document = createMockDocument(testDocumentUri, content)

			const context = createMockAnalysisContext(document, content)

			const suggestions = await analyzer.performDeepAnalysis(context)

			// 验证返回的是建议数组
			expect(Array.isArray(suggestions)).toBe(true)

			// 如果有建议，验证格式
			if (suggestions.length > 0) {
				const suggestion = suggestions[0]
				expect(suggestion).toHaveProperty("type")
				expect(suggestion).toHaveProperty("description")
				expect(suggestion).toHaveProperty("location")
				expect(suggestion).toHaveProperty("patch")
			}

			console.log("输出格式控制测试通过")
		})

		test("应该能够处理错误情况", async () => {
			const content = `
// 空文件或无效内容
`
			const document = createMockDocument(testDocumentUri, content)

			const context = createMockAnalysisContext(document, content)

			// 应该不会抛出错误
			const suggestions = await analyzer.performDeepAnalysis(context)
			expect(Array.isArray(suggestions)).toBe(true)

			console.log("错误处理测试通过")
		})
	})
})
