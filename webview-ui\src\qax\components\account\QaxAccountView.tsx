import { useQaxAuth } from "@/context/ClineAuthContext"
import { VSCodeButton, VSCodeDivider } from "@vscode/webview-ui-toolkit/react"
import { memo } from "react"

/**
 * 格式化 JWT 过期时间戳为可读格式
 * @param exp JWT 过期时间戳（秒）
 * @returns 格式化的时间字符串
 */
const formatTokenExpiration = (exp: number): string => {
	const date = new Date(exp * 1000) // JWT exp 是秒，需要转换为毫秒
	return date.toLocaleString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
		hour: "2-digit",
		minute: "2-digit",
		second: "2-digit",
		hour12: false,
	})
}

/**
 * 检查 token 是否即将过期（24小时内）
 * @param exp JWT 过期时间戳（秒）
 * @returns 是否即将过期
 */
const isTokenExpiringSoon = (exp: number): boolean => {
	const now = Math.floor(Date.now() / 1000)
	const hoursUntilExpiry = (exp - now) / 3600
	return hoursUntilExpiry <= 24 && hoursUntilExpiry > 0
}

/**
 * 检查 token 是否已过期
 * @param exp JWT 过期时间戳（秒）
 * @returns 是否已过期
 */
const isTokenExpired = (exp: number): boolean => {
	const now = Math.floor(Date.now() / 1000)
	return exp <= now
}

type QaxAccountViewProps = {
	onDone: () => void
}

const QaxAccountView = ({ onDone }: QaxAccountViewProps) => {
	return (
		<div className="fixed inset-0 flex flex-col overflow-hidden pt-[10px] pl-[20px]">
			<div className="flex justify-between items-center mb-[17px] pr-[17px]">
				<h3 className="text-[var(--vscode-foreground)] m-0">QAX 账户</h3>
				<VSCodeButton onClick={onDone}>完成</VSCodeButton>
			</div>
			<div className="flex-grow overflow-hidden pr-[8px] flex flex-col">
				<div className="h-full mb-[5px]">
					<QaxUserInfoView />
				</div>
			</div>
		</div>
	)
}

/**
 * QAX 用户信息显示组件
 * 显示用户基本信息：用户名、邮箱、token 过期时间
 */
export const QaxUserInfoView = () => {
	const { qaxUser, handleSignIn, handleSignOut } = useQaxAuth()

	// 如果用户未登录，显示登录界面
	if (!qaxUser) {
		return (
			<div className="h-full flex flex-col">
				<div className="flex flex-col items-center pr-3">
					<div className="size-16 rounded-full bg-[var(--vscode-button-background)] flex items-center justify-center text-2xl text-[var(--vscode-button-foreground)] mb-4">
						QAX
					</div>

					<p className="text-center mb-4">请登录 QAX 账户以查看用户信息和使用 QAX Codegen 服务。</p>

					<VSCodeButton onClick={handleSignIn} className="w-full mb-4">
						登录 QAX 账户
					</VSCodeButton>
				</div>
			</div>
		)
	}

	// 已登录用户信息显示
	const tokenExpired = isTokenExpired(qaxUser.exp)
	const tokenExpiringSoon = isTokenExpiringSoon(qaxUser.exp)

	return (
		<div className="h-full flex flex-col">
			<div className="flex flex-col pr-3 h-full">
				{/* 用户基本信息 */}
				<div className="flex flex-col w-full">
					<div className="flex items-center mb-6 flex-wrap gap-y-4">
						{/* 用户头像 */}
						<div className="size-16 rounded-full bg-[var(--vscode-button-background)] flex items-center justify-center text-2xl text-[var(--vscode-button-foreground)] mr-4">
							{qaxUser.displayName?.[0] || qaxUser.name?.[0] || qaxUser.email?.[0] || "Q"}
						</div>

						{/* 用户信息 */}
						<div className="flex flex-col">
							{/* 显示名称 */}
							{qaxUser.displayName && (
								<h2 className="text-[var(--vscode-foreground)] m-0 text-lg font-medium">{qaxUser.displayName}</h2>
							)}

							{/* 用户名 */}
							{qaxUser.name && qaxUser.name !== qaxUser.displayName && (
								<div className="text-sm text-[var(--vscode-descriptionForeground)]">用户名: {qaxUser.name}</div>
							)}

							{/* 邮箱 */}
							{qaxUser.email && (
								<div className="text-sm text-[var(--vscode-descriptionForeground)]">邮箱: {qaxUser.email}</div>
							)}

							{/* 工号 */}
							{qaxUser.employeeNumber && (
								<div className="text-sm text-[var(--vscode-descriptionForeground)]">
									工号: {qaxUser.employeeNumber}
								</div>
							)}
						</div>
					</div>
				</div>

				<VSCodeDivider className="w-full my-6" />

				{/* Token 过期信息 */}
				<div className="w-full flex flex-col">
					<div className="text-sm text-[var(--vscode-descriptionForeground)] mb-3 font-mono">TOKEN 状态</div>

					<div className="mb-4">
						<div className="text-sm mb-2">
							<span className="text-[var(--vscode-descriptionForeground)]">过期时间: </span>
							<span className="font-mono">{formatTokenExpiration(qaxUser.exp)}</span>
						</div>

						{tokenExpired && (
							<div className="text-sm text-[var(--vscode-errorForeground)] mb-2">⚠️ Token 已过期，请重新登录</div>
						)}

						{!tokenExpired && tokenExpiringSoon && (
							<div className="text-sm text-[var(--vscode-notificationsWarningIcon-foreground)] mb-2">
								⚠️ Token 将在 24 小时内过期
							</div>
						)}

						{!tokenExpired && !tokenExpiringSoon && (
							<div className="text-sm text-[var(--vscode-testing-iconPassed)] mb-2">✅ Token 有效</div>
						)}
					</div>
				</div>

				<VSCodeDivider className="w-full my-6" />

				{/* 操作按钮 */}
				<div className="w-full flex gap-2 flex-col min-[225px]:flex-row">
					<VSCodeButton appearance="secondary" onClick={handleSignOut} className="w-full">
						退出登录
					</VSCodeButton>
				</div>
			</div>
		</div>
	)
}

export default memo(QaxAccountView)
