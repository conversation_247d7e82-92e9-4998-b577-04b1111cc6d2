# QAX Next Edit 架构设计文档

## 概述

QAX Next Edit 是一个智能代码编辑建议系统，为 VSCode 扩展提供两种推荐模式：
1. **基于 AST+LSP 的快速语法推荐**：响应时间 0.1 秒，专注于语法级别的修改建议
2. **基于 AI 模型的深度分析推荐**：响应时间 5 秒，提供逻辑完整性、代码质量等深度建议

系统通过维护每个文档的建议数组，提供增量分析能力，并与现有的 autocomplete 功能严格区分触发时机。

## 核心特性

- **双模式推荐引擎**：AST+LSP 快速分析 + AI 模型深度分析
- **智能触发机制**：基于输入位置和文档状态的精确触发控制
- **增量缓存系统**：AST 和 LSP 数据的增量更新和缓存管理
- **建议生命周期管理**：自动过滤失效建议，维护建议数组的有效性
- **防抖机制**：避免频繁触发，优化性能体验
- **与 Autocomplete 互斥**：确保功能边界清晰，避免冲突

## 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "QAX Next Edit 核心系统"
        A[NextEditManager<br/>主管理器] --> B[SuggestionManager<br/>建议管理器]
        A --> C[TriggerManager<br/>触发管理器]
        A --> D[AnalysisEngine<br/>分析引擎]

        B --> E[DocumentSuggestionStore<br/>文档建议存储]
        B --> F[SuggestionFilter<br/>建议过滤器]
        B --> G[SuggestionValidator<br/>建议验证器]

        C --> H[DocumentChangeListener<br/>文档变更监听器]
        C --> I[DebounceManager<br/>防抖管理器]
        C --> J[TriggerConditionChecker<br/>触发条件检查器]

        D --> K[ASTLSPAnalyzer<br/>AST+LSP分析器]
        D --> L[ModelAnalyzer<br/>模型分析器]
        D --> M[DiffGenerator<br/>差异生成器]

        K --> N[ASTParser<br/>AST解析器]
        K --> O[LSPClient<br/>LSP客户端]
        K --> P[ChangeTypeDetector<br/>变更类型检测器]
        K --> Q[ReferenceAnalyzer<br/>引用分析器]

        L --> R[ModelAPIClient<br/>模型API客户端]
        L --> S[ContextCollector<br/>上下文收集器]
        L --> T[ResponseParser<br/>响应解析器]

        N --> U[ASTCache<br/>AST缓存]
        O --> V[LSPCache<br/>LSP缓存]
    end

    subgraph "VSCode 集成层"
        W[DocumentChangeEvents<br/>文档变更事件] --> H
        X[LSPServices<br/>LSP服务] --> O
        Y[ModelAPI<br/>模型API] --> R
        Z[ConfigurationService<br/>配置服务] --> A
    end

    subgraph "现有系统集成"
        AA[AutocompleteProvider<br/>自动补全] -.-> C
        BB[TaskManager<br/>任务管理器] -.-> A
        CC[TreeSitterService<br/>语法解析服务] -.-> N
    end
```

### 触发机制流程图

```mermaid
flowchart TD
    A[用户输入/文档变更] --> B{检查触发条件}

    B --> C{输入位置在行末?}
    C -->|是| D[触发 Autocomplete]
    C -->|否| E{输入位置在行中间?}

    E -->|是| F[等待1秒防抖]
    F --> G[触发 AST+LSP 分析]

    E -->|否| H{文档空闲3秒?}
    H -->|是| I{有未执行建议?}
    I -->|否| J[触发模型推荐]
    I -->|是| K[跳过模型推荐]

    G --> L[生成文档 Diff]
    L --> M{能确定变更类型?}
    M -->|是| N[查找符号引用]
    M -->|否| O[结束分析]

    N --> P[生成 AST+LSP 建议]

    J --> Q[收集最近变更]
    Q --> R[构建分析上下文]
    R --> S[调用模型API]
    S --> T[解析模型响应]
    T --> U[生成模型建议]

    P --> V[过滤和验证建议]
    U --> V
    V --> W[更新建议数组]
    W --> X[显示建议给用户]
```

### 建议生命周期管理

```mermaid
stateDiagram-v2
    [*] --> Created: 生成建议
    Created --> Validated: 验证通过
    Created --> Invalid: 验证失败
    Validated --> Displayed: 显示给用户
    Displayed --> Executed: 用户执行
    Displayed --> Dismissed: 用户忽略
    Displayed --> Obsolete: 文档变更导致失效
    Executed --> [*]
    Dismissed --> [*]
    Obsolete --> [*]
    Invalid --> [*]
```

## 核心组件接口设计

### 1. NextEditManager (主管理器)

主管理器负责协调所有子组件，管理文档生命周期，处理用户交互。

```typescript
interface NextEditManager {
    // 初始化和配置
    initialize(context: vscode.ExtensionContext): Promise<void>
    dispose(): Promise<void>
    updateConfiguration(config: NextEditSettings): void

    // 文档生命周期管理
    onDocumentOpened(document: vscode.TextDocument): Promise<void>
    onDocumentClosed(document: vscode.TextDocument): Promise<void>
    onDocumentChanged(document: vscode.TextDocument, changes: vscode.TextDocumentChangeEvent[]): Promise<void>
    onDocumentSaved(document: vscode.TextDocument): Promise<void>

    // 建议管理
    getSuggestions(documentUri: string): Suggestion[]
    executeSuggestion(suggestion: Suggestion): Promise<boolean>
    dismissSuggestion(suggestionId: string): Promise<void>
    dismissAllSuggestions(documentUri: string): Promise<void>

    // 状态查询
    isAnalyzing(documentUri: string): boolean
    getAnalysisStatus(documentUri: string): AnalysisStatus

    // 事件处理
    onSuggestionGenerated(callback: (suggestions: Suggestion[]) => void): void
    onAnalysisCompleted(callback: (documentUri: string, duration: number) => void): void
    onError(callback: (error: NextEditError) => void): void
}
```

### 2. SuggestionManager (建议管理器)

建议管理器负责维护每个文档的建议数组，处理建议的增删改查，以及建议的过滤和验证。

```typescript
interface SuggestionManager {
    // 建议存储和检索
    addSuggestions(documentUri: string, suggestions: Suggestion[]): Promise<void>
    getSuggestions(documentUri: string): Suggestion[]
    getSuggestionById(suggestionId: string): Suggestion | null
    removeSuggestion(documentUri: string, suggestionId: string): Promise<boolean>
    clearSuggestions(documentUri: string): Promise<void>
    clearAllSuggestions(): Promise<void>

    // 建议过滤和验证
    filterObsoleteSuggestions(documentUri: string, changes: vscode.TextDocumentChangeEvent[]): Promise<Suggestion[]>
    validateSuggestion(suggestion: Suggestion, document: vscode.TextDocument): Promise<boolean>
    validateAllSuggestions(documentUri: string, document: vscode.TextDocument): Promise<void>

    // 建议统计和查询
    getSuggestionCount(documentUri: string): number
    getSuggestionsByType(documentUri: string, type: SuggestionType): Suggestion[]
    getSuggestionsBySource(documentUri: string, source: SuggestionSource): Suggestion[]
    hasUnexecutedSuggestions(documentUri: string): boolean

    // 建议排序和优先级
    sortSuggestionsByPriority(suggestions: Suggestion[]): Suggestion[]
    updateSuggestionPriority(suggestionId: string, priority: number): Promise<void>

    // 事件通知
    onSuggestionsUpdated(callback: (documentUri: string, suggestions: Suggestion[]) => void): void
    onSuggestionRemoved(callback: (suggestionId: string) => void): void
}
```

### 3. TriggerManager (触发管理器)

触发管理器负责判断何时触发不同类型的分析，管理防抖机制，确保与 autocomplete 的互斥触发。

```typescript
interface TriggerManager {
    // 触发条件检查
    shouldTriggerModelRecommendation(document: vscode.TextDocument): Promise<boolean>
    shouldTriggerASTLSPAnalysis(document: vscode.TextDocument, position: vscode.Position): Promise<boolean>
    shouldTriggerAutocomplete(document: vscode.TextDocument, position: vscode.Position): boolean

    // 位置分析
    isPositionAtLineEnd(document: vscode.TextDocument, position: vscode.Position): boolean
    isPositionInLineMiddle(document: vscode.TextDocument, position: vscode.Position): boolean
    getInputContext(document: vscode.TextDocument, position: vscode.Position): InputContext

    // 防抖管理
    debounceModelRecommendation(documentUri: string, callback: () => void): void
    debounceASTLSPAnalysis(documentUri: string, callback: () => void): void
    cancelPendingAnalysis(documentUri: string, analysisType?: AnalysisType): void

    // 文档状态管理
    setDocumentIdle(documentUri: string): void
    setDocumentActive(documentUri: string): void
    getDocumentIdleTime(documentUri: string): number
    isDocumentIdle(documentUri: string, requiredIdleMs: number): boolean

    // 分析状态管理
    setAnalysisInProgress(documentUri: string, analysisType: AnalysisType): void
    setAnalysisCompleted(documentUri: string, analysisType: AnalysisType): void
    isAnalysisInProgress(documentUri: string, analysisType?: AnalysisType): boolean

    // 互斥控制
    canTriggerAnalysis(documentUri: string, analysisType: AnalysisType): boolean
    blockAnalysis(documentUri: string, analysisType: AnalysisType, durationMs: number): void

    // 事件处理
    onTriggerConditionMet(callback: (documentUri: string, analysisType: AnalysisType) => void): void
    onAnalysisBlocked(callback: (documentUri: string, reason: string) => void): void
}
```

### 4. AnalysisEngine (分析引擎)

分析引擎是核心分析组件，协调 AST+LSP 分析器和模型分析器，生成文档差异，收集分析上下文。

```typescript
interface AnalysisEngine {
    // 分析调度
    scheduleAnalysis(documentUri: string, analysisType: AnalysisType, priority: number): Promise<string>
    cancelAnalysis(analysisId: string): Promise<boolean>
    getAnalysisQueue(): AnalysisTask[]

    // 模型分析
    performModelAnalysis(document: vscode.TextDocument, context: ModelAnalysisContext): Promise<Suggestion[]>
    buildModelAnalysisContext(document: vscode.TextDocument): Promise<ModelAnalysisContext>

    // AST+LSP分析
    performASTLSPAnalysis(document: vscode.TextDocument, position: vscode.Position, diff: DocumentDiff): Promise<Suggestion[]>

    // 差异生成
    generateDocumentDiff(document: vscode.TextDocument, changes: vscode.TextDocumentChangeEvent[]): DocumentDiff
    generateIncrementalDiff(documentUri: string, changes: vscode.TextDocumentChangeEvent[]): DocumentDiff

    // 上下文收集
    collectRecentChanges(timeWindowMs?: number): Promise<RecentChange[]>
    collectRelatedFiles(document: vscode.TextDocument): Promise<string[]>
    collectWorkspaceContext(): Promise<WorkspaceContext>

    // 分析结果处理
    mergeAnalysisResults(results: AnalysisResult[]): Suggestion[]
    filterDuplicateSuggestions(suggestions: Suggestion[]): Suggestion[]

    // 性能监控
    getAnalysisMetrics(analysisType?: AnalysisType): AnalysisMetrics
    resetMetrics(): void

    // 事件通知
    onAnalysisStarted(callback: (analysisId: string, analysisType: AnalysisType) => void): void
    onAnalysisCompleted(callback: (analysisId: string, result: AnalysisResult) => void): void
    onAnalysisError(callback: (analysisId: string, error: NextEditError) => void): void
}
```

### 5. ASTLSPAnalyzer (AST+LSP分析器)

AST+LSP 分析器负责快速语法分析，检测代码变更类型，查找符号引用，生成语法级别的修改建议。

```typescript
interface ASTLSPAnalyzer {
    // 初始化和配置
    initialize(treeSitterService: TreeSitterService, lspClient: LSPClient): Promise<void>
    dispose(): Promise<void>

    // 修改类型检测
    detectChangeType(diff: DocumentDiff, document: vscode.TextDocument): Promise<ChangeType | null>
    analyzeChangePattern(diff: DocumentDiff): ChangePattern[]

    // 符号分析
    analyzeSymbolRename(oldSymbol: string, newSymbol: string, document: vscode.TextDocument): Promise<Suggestion[]>
    analyzeFunctionSignatureChange(functionInfo: FunctionInfo, oldSignature: string, newSignature: string): Promise<Suggestion[]>
    analyzeVariableDeclaration(variableInfo: VariableInfo, document: vscode.TextDocument): Promise<Suggestion[]>
    analyzeImportChange(importInfo: ImportInfo, document: vscode.TextDocument): Promise<Suggestion[]>

    // 引用查找和分析
    findAllReferences(symbol: string, document: vscode.TextDocument): Promise<vscode.Location[]>
    findDefinition(symbol: string, position: vscode.Position, document: vscode.TextDocument): Promise<vscode.Location | null>
    findImplementations(symbol: string, document: vscode.TextDocument): Promise<vscode.Location[]>

    // AST 解析和缓存
    parseAST(document: vscode.TextDocument): Promise<ASTNode>
    updateASTCache(document: vscode.TextDocument, changes: vscode.TextDocumentChangeEvent[]): Promise<void>
    getASTFromCache(documentUri: string): ASTNode | null
    invalidateASTCache(documentUri: string): void

    // LSP 集成和缓存
    updateLSPCache(document: vscode.TextDocument): Promise<void>
    getLSPSymbols(document: vscode.TextDocument): Promise<vscode.DocumentSymbol[]>
    getLSPDiagnostics(document: vscode.TextDocument): Promise<vscode.Diagnostic[]>

    // 建议生成
    generateRenameSuggestions(renameInfo: RenameInfo): Promise<Suggestion[]>
    generateSignatureChangeSuggestions(signatureInfo: SignatureChangeInfo): Promise<Suggestion[]>
    generateRefactoringSuggestions(refactoringInfo: RefactoringInfo): Promise<Suggestion[]>

    // 性能优化
    enableIncrementalParsing(enabled: boolean): void
    getParsingMetrics(): ParsingMetrics

    // 事件处理
    onASTUpdated(callback: (documentUri: string, ast: ASTNode) => void): void
    onSymbolChanged(callback: (documentUri: string, symbol: SymbolChange) => void): void
}
```

### 6. ModelAnalyzer (模型分析器)

模型分析器负责深度代码分析，调用 AI 模型生成高质量的代码改进建议。

```typescript
interface ModelAnalyzer {
    // 初始化和配置
    initialize(apiClient: ModelAPIClient, config: ModelAnalysisConfig): Promise<void>
    dispose(): Promise<void>
    updateModelConfig(config: ModelAnalysisConfig): void

    // 模型推荐
    generateRecommendations(context: ModelAnalysisContext): Promise<ModelResponse>
    generateIncrementalRecommendations(context: ModelAnalysisContext, previousSuggestions: Suggestion[]): Promise<ModelResponse>

    // 上下文构建
    buildAnalysisContext(document: vscode.TextDocument, recentChanges: RecentChange[]): Promise<ModelAnalysisContext>
    buildPromptContext(context: ModelAnalysisContext): string
    optimizeContextSize(context: ModelAnalysisContext, maxTokens: number): ModelAnalysisContext

    // 响应解析
    parseModelResponse(response: string): Promise<Suggestion[]>
    validateModelResponse(response: string): boolean
    extractSuggestionsFromResponse(response: string): RawSuggestion[]

    // 建议类型分析
    analyzeIncompleteLogic(document: vscode.TextDocument): Promise<Suggestion[]>
    analyzeUnimplementedFunctions(document: vscode.TextDocument): Promise<Suggestion[]>
    analyzeUnusedVariables(document: vscode.TextDocument): Promise<Suggestion[]>
    analyzeFormattingIssues(document: vscode.TextDocument): Promise<Suggestion[]>
    analyzeLintIssues(document: vscode.TextDocument): Promise<Suggestion[]>

    // 质量评估
    evaluateSuggestionQuality(suggestion: Suggestion, context: ModelAnalysisContext): number
    filterLowQualitySuggestions(suggestions: Suggestion[], threshold: number): Suggestion[]

    // 缓存管理
    cacheAnalysisResult(contextHash: string, result: ModelResponse): void
    getCachedAnalysisResult(contextHash: string): ModelResponse | null
    clearAnalysisCache(): void

    // 性能监控
    getModelMetrics(): ModelAnalysisMetrics
    trackAnalysisLatency(startTime: number, endTime: number): void

    // 事件处理
    onModelRequestSent(callback: (requestId: string, context: ModelAnalysisContext) => void): void
    onModelResponseReceived(callback: (requestId: string, response: ModelResponse) => void): void
    onAnalysisError(callback: (requestId: string, error: ModelAnalysisError) => void): void
}
```

## 数据模型设计

### 核心数据结构

#### 建议数据结构

```typescript
// 建议数据结构 - 符合用户要求的 JSON 格式
interface Suggestion {
    id: string                          // 唯一标识符
    type: 'add' | 'modify' | 'delete'   // 建议类型
    description: string                 // 变更描述，格式：Change: xxx -> yyy | Change: Add xxx | Change: Del xxx
    location: SuggestionLocation        // 位置信息
    patch: SuggestionPatch             // 修改内容

    // 扩展字段
    confidence: number                  // 置信度 0-1
    source: SuggestionSource           // 建议来源
    priority: number                   // 优先级 1-10
    timestamp: number                  // 创建时间戳
    category: SuggestionCategory       // 建议分类
    impact: ImpactLevel                // 影响级别
    tags: string[]                     // 标签
    relatedFiles?: string[]            // 相关文件
    dependencies?: string[]            // 依赖的其他建议ID
}

interface SuggestionLocation {
    anchor: string                     // 用于定位的唯一代码模式
    position: 'before' | 'after' | 'replace'  // 相对位置
    range?: vscode.Range              // 可选的精确范围
    lineNumber?: number               // 行号（用于快速定位）
    columnNumber?: number             // 列号
    contextLines?: string[]           // 上下文行（用于验证）
}

interface SuggestionPatch {
    new_content: string               // 新内容（符合用户要求的字段名）
    old_content?: string              // 原内容（用于replace类型）
    encoding?: string                 // 编码格式
    language?: string                 // 编程语言
}

// 枚举类型定义
enum SuggestionSource {
    MODEL = 'model',
    AST_LSP = 'ast-lsp',
    HYBRID = 'hybrid'
}

enum SuggestionCategory {
    LOGIC_COMPLETION = 'logic_completion',      // 逻辑完整性
    FUNCTION_IMPLEMENTATION = 'function_impl',   // 函数实现
    VARIABLE_USAGE = 'variable_usage',          // 变量使用
    CODE_FORMATTING = 'formatting',             // 代码格式
    LINT_ISSUES = 'lint',                      // 代码规范
    REFACTORING = 'refactoring',               // 重构建议
    PERFORMANCE = 'performance',                // 性能优化
    SECURITY = 'security',                     // 安全问题
    DOCUMENTATION = 'documentation'             // 文档完善
}

enum ImpactLevel {
    LOW = 'low',        // 低影响：格式、注释等
    MEDIUM = 'medium',  // 中等影响：变量重命名、小重构
    HIGH = 'high',      // 高影响：函数签名变更、大重构
    CRITICAL = 'critical' // 关键影响：API变更、架构调整
}

// 文档建议存储
interface DocumentSuggestionStore {
    [documentUri: string]: DocumentSuggestionData
}

interface DocumentSuggestionData {
    suggestions: Suggestion[]           // 建议数组
    lastUpdate: number                 // 最后更新时间
    documentVersion: number            // 文档版本号
    analysisHistory: AnalysisRecord[]  // 分析历史
    metadata: DocumentMetadata         // 文档元数据
}

interface DocumentMetadata {
    language: string                   // 编程语言
    fileSize: number                  // 文件大小
    lineCount: number                 // 行数
    lastAnalysisTime: number          // 最后分析时间
    analysisCount: number             // 分析次数
    suggestionStats: SuggestionStats  // 建议统计
}

interface SuggestionStats {
    totalGenerated: number            // 总生成数
    totalExecuted: number             // 总执行数
    totalDismissed: number            // 总忽略数
    averageConfidence: number         // 平均置信度
    categoryDistribution: Record<SuggestionCategory, number>  // 分类分布
}

#### 分析上下文数据结构

```typescript
// 基础分析上下文
interface AnalysisContext {
    document: vscode.TextDocument      // 当前文档
    recentChanges: RecentChange[]      // 最近变更
    relatedFiles: string[]             // 相关文件
    workspaceRoot: string              // 工作区根目录
    timestamp: number                  // 分析时间戳
}

// 模型分析上下文
interface ModelAnalysisContext extends AnalysisContext {
    currentContent: string             // 当前文件内容
    changeHistory: DocumentChange[]    // 变更历史
    projectContext: ProjectContext     // 项目上下文
    codebaseStats: CodebaseStats      // 代码库统计
    analysisGoals: AnalysisGoal[]     // 分析目标
}

// AST+LSP分析上下文
interface ASTLSPAnalysisContext extends AnalysisContext {
    diff: DocumentDiff                 // 文档差异
    position: vscode.Position          // 当前位置
    astNode: ASTNode                   // AST节点
    symbolInfo: SymbolInfo[]           // 符号信息
    lspDiagnostics: vscode.Diagnostic[] // LSP诊断信息
}

// 项目上下文
interface ProjectContext {
    packageInfo: PackageInfo           // 包信息
    dependencies: DependencyInfo[]     // 依赖信息
    buildConfig: BuildConfig           // 构建配置
    testFiles: string[]                // 测试文件
    documentationFiles: string[]       // 文档文件
}

// 代码库统计
interface CodebaseStats {
    totalFiles: number                 // 总文件数
    totalLines: number                 // 总行数
    languageDistribution: Record<string, number>  // 语言分布
    complexityMetrics: ComplexityMetrics  // 复杂度指标
    qualityMetrics: QualityMetrics     // 质量指标
}
```

#### 文档变更和差异数据结构

```typescript
// 最近变更
interface RecentChange {
    filePath: string                   // 文件路径
    changeType: 'create' | 'modify' | 'delete' | 'rename'  // 变更类型
    timestamp: number                  // 变更时间戳
    diff?: string                      // 差异内容
    author?: string                    // 变更作者
    commitHash?: string                // 提交哈希
    changeSize: number                 // 变更大小（行数）
    impactLevel: ImpactLevel          // 影响级别
}

// 文档差异（完整的diff，不是单字符diff）
interface DocumentDiff {
    additions: DiffLine[]              // 新增行
    deletions: DiffLine[]              // 删除行
    modifications: DiffLine[]          // 修改行
    isComplete: boolean                // 是否为完整diff
    diffType: DiffType                 // 差异类型
    affectedSymbols: string[]          // 受影响的符号
    contextLines: number               // 上下文行数
    metadata: DiffMetadata             // 差异元数据
}

interface DiffLine {
    lineNumber: number                 // 行号
    content: string                    // 行内容
    type: 'added' | 'removed' | 'modified'  // 行类型
    indentLevel: number                // 缩进级别
    syntaxType?: string                // 语法类型
}

enum DiffType {
    SYMBOL_RENAME = 'symbol_rename',           // 符号重命名
    FUNCTION_SIGNATURE = 'function_signature', // 函数签名变更
    VARIABLE_DECLARATION = 'variable_decl',    // 变量声明
    IMPORT_STATEMENT = 'import_statement',     // 导入语句
    CLASS_DEFINITION = 'class_definition',     // 类定义
    METHOD_IMPLEMENTATION = 'method_impl',     // 方法实现
    BLOCK_RESTRUCTURE = 'block_restructure',   // 代码块重构
    FORMATTING_CHANGE = 'formatting',          // 格式变更
    UNKNOWN = 'unknown'                        // 未知类型
}

interface DiffMetadata {
    confidence: number                 // 差异检测置信度
    complexity: number                 // 变更复杂度
    riskLevel: 'low' | 'medium' | 'high'  // 风险级别
    estimatedImpact: string[]          // 预估影响范围
}
```

#### 缓存系统数据结构

```typescript
// AST缓存（增量更新，非持久化）
interface ASTCache {
    [documentUri: string]: ASTCacheEntry
}

interface ASTCacheEntry {
    ast: ASTNode                       // 解析后的AST
    symbols: SymbolInfo[]              // 符号信息
    lastUpdate: number                 // 最后更新时间
    documentVersion: number            // 文档版本
    parseTime: number                  // 解析耗时
    cacheHits: number                  // 缓存命中次数
    isValid: boolean                   // 缓存是否有效
    dependencies: string[]             // 依赖的其他文件
}

// LSP缓存
interface LSPCache {
    [documentUri: string]: LSPCacheEntry
}

interface LSPCacheEntry {
    references: Map<string, vscode.Location[]>     // 符号引用
    definitions: Map<string, vscode.Location>      // 符号定义
    implementations: Map<string, vscode.Location[]> // 符号实现
    diagnostics: vscode.Diagnostic[]               // 诊断信息
    documentSymbols: vscode.DocumentSymbol[]       // 文档符号
    lastUpdate: number                             // 最后更新时间
    lspVersion: string                             // LSP版本
    isValid: boolean                               // 缓存是否有效
}

// 符号信息
interface SymbolInfo {
    name: string                       // 符号名称
    type: string                       // 符号类型
    kind: vscode.SymbolKind           // 符号种类
    range: vscode.Range               // 符号范围
    selectionRange: vscode.Range      // 选择范围
    references: vscode.Location[]     // 引用位置
    definition?: vscode.Location      // 定义位置
    implementations: vscode.Location[] // 实现位置
    containerName?: string            // 容器名称
    detail?: string                   // 详细信息
    documentation?: string            // 文档说明
}

// AST节点
interface ASTNode {
    type: string                      // 节点类型
    range: [number, number]           // 字节范围
    startPosition: Position           // 开始位置
    endPosition: Position             // 结束位置
    children: ASTNode[]               // 子节点
    parent?: ASTNode                  // 父节点
    text?: string                     // 节点文本
    namedChildren: ASTNode[]          // 命名子节点
    fieldName?: string                // 字段名
}

interface Position {
    row: number                       // 行号
    column: number                    // 列号
}
```
```

#### 配置数据结构

```typescript
interface NextEditSettings {
    enabled: boolean                   // 总开关

    // 模型推荐配置
    modelRecommendation: ModelRecommendationConfig

    // AST+LSP分析配置
    astLspAnalysis: ASTLSPAnalysisConfig

    // 建议过滤配置
    suggestionFilter: SuggestionFilterConfig

    // 触发机制配置
    triggerConfig: TriggerConfig

    // 性能配置
    performance: PerformanceConfig

    // UI配置
    ui: UIConfig

    // 调试配置
    debug: DebugConfig
}

interface ModelRecommendationConfig {
    enabled: boolean                   // 是否启用模型推荐
    debounceMs: number                // 防抖时间（默认3000ms）
    idleTimeMs: number                // 空闲时间要求（默认3000ms）
    maxContextLines: number           // 最大上下文行数
    apiEndpoint: string               // API端点
    modelId: string                   // 模型ID
    maxTokens: number                 // 最大token数
    temperature: number               // 温度参数
    timeout: number                   // 请求超时时间
    retryAttempts: number             // 重试次数
    enableCache: boolean              // 是否启用缓存
    analysisGoals: AnalysisGoal[]     // 分析目标
}

interface ASTLSPAnalysisConfig {
    enabled: boolean                  // 是否启用AST+LSP分析
    debounceMs: number               // 防抖时间（默认1000ms）
    responseTimeTarget: number       // 响应时间目标（默认100ms）
    enableIncrementalCache: boolean  // 是否启用增量缓存
    maxCacheSize: number             // 最大缓存大小（MB）
    cacheExpirationMs: number        // 缓存过期时间
    enableParallelParsing: boolean   // 是否启用并行解析
    supportedLanguages: string[]     // 支持的语言
    treeSitterConfig: TreeSitterConfig // Tree-sitter配置
}

interface SuggestionFilterConfig {
    minConfidence: number            // 最小置信度阈值
    maxSuggestionsPerDocument: number // 每个文档最大建议数
    enableDuplicateFilter: boolean   // 是否启用重复过滤
    enableQualityFilter: boolean     // 是否启用质量过滤
    categoryFilters: Record<SuggestionCategory, boolean> // 分类过滤器
    impactLevelFilters: Record<ImpactLevel, boolean>     // 影响级别过滤器
    blacklistPatterns: string[]      // 黑名单模式
    whitelistPatterns: string[]      // 白名单模式
}

interface TriggerConfig {
    enableAutocompleteIntegration: boolean  // 是否集成autocomplete
    lineEndTriggerAutocomplete: boolean     // 行末触发autocomplete
    lineMiddleTriggerASTLSP: boolean       // 行中触发AST+LSP
    documentIdleTriggerModel: boolean      // 文档空闲触发模型
    preventDuplicateTriggers: boolean      // 防止重复触发
    maxConcurrentAnalyses: number          // 最大并发分析数
}

interface PerformanceConfig {
    enableMetrics: boolean           // 是否启用性能指标
    metricsRetentionDays: number    // 指标保留天数
    enableProfiling: boolean        // 是否启用性能分析
    memoryLimitMB: number           // 内存限制
    cpuThrottleThreshold: number    // CPU节流阈值
    enableResourceMonitoring: boolean // 是否启用资源监控
}

interface UIConfig {
    showSuggestionNotifications: boolean    // 显示建议通知
    suggestionDisplayMode: 'inline' | 'popup' | 'panel'  // 建议显示模式
    enableHoverPreview: boolean            // 启用悬停预览
    enableQuickActions: boolean            // 启用快速操作
    themeIntegration: boolean              // 主题集成
    animationEnabled: boolean              // 启用动画
}

interface DebugConfig {
    enableLogging: boolean          // 启用日志
    logLevel: 'error' | 'warn' | 'info' | 'debug'  // 日志级别
    enableTracing: boolean          // 启用追踪
    saveAnalysisResults: boolean    // 保存分析结果
    enablePerformanceLogging: boolean // 启用性能日志
    debugOutputPath?: string        // 调试输出路径
}

enum AnalysisGoal {
    LOGIC_COMPLETION = 'logic_completion',
    FUNCTION_IMPLEMENTATION = 'function_implementation',
    VARIABLE_USAGE = 'variable_usage',
    CODE_FORMATTING = 'code_formatting',
    LINT_ISSUES = 'lint_issues',
    PERFORMANCE_OPTIMIZATION = 'performance_optimization',
    SECURITY_ANALYSIS = 'security_analysis',
    DOCUMENTATION = 'documentation'
}
```

## 架构实现细节

### 触发机制详细设计

#### 触发条件判断逻辑

```typescript
class TriggerConditionChecker {
    // 检查是否应该触发模型推荐
    async shouldTriggerModelRecommendation(document: vscode.TextDocument): Promise<boolean> {
        // 1. 检查文档是否空闲3秒
        const idleTime = this.getDocumentIdleTime(document.uri.toString())
        if (idleTime < 3000) return false

        // 2. 检查是否有未执行的建议
        const hasUnexecuted = await this.suggestionManager.hasUnexecutedSuggestions(document.uri.toString())
        if (hasUnexecuted) return false

        // 3. 检查是否正在进行其他分析
        if (this.isAnalysisInProgress(document.uri.toString())) return false

        // 4. 检查文档是否符合分析条件（文件大小、类型等）
        return this.isDocumentEligibleForAnalysis(document)
    }

    // 检查是否应该触发AST+LSP分析
    shouldTriggerASTLSPAnalysis(document: vscode.TextDocument, position: vscode.Position): boolean {
        // 1. 检查输入位置是否在行中间
        if (!this.isPositionInLineMiddle(document, position)) return false

        // 2. 检查是否有足够的变更内容
        const diff = this.diffGenerator.generateIncrementalDiff(document.uri.toString(), [])
        if (!diff.isComplete || this.isMinimalChange(diff)) return false

        // 3. 检查是否正在进行分析
        if (this.isAnalysisInProgress(document.uri.toString(), AnalysisType.AST_LSP)) return false

        return true
    }

    // 检查是否应该触发autocomplete
    shouldTriggerAutocomplete(document: vscode.TextDocument, position: vscode.Position): boolean {
        return this.isPositionAtLineEnd(document, position)
    }
}
```

#### 防抖机制实现

```typescript
class DebounceManager {
    private timers: Map<string, NodeJS.Timeout> = new Map()
    private pendingCallbacks: Map<string, () => void> = new Map()

    debounce(key: string, callback: () => void, delayMs: number): void {
        // 取消之前的定时器
        const existingTimer = this.timers.get(key)
        if (existingTimer) {
            clearTimeout(existingTimer)
        }

        // 保存新的回调
        this.pendingCallbacks.set(key, callback)

        // 设置新的定时器
        const timer = setTimeout(() => {
            const pendingCallback = this.pendingCallbacks.get(key)
            if (pendingCallback) {
                pendingCallback()
                this.pendingCallbacks.delete(key)
            }
            this.timers.delete(key)
        }, delayMs)

        this.timers.set(key, timer)
    }

    cancel(key: string): void {
        const timer = this.timers.get(key)
        if (timer) {
            clearTimeout(timer)
            this.timers.delete(key)
        }
        this.pendingCallbacks.delete(key)
    }

    cancelAll(): void {
        this.timers.forEach(timer => clearTimeout(timer))
        this.timers.clear()
        this.pendingCallbacks.clear()
    }
}
```

### 增量分析实现

#### AST增量更新策略

```typescript
class IncrementalASTAnalyzer {
    async updateASTIncremental(
        documentUri: string,
        changes: vscode.TextDocumentChangeEvent[]
    ): Promise<void> {
        const cachedEntry = this.astCache.get(documentUri)
        if (!cachedEntry) {
            // 首次解析，全量构建AST
            await this.parseASTFull(documentUri)
            return
        }

        // 分析变更影响范围
        const affectedRanges = this.calculateAffectedRanges(changes)

        // 判断是否需要全量重新解析
        if (this.shouldReparse(affectedRanges, cachedEntry.ast)) {
            await this.parseASTFull(documentUri)
            return
        }

        // 增量更新AST
        for (const range of affectedRanges) {
            await this.updateASTRange(cachedEntry.ast, range, documentUri)
        }

        // 更新缓存元数据
        cachedEntry.lastUpdate = Date.now()
        cachedEntry.cacheHits++
    }

    private calculateAffectedRanges(changes: vscode.TextDocumentChangeEvent[]): vscode.Range[] {
        // 计算变更影响的语法范围，考虑语法结构边界
        return changes.map(change => {
            // 扩展范围以包含完整的语法结构
            return this.expandToSyntaxBoundary(change.range)
        })
    }
}
```

## Error Handling

### 错误处理策略

```typescript
// 错误类型定义
enum NextEditErrorType {
    MODEL_API_ERROR = 'model_api_error',
    LSP_CONNECTION_ERROR = 'lsp_connection_error',
    AST_PARSING_ERROR = 'ast_parsing_error',
    SUGGESTION_VALIDATION_ERROR = 'suggestion_validation_error',
    CACHE_ERROR = 'cache_error'
}

interface NextEditError {
    type: NextEditErrorType
    message: string
    documentUri?: string
    timestamp: number
    stack?: string
}

// 错误处理器
interface ErrorHandler {
    handleError(error: NextEditError): void
    shouldRetry(error: NextEditError): boolean
    getRetryDelay(error: NextEditError, attemptCount: number): number
}
```

### 错误恢复机制

1. **模型API错误**: 自动重试，降级到缓存建议
2. **LSP连接错误**: 重新连接，临时禁用AST+LSP分析
3. **AST解析错误**: 跳过当前文档，记录错误日志
4. **建议验证错误**: 过滤无效建议，继续处理其他建议
5. **缓存错误**: 清理缓存，重新构建

## Testing Strategy

### 单元测试

```typescript
// 测试覆盖的主要组件
describe('NextEditManager', () => {
    // 初始化和配置测试
    // 文档生命周期测试
    // 建议管理测试
})

describe('SuggestionManager', () => {
    // 建议存储和检索测试
    // 建议过滤和验证测试
})

describe('TriggerManager', () => {
    // 触发条件检查测试
    // 防抖机制测试
})

describe('AnalysisEngine', () => {
    // 模型分析测试
    // AST+LSP分析测试
    // 上下文收集测试
})
```

### 集成测试

```typescript
describe('NextEdit Integration', () => {
    // 端到端工作流测试
    // 与autocomplete集成测试
    // 性能测试
    // 并发处理测试
})
```

### 性能测试

1. **响应时间测试**: 确保AST+LSP分析在0.1秒内完成，模型推荐在5秒内完成
2. **内存使用测试**: 监控缓存大小和内存泄漏
3. **并发处理测试**: 测试多文档同时编辑的性能
4. **防抖效果测试**: 验证防抖机制的有效性

### 用户体验测试

1. **触发时机测试**: 验证与autocomplete的区分逻辑
2. **建议质量测试**: 评估建议的准确性和实用性
3. **界面响应测试**: 确保UI不会因为分析而卡顿
4. **错误处理测试**: 验证错误情况下的用户体验

## Implementation Notes

### 与现有系统的集成

1. **AutocompleteProvider集成**: 
   - 共享防抖机制配置
   - 互斥触发逻辑
   - 统一的API客户端

2. **TaskManager集成**:
   - 任务执行期间暂停next edit
   - 状态同步机制

3. **LSP服务集成**:
   - 复用现有LSP连接
   - 共享符号信息缓存

### 性能优化策略

1. **增量分析**: 只分析变更部分，避免全文档重新分析
2. **智能缓存**: 基于文档版本的缓存失效机制
3. **异步处理**: 所有分析操作异步执行，不阻塞UI
4. **资源管理**: 自动清理过期缓存和无用数据

### 扩展性考虑

1. **插件化架构**: 支持自定义分析器和建议生成器
2. **配置化**: 所有行为都可通过配置调整
3. **多语言支持**: 抽象的AST解析接口，支持不同编程语言
4. **API开放**: 提供API供其他扩展集成