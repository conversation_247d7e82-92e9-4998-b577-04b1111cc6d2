# Requirements Document

## Introduction

QAX Next Edit 是一个为 VSCode 扩展设计的智能代码编辑建议系统。该功能通过维护每个文档的建议数组，提供两种推荐模式：基于 AST+LSP 的快速语法推荐（0.1秒响应）和基于 AI 模型的深度分析推荐（5秒响应）。系统需要与现有的 autocomplete 功能区分触发时机，并提供增量分析能力。

## Requirements

### Requirement 1

**User Story:** 作为开发者，我希望系统能够维护每个文档的建议数组，以便跟踪和管理所有待执行的代码修改建议

#### Acceptance Criteria

1. WHEN 打开一个文档 THEN 系统 SHALL 为该文档初始化一个空的 suggest 数组
2. WHEN 文档内容发生变化 THEN 系统 SHALL 更新对应的 suggest 数组，过滤掉不再适用的建议
3. WHEN 建议被执行 THEN 系统 SHALL 从 suggest 数组中移除对应的建议项
4. when workspace关闭 then 系统 清除所有suggest

### Requirement 2

**User Story:** 作为开发者，我希望系统提供基于 AST+LSP 的快速语法推荐，以便在编辑过程中获得即时的代码建议

#### Acceptance Criteria

1. WHEN 用户输入结束1秒后没有新的输入动作 AND 输入位置处于一行中间 THEN 系统 SHALL 触发 AST+LSP 分析
2. WHEN 进行 AST+LSP 分析 THEN 系统 SHALL 生成完整的文件修改 diff，而不是单个字符的 diff
3. WHEN 能确定修改类型（如符号重命名、函数参数修改）THEN 系统 SHALL 通过 LSP 找到所有引用位置并构建建议
5. WHEN 无法确定修改类型 THEN 系统 SHALL 结束分析流程

### Requirement 3

**User Story:** 作为开发者，我希望系统提供基于 AI 模型的深度推荐，以便获得更全面的代码改进建议

#### Acceptance Criteria

1. WHEN 文档打开 AND 空闲3秒 AND 当前文档没有未执行推荐 THEN 系统 SHALL 触发模型推荐
2. WHEN 进行模型推荐 THEN 系统 SHALL 收集最近所有修改和对应文件名称，连同当前文件内容发送给模型
3. WHEN 调用模型 THEN 提示词 SHALL 包括要求对不完整的逻辑、未实现的函数、未调用的变量、格式问题、lint问题进行提示
4. WHEN 收到模型响应 THEN 系统 SHALL 解析出 suggest 数据并保存到对应文档的建议数组

### Requirement 4

**User Story:** 作为开发者，我希望系统能够与 autocomplete 功能区分触发时机，避免功能冲突和重复触发

#### Acceptance Criteria

1. WHEN 用户输入位置处于行末 THEN 系统 SHALL 触发 autocomplete 而不是 next edit
2. WHEN 用户输入位置处于行中间 THEN 系统 SHALL 触发 AST+LSP 分析而不是 autocomplete
3. WHEN 文档空闲3秒 AND 没有未执行推荐 THEN 系统 SHALL 触发模型推荐
4. WHEN 系统正在进行推荐分析 THEN 系统 SHALL 防止重复触发相同类型的分析

### Requirement 5

**User Story:** 作为开发者，我希望系统提供防抖机制，避免频繁的分析请求影响性能

#### Acceptance Criteria

1. WHEN 用户连续输入 THEN 系统 SHALL 等待输入结束1秒后再触发 AST+LSP 分析
2. WHEN 文档频繁变化 THEN 系统 SHALL 等待空闲3秒后再触发模型推荐
3. WHEN 正在进行分析 THEN 系统 SHALL 取消之前未完成的同类型分析请求
4. WHEN 用户继续输入 THEN 系统 SHALL 重置防抖计时器

### Requirement 6

**User Story:** 作为开发者，我希望系统提供增量分析能力，以提高分析效率和响应速度

#### Acceptance Criteria

1. WHEN 系统启动 THEN 系统 SHALL 建立 AST 和 LSP 缓存
2. WHEN 用户修改代码 THEN 系统 SHALL 根据修改增量更新缓存
3. WHEN 进行分析 THEN 系统 SHALL 利用缓存数据提高分析速度
4. WHEN 系统关闭 THEN 系统 SHALL 不进行缓存持久化，直接清理内存

### Requirement 7

**User Story:** 作为开发者，我希望建议以标准化的 JSON 格式存储，便于处理和展示

#### Acceptance Criteria

1. WHEN 生成建议 THEN 系统 SHALL 使用包含 type、description、location、patch 字段的 JSON 结构
2. WHEN 建议类型为 add、modify 或 delete THEN 系统 SHALL 在 type 字段中正确标识
3. WHEN 建议包含位置信息 THEN 系统 SHALL 提供 anchor 和 position 字段用于精确定位
4. WHEN 建议包含修改内容 THEN 系统 SHALL 在 patch 字段中提供 new_content
5. WHEN 建议包含描述 THEN 系统 SHALL 提供清晰的变更描述格式