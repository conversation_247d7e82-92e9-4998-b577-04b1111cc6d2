module.exports = {
	preset: "ts-jest",
	testEnvironment: "node",

	// 使用 moduleNameMapper 来 mock vscode 模块
	moduleNameMapper: {
		"^vscode$": "<rootDir>/test/__mocks__/vscode.js",
	},

	// 测试文件匹配模式
	testMatch: ["<rootDir>/test/**/*.test.ts"],

	// TypeScript 转换配置 - 使用新的配置格式
	transform: {
		"^.+\\.ts$": [
			"ts-jest",
			{
				tsconfig: {
					target: "es2020",
					module: "commonjs",
					moduleResolution: "node",
					esModuleInterop: true,
					allowSyntheticDefaultImports: true,
					skipLibCheck: true,
					strict: false,
				},
			},
		],
	},

	// 模块文件扩展名
	moduleFileExtensions: ["ts", "js", "json"],

	// 设置根目录
	rootDir: ".",

	// 忽略的路径
	testPathIgnorePatterns: ["/node_modules/", "/compiled/", "/test/__mocks__/"],

	// 收集覆盖率
	collectCoverage: false,

	// 设置超时时间
	testTimeout: 10000,

	// 详细输出
	verbose: true,

	// 最大并发数
	maxConcurrency: 1,

	// 强制退出
	forceExit: true,

	// 检测打开的句柄
	detectOpenHandles: true,
}
