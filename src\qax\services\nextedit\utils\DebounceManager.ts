/**
 * QAX Next Edit - 防抖管理器
 * 提供防抖功能，避免频繁触发分析请求
 */

/**
 * 防抖管理器
 */
export class DebounceManager {
	private timers: Map<string, NodeJS.Timeout> = new Map()
	private pendingCallbacks: Map<string, () => void> = new Map()

	/**
	 * 防抖执行
	 * @param key 唯一标识符
	 * @param callback 要执行的回调函数
	 * @param delayMs 延迟时间（毫秒）
	 */
	debounce(key: string, callback: () => void, delayMs: number): void {
		// 取消之前的定时器
		const existingTimer = this.timers.get(key)
		if (existingTimer) {
			clearTimeout(existingTimer)
		}

		// 保存新的回调
		this.pendingCallbacks.set(key, callback)

		// 设置新的定时器
		const timer = setTimeout(() => {
			const pendingCallback = this.pendingCallbacks.get(key)
			if (pendingCallback) {
				try {
					pendingCallback()
				} catch (error) {
					console.error(`Error executing debounced callback for key ${key}:`, error)
				} finally {
					this.pendingCallbacks.delete(key)
					this.timers.delete(key)
				}
			}
		}, delayMs)

		this.timers.set(key, timer)
	}

	/**
	 * 取消指定的防抖
	 * @param key 唯一标识符
	 */
	cancel(key: string): void {
		const timer = this.timers.get(key)
		if (timer) {
			clearTimeout(timer)
			this.timers.delete(key)
		}
		this.pendingCallbacks.delete(key)
	}

	/**
	 * 取消所有防抖
	 */
	cancelAll(): void {
		this.timers.forEach((timer) => clearTimeout(timer))
		this.timers.clear()
		this.pendingCallbacks.clear()
	}

	/**
	 * 检查是否有待处理的防抖
	 * @param key 唯一标识符
	 */
	hasPending(key: string): boolean {
		return this.timers.has(key)
	}

	/**
	 * 获取所有待处理的防抖键
	 */
	getPendingKeys(): string[] {
		return Array.from(this.timers.keys())
	}

	/**
	 * 获取待处理的防抖数量
	 */
	getPendingCount(): number {
		return this.timers.size
	}

	/**
	 * 立即执行指定的防抖回调
	 * @param key 唯一标识符
	 */
	flush(key: string): void {
		const timer = this.timers.get(key)
		const callback = this.pendingCallbacks.get(key)

		if (timer && callback) {
			clearTimeout(timer)
			this.timers.delete(key)
			this.pendingCallbacks.delete(key)

			try {
				callback()
			} catch (error) {
				console.error(`Error executing flushed callback for key ${key}:`, error)
			}
		}
	}

	/**
	 * 立即执行所有待处理的防抖回调
	 */
	flushAll(): void {
		const keys = Array.from(this.timers.keys())
		keys.forEach((key) => this.flush(key))
	}

	/**
	 * 销毁防抖管理器
	 */
	dispose(): void {
		this.cancelAll()
	}
}

/**
 * 创建防抖函数的工具函数
 * @param fn 要防抖的函数
 * @param delayMs 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function createDebouncedFunction<T extends (...args: any[]) => any>(
	fn: T,
	delayMs: number,
): T & { cancel: () => void; flush: () => void } {
	let timer: NodeJS.Timeout | null = null
	let lastArgs: Parameters<T>
	let lastThis: any

	const debouncedFn = function (this: any, ...args: Parameters<T>) {
		lastThis = this
		lastArgs = args

		if (timer) {
			clearTimeout(timer)
		}

		timer = setTimeout(() => {
			timer = null
			fn.apply(lastThis, lastArgs)
		}, delayMs)
	} as T & { cancel: () => void; flush: () => void }

	debouncedFn.cancel = () => {
		if (timer) {
			clearTimeout(timer)
			timer = null
		}
	}

	debouncedFn.flush = () => {
		if (timer) {
			clearTimeout(timer)
			timer = null
			fn.apply(lastThis, lastArgs)
		}
	}

	return debouncedFn
}

/**
 * 节流管理器
 * 与防抖不同，节流确保函数在指定时间间隔内最多执行一次
 */
export class ThrottleManager {
	private lastExecutionTimes: Map<string, number> = new Map()
	private timers: Map<string, NodeJS.Timeout> = new Map()

	/**
	 * 节流执行
	 * @param key 唯一标识符
	 * @param callback 要执行的回调函数
	 * @param intervalMs 时间间隔（毫秒）
	 * @param leading 是否在开始时立即执行
	 * @param trailing 是否在结束时执行
	 */
	throttle(key: string, callback: () => void, intervalMs: number, leading: boolean = true, trailing: boolean = true): void {
		const now = Date.now()
		const lastExecution = this.lastExecutionTimes.get(key) || 0
		const timeSinceLastExecution = now - lastExecution

		// 清除之前的尾随定时器
		const existingTimer = this.timers.get(key)
		if (existingTimer) {
			clearTimeout(existingTimer)
			this.timers.delete(key)
		}

		if (timeSinceLastExecution >= intervalMs) {
			// 可以立即执行
			if (leading) {
				this.executeCallback(key, callback, now)
			}
		} else if (trailing) {
			// 设置尾随执行
			const remainingTime = intervalMs - timeSinceLastExecution
			const timer = setTimeout(() => {
				this.executeCallback(key, callback, Date.now())
				this.timers.delete(key)
			}, remainingTime)
			this.timers.set(key, timer)
		}
	}

	/**
	 * 取消指定的节流
	 * @param key 唯一标识符
	 */
	cancel(key: string): void {
		const timer = this.timers.get(key)
		if (timer) {
			clearTimeout(timer)
			this.timers.delete(key)
		}
		this.lastExecutionTimes.delete(key)
	}

	/**
	 * 取消所有节流
	 */
	cancelAll(): void {
		this.timers.forEach((timer) => clearTimeout(timer))
		this.timers.clear()
		this.lastExecutionTimes.clear()
	}

	/**
	 * 销毁节流管理器
	 */
	dispose(): void {
		this.cancelAll()
	}

	private executeCallback(key: string, callback: () => void, timestamp: number): void {
		this.lastExecutionTimes.set(key, timestamp)
		try {
			callback()
		} catch (error) {
			console.error(`Error executing throttled callback for key ${key}:`, error)
		}
	}
}

/**
 * 创建节流函数的工具函数
 * @param fn 要节流的函数
 * @param intervalMs 时间间隔（毫秒）
 * @param leading 是否在开始时立即执行
 * @param trailing 是否在结束时执行
 * @returns 节流后的函数
 */
export function createThrottledFunction<T extends (...args: any[]) => any>(
	fn: T,
	intervalMs: number,
	leading: boolean = true,
	trailing: boolean = true,
): T & { cancel: () => void } {
	let lastExecutionTime = 0
	let timer: NodeJS.Timeout | null = null
	let lastArgs: Parameters<T>
	let lastThis: any

	const throttledFn = function (this: any, ...args: Parameters<T>) {
		lastThis = this
		lastArgs = args

		const now = Date.now()
		const timeSinceLastExecution = now - lastExecutionTime

		// 清除之前的尾随定时器
		if (timer) {
			clearTimeout(timer)
			timer = null
		}

		if (timeSinceLastExecution >= intervalMs) {
			// 可以立即执行
			if (leading) {
				lastExecutionTime = now
				fn.apply(lastThis, lastArgs)
			}
		} else if (trailing) {
			// 设置尾随执行
			const remainingTime = intervalMs - timeSinceLastExecution
			timer = setTimeout(() => {
				lastExecutionTime = Date.now()
				timer = null
				fn.apply(lastThis, lastArgs)
			}, remainingTime)
		}
	} as T & { cancel: () => void }

	throttledFn.cancel = () => {
		if (timer) {
			clearTimeout(timer)
			timer = null
		}
		lastExecutionTime = 0
	}

	return throttledFn
}
