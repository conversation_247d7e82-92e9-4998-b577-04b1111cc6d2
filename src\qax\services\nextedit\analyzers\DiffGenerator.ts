/**
 * QAX Next Edit - 差异生成器
 * 负责生成完整的文档差异（不是单字符diff），累积变更，生成语义完整的差异
 */

import * as vscode from "vscode"
import { DocumentDiff, DiffLine, DiffType, DiffMetadata, ChangeType } from "../types/AnalysisContext"

/**
 * 文档状态
 */
interface DocumentState {
	/** 文档内容 */
	content: string
	/** 行数组 */
	lines: string[]
	/** 版本号 */
	version: number
	/** 最后更新时间 */
	lastUpdate: number
}

/**
 * 增量差异生成器
 */
export class DiffGenerator {
	private documentStates = new Map<string, DocumentState>()

	/**
	 * 生成完整的文档差异（不是单字符diff）
	 */
	generateCompleteDiff(documentUri: string, changes: vscode.TextDocumentContentChangeEvent[]): DocumentDiff {
		const previousState = this.documentStates.get(documentUri)
		if (!previousState) {
			// 首次变更，建立基线
			return this.createBaselineDiff(changes)
		}

		// 累积变更，生成完整diff
		return this.accumulateChanges(previousState, changes)
	}

	/**
	 * 生成增量差异
	 */
	generateIncrementalDiff(documentUri: string, changes: vscode.TextDocumentContentChangeEvent[]): DocumentDiff {
		// 更新文档状态
		this.updateDocumentState(documentUri, changes)

		// 生成差异
		return this.generateCompleteDiff(documentUri, changes)
	}

	/**
	 * 更新文档状态
	 */
	updateDocumentState(documentUri: string, changes: vscode.TextDocumentContentChangeEvent[]): void {
		const document = vscode.workspace.textDocuments.find((doc) => doc.uri.toString() === documentUri)
		if (!document) return

		const state: DocumentState = {
			content: document.getText(),
			lines: document.getText().split("\n"),
			version: document.version,
			lastUpdate: Date.now(),
		}

		this.documentStates.set(documentUri, state)
	}

	/**
	 * 清理文档状态
	 */
	cleanupDocument(documentUri: string): void {
		this.documentStates.delete(documentUri)
	}

	/**
	 * 获取文档状态
	 */
	getDocumentState(documentUri: string): DocumentState | undefined {
		return this.documentStates.get(documentUri)
	}

	// 私有方法

	/**
	 * 创建基线差异
	 */
	private createBaselineDiff(changes: vscode.TextDocumentContentChangeEvent[]): DocumentDiff {
		const diff: DocumentDiff = {
			additions: [],
			deletions: [],
			modifications: [],
			isComplete: true,
			diffType: DiffType.UNKNOWN,
			affectedSymbols: [],
			contextLines: 3,
			metadata: {
				confidence: 0.9,
				complexity: 0,
				riskLevel: "low",
				estimatedImpact: [],
			},
			oldContent: "", // 基线时没有旧内容
			newContent: this.extractNewContentFromChanges(changes),
		}

		// 分析变更
		for (const change of changes) {
			this.analyzeChange(change, diff)
		}

		// 确定整体变更类型
		diff.diffType = this.determineOverallChangeType(diff)
		diff.metadata = this.calculateDiffMetadata(diff)

		return diff
	}

	/**
	 * 从变更中提取新内容
	 */
	private extractNewContentFromChanges(changes: vscode.TextDocumentContentChangeEvent[]): string {
		// 简化实现：返回所有变更的文本内容
		return changes.map((change) => change.text).join("\n")
	}

	/**
	 * 累积变更逻辑
	 */
	private accumulateChanges(previousState: DocumentState, newChanges: vscode.TextDocumentContentChangeEvent[]): DocumentDiff {
		const diff: DocumentDiff = {
			additions: [],
			deletions: [],
			modifications: [],
			isComplete: true,
			diffType: DiffType.UNKNOWN,
			affectedSymbols: [],
			contextLines: 3,
			metadata: {
				confidence: 0.9,
				complexity: 0,
				riskLevel: "low",
				estimatedImpact: [],
			},
			oldContent: previousState.content || "",
			newContent: this.extractNewContentFromChanges(newChanges),
		}

		// 分析每个变更的语义影响
		for (const change of newChanges) {
			this.analyzeChangeImpact(change, diff, previousState)
		}

		// 确定整体变更类型
		diff.diffType = this.determineOverallChangeType(diff)
		diff.metadata = this.calculateDiffMetadata(diff)

		return diff
	}

	/**
	 * 分析变更
	 */
	private analyzeChange(change: vscode.TextDocumentContentChangeEvent, diff: DocumentDiff): void {
		const changeLines = change.text.split("\n")
		const rangeLines = change.range.end.line - change.range.start.line + 1

		if (change.text.length === 0) {
			// 删除操作
			for (let i = 0; i < rangeLines; i++) {
				diff.deletions.push({
					lineNumber: change.range.start.line + i,
					content: "", // 无法获取原内容，需要从文档状态获取
					type: "removed",
					indentLevel: 0,
				})
			}
		} else if (rangeLines === 1 && changeLines.length === 1) {
			// 修改操作
			diff.modifications.push({
				lineNumber: change.range.start.line,
				content: changeLines[0],
				type: "modified",
				indentLevel: this.calculateIndentLevel(changeLines[0]),
			})
		} else {
			// 添加操作
			changeLines.forEach((line, index) => {
				diff.additions.push({
					lineNumber: change.range.start.line + index,
					content: line,
					type: "added",
					indentLevel: this.calculateIndentLevel(line),
				})
			})
		}
	}

	/**
	 * 分析变更影响
	 */
	private analyzeChangeImpact(change: vscode.TextDocumentContentChangeEvent, diff: DocumentDiff, state: DocumentState): void {
		const changeLines = change.text.split("\n")
		const rangeLines = change.range.end.line - change.range.start.line + 1

		if (change.text.length === 0) {
			// 删除操作
			for (let i = 0; i < rangeLines; i++) {
				const lineNumber = change.range.start.line + i
				const originalContent = this.getLineContent(state, lineNumber)
				diff.deletions.push({
					lineNumber,
					content: originalContent,
					type: "removed",
					indentLevel: this.calculateIndentLevel(originalContent),
					syntaxType: this.detectSyntaxType(originalContent),
				})
			}
		} else if (rangeLines === 1 && changeLines.length === 1) {
			// 修改操作
			const lineNumber = change.range.start.line
			const originalContent = this.getLineContent(state, lineNumber)
			diff.modifications.push({
				lineNumber,
				content: changeLines[0],
				type: "modified",
				indentLevel: this.calculateIndentLevel(changeLines[0]),
				syntaxType: this.detectSyntaxType(changeLines[0]),
			})
		} else {
			// 添加操作
			changeLines.forEach((line, index) => {
				diff.additions.push({
					lineNumber: change.range.start.line + index,
					content: line,
					type: "added",
					indentLevel: this.calculateIndentLevel(line),
					syntaxType: this.detectSyntaxType(line),
				})
			})
		}

		// 提取受影响的符号
		this.extractAffectedSymbols(change, diff)
	}

	/**
	 * 获取行内容
	 */
	private getLineContent(state: DocumentState, lineNumber: number): string {
		return state.lines[lineNumber] || ""
	}

	/**
	 * 计算缩进级别
	 */
	private calculateIndentLevel(line: string): number {
		const match = line.match(/^(\s*)/)
		return match ? match[1].length : 0
	}

	/**
	 * 检测语法类型
	 */
	private detectSyntaxType(line: string): string {
		const trimmed = line.trim()

		if (trimmed.startsWith("//") || trimmed.startsWith("/*") || trimmed.startsWith("#")) {
			return "comment"
		}

		if (trimmed.startsWith("import ") || trimmed.startsWith("from ") || trimmed.startsWith("#include")) {
			return "import"
		}

		if (trimmed.includes("function ") || trimmed.includes("def ") || trimmed.includes("fn ")) {
			return "function"
		}

		if (trimmed.includes("class ") || trimmed.includes("interface ") || trimmed.includes("struct ")) {
			return "class"
		}

		if (trimmed.includes("var ") || trimmed.includes("let ") || trimmed.includes("const ")) {
			return "variable"
		}

		return "statement"
	}

	/**
	 * 提取受影响的符号
	 */
	private extractAffectedSymbols(change: vscode.TextDocumentContentChangeEvent, diff: DocumentDiff): void {
		const text = change.text

		// 简单的符号提取逻辑
		const symbolPatterns = [
			/\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g, // 函数调用
			/\b([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g, // 变量赋值
			/class\s+([a-zA-Z_][a-zA-Z0-9_]*)/g, // 类定义
			/function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g, // 函数定义
		]

		for (const pattern of symbolPatterns) {
			let match
			while ((match = pattern.exec(text)) !== null) {
				const symbol = match[1]
				if (!diff.affectedSymbols.includes(symbol)) {
					diff.affectedSymbols.push(symbol)
				}
			}
		}
	}

	/**
	 * 确定整体变更类型
	 */
	private determineOverallChangeType(diff: DocumentDiff): DiffType {
		// 基于变更内容分析变更类型
		const hasAdditions = diff.additions.length > 0
		const hasDeletions = diff.deletions.length > 0
		const hasModifications = diff.modifications.length > 0

		// 检查是否为符号重命名
		if (this.isSymbolRename(diff)) {
			return DiffType.SYMBOL_RENAME
		}

		// 检查是否为函数签名变更
		if (this.isFunctionSignatureChange(diff)) {
			return DiffType.FUNCTION_SIGNATURE
		}

		// 检查是否为导入语句变更
		if (this.isImportChange(diff)) {
			return DiffType.IMPORT_STATEMENT
		}

		// 检查是否为格式变更
		if (this.isFormattingChange(diff)) {
			return DiffType.FORMATTING
		}

		// 默认类型判断
		if (hasAdditions && !hasDeletions && !hasModifications) {
			return DiffType.BLOCK_RESTRUCTURE
		}

		if (hasDeletions && !hasAdditions && !hasModifications) {
			return DiffType.BLOCK_RESTRUCTURE
		}

		return DiffType.UNKNOWN
	}

	/**
	 * 检查是否为符号重命名
	 */
	private isSymbolRename(diff: DocumentDiff): boolean {
		// 简单检查：有删除和添加，且涉及相同的符号模式
		return diff.deletions.length > 0 && diff.additions.length > 0 && diff.affectedSymbols.length > 0
	}

	/**
	 * 检查是否为函数签名变更
	 */
	private isFunctionSignatureChange(diff: DocumentDiff): boolean {
		const allLines = [...diff.additions, ...diff.deletions, ...diff.modifications]
		return allLines.some(
			(line) =>
				line.syntaxType === "function" ||
				line.content.includes("function ") ||
				line.content.includes("def ") ||
				line.content.includes("fn "),
		)
	}

	/**
	 * 检查是否为导入变更
	 */
	private isImportChange(diff: DocumentDiff): boolean {
		const allLines = [...diff.additions, ...diff.deletions, ...diff.modifications]
		return allLines.some((line) => line.syntaxType === "import")
	}

	/**
	 * 检查是否为格式变更
	 */
	private isFormattingChange(diff: DocumentDiff): boolean {
		// 如果只有缩进级别变化，可能是格式变更
		const allLines = [...diff.additions, ...diff.deletions, ...diff.modifications]
		return allLines.every((line) => {
			const trimmed = line.content.trim()
			return (
				trimmed.length === 0 || // 空行
				line.content !== trimmed
			) // 只有空白字符变化
		})
	}

	/**
	 * 计算差异元数据
	 */
	private calculateDiffMetadata(diff: DocumentDiff): DiffMetadata {
		const totalChanges = diff.additions.length + diff.deletions.length + diff.modifications.length

		let complexity = 0
		if (totalChanges > 10) complexity += 0.3
		if (diff.affectedSymbols.length > 5) complexity += 0.3
		if (diff.diffType === DiffType.FUNCTION_SIGNATURE) complexity += 0.4

		let riskLevel: "low" | "medium" | "high" = "low"
		if (complexity > 0.6) riskLevel = "high"
		else if (complexity > 0.3) riskLevel = "medium"

		return {
			confidence: Math.max(0.5, 1 - complexity),
			complexity,
			riskLevel,
			estimatedImpact: this.estimateImpact(diff),
		}
	}

	/**
	 * 估算影响范围
	 */
	private estimateImpact(diff: DocumentDiff): string[] {
		const impact: string[] = []

		if (diff.affectedSymbols.length > 0) {
			impact.push(`Affects ${diff.affectedSymbols.length} symbols`)
		}

		if (diff.diffType === DiffType.FUNCTION_SIGNATURE) {
			impact.push("May require updating function calls")
		}

		if (diff.diffType === DiffType.SYMBOL_RENAME) {
			impact.push("May require updating symbol references")
		}

		if (diff.diffType === DiffType.IMPORT_STATEMENT) {
			impact.push("May affect module dependencies")
		}

		return impact
	}
}
