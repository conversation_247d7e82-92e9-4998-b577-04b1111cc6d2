/**
 * QAX Next Edit - AST+LSP分析器
 * 负责快速的语法级别分析，目标响应时间0.1秒
 */

import * as vscode from "vscode"
import { ASTLSPAnalysisContext, ChangeType, ASTNode, SymbolInfo, DocumentDiff } from "../types/AnalysisContext"
import { Suggestion, SuggestionBuilder, SuggestionSource, SuggestionCategory, ImpactLevel } from "../types/Suggestion"
import { NextEditSettings } from "../types/NextEditSettings"
// TreeSitter 服务接口定义（避免直接导入）
interface TreeSitterService {
	getAst(filepath: string, content: string): Promise<any>
	getTreePathAtCursor(ast: any, cursorIndex: number): Promise<any[]>
	getCachedParser(extension: string): any
	getParserForFile(filepath: string): any
}

/**
 * TreeSitter 服务适配器
 * 将现有的 TreeSitter 服务适配到 QAX Next Edit 系统
 */
class TreeSitterAdapter {
	private treeSitterService: TreeSitterService | null = null

	constructor() {
		this.initializeService()
	}

	/**
	 * 初始化 TreeSitter 服务
	 */
	private initializeService(): void {
		try {
			// 尝试动态导入 TreeSitter 服务
			// 在实际环境中，这里会导入真实的服务
			// const { getAst, getTreePathAtCursor, getCachedParser, getParserForFile } = require('../../../services/tree-sitter/languageParser')

			// 模拟服务（用于开发和测试）
			this.treeSitterService = {
				getAst: async (filepath: string, content: string) => {
					// 模拟 AST 解析
					return {
						rootNode: {
							type: "program",
							startIndex: 0,
							endIndex: content.length,
							startPosition: { row: 0, column: 0 },
							endPosition: { row: content.split("\n").length - 1, column: 0 },
							children: [],
							namedChildren: [],
							text: content,
						},
					}
				},
				getTreePathAtCursor: async (ast: any, cursorIndex: number) => {
					return [ast.rootNode]
				},
				getCachedParser: (extension: string) => {
					return {
						query: {
							captures: () => [],
						},
					}
				},
				getParserForFile: (filepath: string) => null,
			}
		} catch (error) {
			console.warn("TreeSitter service not available, using mock implementation:", error.message)
		}
	}

	/**
	 * 解析文件内容为 AST
	 */
	async parseFile(filepath: string, content: string): Promise<ASTNode | null> {
		try {
			if (!this.treeSitterService) {
				return null
			}

			const ast = await this.treeSitterService.getAst(filepath, content)
			if (!ast) {
				return null
			}

			return this.convertTreeSitterASTToASTNode(ast.rootNode)
		} catch (error) {
			console.error("TreeSitter parsing error:", error)
			return null
		}
	}

	/**
	 * 获取光标位置的 AST 路径
	 */
	async getASTPathAtPosition(filepath: string, content: string, position: vscode.Position): Promise<ASTNode[]> {
		try {
			if (!this.treeSitterService) {
				return []
			}

			const ast = await this.treeSitterService.getAst(filepath, content)
			if (!ast) {
				return []
			}

			// 计算光标在文件中的字符索引
			const lines = content.split("\n")
			let cursorIndex = 0
			for (let i = 0; i < position.line; i++) {
				cursorIndex += lines[i].length + 1 // +1 for newline
			}
			cursorIndex += position.character

			const path = await this.treeSitterService.getTreePathAtCursor(ast, cursorIndex)
			return path.map((node: any) => this.convertTreeSitterASTToASTNode(node))
		} catch (error) {
			console.error("TreeSitter path error:", error)
			return []
		}
	}

	/**
	 * 查找符号定义
	 */
	async findSymbolDefinitions(filepath: string, content: string, symbolName: string): Promise<ASTNode[]> {
		try {
			if (!this.treeSitterService) {
				return []
			}

			const ast = await this.treeSitterService.getAst(filepath, content)
			if (!ast) {
				return []
			}

			const cached = this.treeSitterService.getCachedParser(this.getFileExtension(filepath))
			if (!cached) {
				return []
			}

			// 使用定义查询查找符号
			const captures = cached.query.captures(ast.rootNode)
			const definitions: ASTNode[] = []

			for (const capture of captures) {
				if (capture.name.includes("name.definition") && capture.node.text === symbolName) {
					definitions.push(this.convertTreeSitterASTToASTNode(capture.node))
				}
			}

			return definitions
		} catch (error) {
			console.error("TreeSitter symbol search error:", error)
			return []
		}
	}

	/**
	 * 查找符号引用
	 */
	async findSymbolReferences(filepath: string, content: string, symbolName: string): Promise<ASTNode[]> {
		try {
			if (!this.treeSitterService) {
				return []
			}

			const ast = await this.treeSitterService.getAst(filepath, content)
			if (!ast) {
				return []
			}

			const cached = this.treeSitterService.getCachedParser(this.getFileExtension(filepath))
			if (!cached) {
				return []
			}

			// 使用引用查询查找符号
			const captures = cached.query.captures(ast.rootNode)
			const references: ASTNode[] = []

			for (const capture of captures) {
				if (capture.name.includes("name.reference") && capture.node.text === symbolName) {
					references.push(this.convertTreeSitterASTToASTNode(capture.node))
				}
			}

			return references
		} catch (error) {
			console.error("TreeSitter reference search error:", error)
			return []
		}
	}

	/**
	 * 检测变更类型
	 */
	async detectChangeType(filepath: string, oldContent: string, newContent: string): Promise<ChangeType | null> {
		try {
			if (!this.treeSitterService) {
				// 降级到文本分析
				return this.detectChangeTypeFromText(oldContent, newContent)
			}

			const oldAst = await this.treeSitterService.getAst(filepath, oldContent)
			const newAst = await this.treeSitterService.getAst(filepath, newContent)

			if (!oldAst || !newAst) {
				return this.detectChangeTypeFromText(oldContent, newContent)
			}

			// 简单的变更检测逻辑
			const oldText = oldAst.rootNode.text
			const newText = newAst.rootNode.text

			if (this.isSymbolRename(oldText, newText)) {
				return ChangeType.SYMBOL_RENAME
			}

			if (this.isFunctionSignatureChange(oldText, newText)) {
				return ChangeType.FUNCTION_SIGNATURE_CHANGE
			}

			if (this.isVariableDeclaration(oldText, newText)) {
				return ChangeType.VARIABLE_DECLARATION
			}

			if (this.isImportChange(oldText, newText)) {
				return ChangeType.IMPORT_CHANGE
			}

			return null
		} catch (error) {
			console.error("TreeSitter change detection error:", error)
			return this.detectChangeTypeFromText(oldContent, newContent)
		}
	}

	/**
	 * 从文本检测变更类型（降级方法）
	 */
	private detectChangeTypeFromText(oldContent: string, newContent: string): ChangeType | null {
		if (this.isSymbolRename(oldContent, newContent)) {
			return ChangeType.SYMBOL_RENAME
		}

		if (this.isFunctionSignatureChange(oldContent, newContent)) {
			return ChangeType.FUNCTION_SIGNATURE_CHANGE
		}

		if (this.isVariableDeclaration(oldContent, newContent)) {
			return ChangeType.VARIABLE_DECLARATION
		}

		if (this.isImportChange(oldContent, newContent)) {
			return ChangeType.IMPORT_CHANGE
		}

		return null
	}

	/**
	 * 将 TreeSitter AST 节点转换为我们的 ASTNode 格式
	 */
	private convertTreeSitterASTToASTNode(node: any): ASTNode {
		return {
			type: node.type,
			range: [node.startIndex, node.endIndex],
			startPosition: {
				row: node.startPosition.row,
				column: node.startPosition.column,
			},
			endPosition: {
				row: node.endPosition.row,
				column: node.endPosition.column,
			},
			children: node.children ? node.children.map((child: any) => this.convertTreeSitterASTToASTNode(child)) : [],
			namedChildren: node.namedChildren
				? node.namedChildren.map((child: any) => this.convertTreeSitterASTToASTNode(child))
				: [],
			text: node.text,
		}
	}

	/**
	 * 获取文件扩展名
	 */
	private getFileExtension(filepath: string): string {
		return filepath.split(".").pop()?.toLowerCase() || ""
	}

	/**
	 * 检测是否是符号重命名
	 */
	private isSymbolRename(oldText: string, newText: string): boolean {
		// 简化的检测逻辑
		const oldIdentifiers = this.extractIdentifiers(oldText)
		const newIdentifiers = this.extractIdentifiers(newText)

		return oldIdentifiers.length === newIdentifiers.length && oldIdentifiers.some((id) => !newIdentifiers.includes(id))
	}

	/**
	 * 检测是否是函数签名变更
	 */
	private isFunctionSignatureChange(oldText: string, newText: string): boolean {
		const functionRegex = /function\s+\w+\s*\([^)]*\)/g
		const oldFunctions: string[] = oldText.match(functionRegex) || []
		const newFunctions: string[] = newText.match(functionRegex) || []

		return oldFunctions.length === newFunctions.length && oldFunctions.some((func) => !newFunctions.includes(func))
	}

	/**
	 * 检测是否是变量声明
	 */
	private isVariableDeclaration(oldText: string, newText: string): boolean {
		const varRegex = /(const|let|var)\s+\w+/g
		const oldVars = oldText.match(varRegex) || []
		const newVars = newText.match(varRegex) || []

		return newVars.length > oldVars.length
	}

	/**
	 * 检测是否是导入变更
	 */
	private isImportChange(oldText: string, newText: string): boolean {
		const importRegex = /import\s+.*from\s+['"][^'"]+['"]/g
		const oldImports: string[] = oldText.match(importRegex) || []
		const newImports: string[] = newText.match(importRegex) || []

		return oldImports.length !== newImports.length || oldImports.some((imp) => !newImports.includes(imp))
	}

	/**
	 * 提取标识符
	 */
	private extractIdentifiers(text: string): string[] {
		const identifierRegex = /\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g
		return text.match(identifierRegex) || []
	}
}

/**
 * AST缓存条目
 */
interface ASTCacheEntry {
	/** AST根节点 */
	ast: ASTNode
	/** 文档版本 */
	documentVersion: number
	/** 最后更新时间 */
	lastUpdate: number
	/** 缓存命中次数 */
	cacheHits: number
	/** 文档URI */
	documentUri: string
}

/**
 * LSP缓存条目
 */
interface LSPCacheEntry {
	/** 符号引用 */
	references: Map<string, vscode.Location[]>
	/** 符号定义 */
	definitions: Map<string, vscode.Location>
	/** 诊断信息 */
	diagnostics: vscode.Diagnostic[]
	/** 最后更新时间 */
	lastUpdate: number
	/** 文档版本 */
	documentVersion: number
}

/**
 * AST+LSP分析器
 */
export class ASTLSPAnalyzer {
	private settings: NextEditSettings
	private astCache = new Map<string, ASTCacheEntry>()
	private lspCache = new Map<string, LSPCacheEntry>()
	private treeSitterService: TreeSitterAdapter

	constructor(settings: NextEditSettings) {
		this.settings = settings
		this.treeSitterService = new TreeSitterAdapter()
		this.initializeTreeSitterService()
	}

	/**
	 * 更新配置
	 */
	updateSettings(settings: NextEditSettings): void {
		this.settings = settings
	}

	/**
	 * 执行快速分析
	 */
	async performFastAnalysis(context: ASTLSPAnalysisContext): Promise<Suggestion[]> {
		const startTime = performance.now()

		try {
			// 1. 快速检测变更类型（目标：10ms）
			const changeType = await this.detectChangeTypeQuick(context.diff, context.document)
			if (!changeType) {
				return []
			}

			// 2. 增量AST解析（目标：30ms）
			const astNode = await this.getASTNodeIncremental(context.document, context.position)

			// 3. LSP查询优化（目标：50ms）
			const references = await this.findReferencesOptimized(astNode, context.document)

			// 4. 建议生成（目标：10ms）
			const suggestions = await this.generateSuggestionsQuick(changeType, astNode, references, context)

			const duration = performance.now() - startTime
			console.log(`AST+LSP analysis completed in ${duration.toFixed(2)}ms`)

			return suggestions
		} catch (error) {
			console.error("AST+LSP analysis error:", error)
			return []
		}
	}

	/**
	 * 增量更新AST
	 */
	async updateASTIncremental(documentUri: string, changes: vscode.TextDocumentContentChangeEvent[]): Promise<void> {
		const cached = this.astCache.get(documentUri)
		if (!cached) {
			// 首次解析，全量构建AST
			await this.parseASTFull(documentUri)
			return
		}

		// 分析变更影响范围
		const affectedRanges = this.calculateAffectedRanges(changes)

		// 判断是否需要全量重新解析
		if (this.shouldReparse(affectedRanges, cached.ast)) {
			await this.parseASTFull(documentUri)
			return
		}

		// 增量更新AST
		for (const range of affectedRanges) {
			await this.updateASTRange(cached.ast, range, documentUri)
		}

		// 更新缓存元数据
		cached.lastUpdate = Date.now()
		cached.cacheHits++
	}

	/**
	 * 查找所有引用
	 */
	async findAllReferences(symbol: string, document: vscode.TextDocument): Promise<vscode.Location[]> {
		const documentUri = document.uri.toString()
		const cached = this.lspCache.get(documentUri)

		// 检查缓存
		if (cached && cached.references.has(symbol)) {
			return cached.references.get(symbol)!
		}

		try {
			// 调用LSP查找引用
			const references =
				(await vscode.commands.executeCommand<vscode.Location[]>(
					"vscode.executeReferenceProvider",
					document.uri,
					this.findSymbolPosition(symbol, document),
				)) || []

			// 更新缓存
			this.updateLSPCache(documentUri, symbol, references, "references")

			return references
		} catch (error) {
			console.error("Error finding references:", error)
			return []
		}
	}

	/**
	 * 查找定义
	 */
	async findDefinition(symbol: string, document: vscode.TextDocument): Promise<vscode.Location | null> {
		const documentUri = document.uri.toString()
		const cached = this.lspCache.get(documentUri)

		// 检查缓存
		if (cached && cached.definitions.has(symbol)) {
			return cached.definitions.get(symbol)!
		}

		try {
			// 调用LSP查找定义
			const definitions =
				(await vscode.commands.executeCommand<vscode.Location[]>(
					"vscode.executeDefinitionProvider",
					document.uri,
					this.findSymbolPosition(symbol, document),
				)) || []

			const definition = definitions[0] || null

			// 更新缓存
			if (definition) {
				this.updateLSPCache(documentUri, symbol, definition, "definition")
			}

			return definition
		} catch (error) {
			console.error("Error finding definition:", error)
			return null
		}
	}

	/**
	 * 生成重命名建议
	 */
	async generateRenameSuggestions(oldSymbol: string, newSymbol: string, document: vscode.TextDocument): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		try {
			// 查找所有引用
			const references = await this.findAllReferences(oldSymbol, document)

			for (const reference of references) {
				const suggestion = SuggestionBuilder.create(
					"modify",
					`${oldSymbol} -> ${newSymbol}`,
					this.createAnchorFromLocation(reference, document),
					"replace",
					newSymbol,
					{
						confidence: 0.95,
						source: SuggestionSource.AST_LSP,
						category: SuggestionCategory.REFACTORING,
						impact: ImpactLevel.MEDIUM,
						tags: ["rename", "refactor"],
					},
				)

				suggestions.push(suggestion)
			}
		} catch (error) {
			console.error("Error generating rename suggestions:", error)
		}

		return suggestions
	}

	/**
	 * 生成函数签名变更建议
	 */
	async generateSignatureChangeSuggestions(
		functionName: string,
		oldSignature: string,
		newSignature: string,
		document: vscode.TextDocument,
	): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		try {
			// 查找函数调用
			const references = await this.findAllReferences(functionName, document)

			for (const reference of references) {
				// 检查是否是函数调用
				if (await this.isFunctionCall(reference, document)) {
					const suggestion = SuggestionBuilder.create(
						"modify",
						`Update function call signature`,
						this.createAnchorFromLocation(reference, document),
						"replace",
						this.generateUpdatedFunctionCall(oldSignature, newSignature),
						{
							confidence: 0.85,
							source: SuggestionSource.AST_LSP,
							category: SuggestionCategory.FUNCTION_IMPLEMENTATION,
							impact: ImpactLevel.HIGH,
							tags: ["function-signature", "api-change"],
						},
					)

					suggestions.push(suggestion)
				}
			}
		} catch (error) {
			console.error("Error generating signature change suggestions:", error)
		}

		return suggestions
	}

	/**
	 * 清理缓存
	 */
	clearCache(documentUri?: string): void {
		if (documentUri) {
			this.astCache.delete(documentUri)
			this.lspCache.delete(documentUri)
		} else {
			this.astCache.clear()
			this.lspCache.clear()
		}
	}

	/**
	 * 获取缓存统计
	 */
	getCacheStats(): { ast: number; lsp: number; totalMemory: number } {
		return {
			ast: this.astCache.size,
			lsp: this.lspCache.size,
			totalMemory: this.estimateCacheMemoryUsage(),
		}
	}

	/**
	 * 销毁分析器
	 */
	dispose(): void {
		this.clearCache()
	}

	// 私有方法

	/**
	 * 初始化TreeSitter服务
	 */
	private initializeTreeSitterService(): void {
		// 集成现有的TreeSitter服务
		this.treeSitterService = new TreeSitterAdapter()
	}

	/**
	 * 快速检测变更类型
	 */
	private async detectChangeTypeQuick(diff: DocumentDiff, document: vscode.TextDocument): Promise<ChangeType | null> {
		try {
			// 使用 TreeSitter 进行精确的变更检测
			const oldContent = diff.oldContent || ""
			const newContent = document.getText()

			const changeType = await this.treeSitterService.detectChangeType(document.uri.toString(), oldContent, newContent)

			return changeType
		} catch (error) {
			console.error("Change type detection error:", error)

			// 降级到启发式规则
			if (this.isSymbolRename(diff)) {
				return ChangeType.SYMBOL_RENAME
			}

			if (this.isFunctionSignatureChange(diff)) {
				return ChangeType.FUNCTION_SIGNATURE_CHANGE
			}

			if (this.isVariableDeclaration(diff)) {
				return ChangeType.VARIABLE_DECLARATION
			}

			if (this.isImportChange(diff)) {
				return ChangeType.IMPORT_CHANGE
			}

			return null
		}
	}

	/**
	 * 增量获取AST节点
	 */
	private async getASTNodeIncremental(document: vscode.TextDocument, position: vscode.Position): Promise<ASTNode | null> {
		const cacheKey = document.uri.toString()
		const cached = this.astCache.get(cacheKey)

		if (cached && cached.documentVersion === document.version) {
			// 缓存命中，使用 TreeSitter 获取位置节点
			cached.cacheHits++
			return await this.getNodeAtPositionFromTreeSitter(document, position)
		}

		// 重新解析AST
		await this.parseASTFull(cacheKey)

		// 获取位置节点
		return await this.getNodeAtPositionFromTreeSitter(document, position)
	}

	/**
	 * 使用 TreeSitter 获取位置处的节点
	 */
	private async getNodeAtPositionFromTreeSitter(
		document: vscode.TextDocument,
		position: vscode.Position,
	): Promise<ASTNode | null> {
		try {
			const content = document.getText()
			const path = await this.treeSitterService.getASTPathAtPosition(document.uri.toString(), content, position)

			// 返回最具体的节点（路径中的最后一个）
			return path.length > 0 ? path[path.length - 1] : null
		} catch (error) {
			console.error("Error getting node at position:", error)
			return null
		}
	}

	/**
	 * 优化的引用查找
	 */
	private async findReferencesOptimized(astNode: ASTNode | null, document: vscode.TextDocument): Promise<vscode.Location[]> {
		if (!astNode) {
			return []
		}

		// 从AST节点提取符号名
		const symbolName = this.extractSymbolName(astNode)
		if (!symbolName) {
			return []
		}

		try {
			// 首先尝试使用 TreeSitter 查找引用
			const content = document.getText()
			const references = await this.treeSitterService.findSymbolReferences(document.uri.toString(), content, symbolName)

			// 将 ASTNode 转换为 vscode.Location
			const locations: vscode.Location[] = []
			for (const ref of references) {
				const location = new vscode.Location(
					document.uri,
					new vscode.Range(
						ref.startPosition.row,
						ref.startPosition.column,
						ref.endPosition.row,
						ref.endPosition.column,
					),
				)
				locations.push(location)
			}

			return locations
		} catch (error) {
			console.error("TreeSitter reference search error:", error)

			// 降级到 LSP 查找
			return await this.findAllReferences(symbolName, document)
		}
	}

	/**
	 * 快速生成建议
	 */
	private async generateSuggestionsQuick(
		changeType: ChangeType,
		_astNode: ASTNode | null,
		_references: vscode.Location[],
		_context: ASTLSPAnalysisContext,
	): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		switch (changeType) {
			case ChangeType.SYMBOL_RENAME:
				suggestions.push(...(await this.generateSymbolRenameSuggestions(_astNode, _references, _context)))
				break

			case ChangeType.FUNCTION_SIGNATURE_CHANGE:
				suggestions.push(...(await this.generateFunctionSignatureSuggestions(_astNode, _references, _context)))
				break

			case ChangeType.VARIABLE_DECLARATION:
				suggestions.push(...(await this.generateVariableDeclarationSuggestions(_astNode, _references, _context)))
				break

			case ChangeType.IMPORT_CHANGE:
				suggestions.push(...(await this.generateImportChangeSuggestions(_astNode, _references, _context)))
				break
		}

		return suggestions
	}

	/**
	 * 全量解析AST
	 */
	private async parseASTFull(documentUri: string): Promise<void> {
		try {
			const document = vscode.workspace.textDocuments.find((doc) => doc.uri.toString() === documentUri)
			if (!document) {
				console.error("Document not found for AST parsing:", documentUri)
				return
			}

			const content = document.getText()
			const ast = await this.treeSitterService.parseFile(documentUri, content)

			if (ast) {
				this.astCache.set(documentUri, {
					ast,
					documentVersion: document.version,
					lastUpdate: Date.now(),
					cacheHits: 0,
					documentUri,
				})

				console.log(`AST parsed successfully for ${documentUri}`)
			} else {
				console.warn(`Failed to parse AST for ${documentUri}`)
			}
		} catch (error) {
			console.error("AST parsing error:", error)
		}
	}

	private calculateAffectedRanges(changes: vscode.TextDocumentContentChangeEvent[]): vscode.Range[] {
		return changes.map((change) => change.range).filter((range) => range !== undefined) as vscode.Range[]
	}

	private shouldReparse(affectedRanges: vscode.Range[], _ast: ASTNode): boolean {
		// 简单策略：如果影响范围太大，重新解析
		return affectedRanges.length > 5
	}

	private async updateASTRange(ast: ASTNode, range: vscode.Range, documentUri: string): Promise<void> {
		// 实现AST范围更新
		try {
			// 获取文档
			const document = await vscode.workspace.openTextDocument(vscode.Uri.parse(documentUri))

			// 获取变更范围的文本
			const changedText = document.getText(range)

			// 重新解析变更的部分
			const partialAST = await this.treeSitterService.parseFile(documentUri, changedText)

			if (partialAST) {
				// 更新 AST 缓存中的相应部分
				this.updateASTNodeRange(ast, range, partialAST)

				// 更新缓存
				const cacheKey = documentUri
				const cacheEntry = this.astCache.get(cacheKey)
				if (cacheEntry) {
					cacheEntry.ast = ast
					cacheEntry.lastUpdate = Date.now()
					cacheEntry.documentVersion = document.version
				}
			}
		} catch (error) {
			console.error("Failed to update AST range:", error)
			// 如果部分更新失败，标记需要完全重新解析
			this.astCache.delete(documentUri)
		}
	}

	private updateASTNodeRange(ast: ASTNode, range: vscode.Range, newNode: ASTNode): void {
		// 递归查找并更新指定范围内的节点
		if (this.nodeIntersectsRange(ast, range)) {
			// 如果当前节点完全在范围内，替换它
			if (this.nodeWithinRange(ast, range)) {
				// 替换节点内容
				Object.assign(ast, newNode)
				return
			}

			// 递归更新子节点
			for (let i = 0; i < ast.namedChildren.length; i++) {
				const child = ast.namedChildren[i]
				if (this.nodeIntersectsRange(child, range)) {
					this.updateASTNodeRange(child, range, newNode)
				}
			}
		}
	}

	private nodeIntersectsRange(node: ASTNode, range: vscode.Range): boolean {
		const nodeStart = new vscode.Position(node.startPosition.row, node.startPosition.column)
		const nodeEnd = new vscode.Position(node.endPosition.row, node.endPosition.column)
		const nodeRange = new vscode.Range(nodeStart, nodeEnd)

		return nodeRange.intersection(range) !== undefined
	}

	private nodeWithinRange(node: ASTNode, range: vscode.Range): boolean {
		const nodeStart = new vscode.Position(node.startPosition.row, node.startPosition.column)
		const nodeEnd = new vscode.Position(node.endPosition.row, node.endPosition.column)

		return range.contains(nodeStart) && range.contains(nodeEnd)
	}

	private updateLSPCache(documentUri: string, symbol: string, data: any, type: "references" | "definition"): void {
		if (!this.lspCache.has(documentUri)) {
			this.lspCache.set(documentUri, {
				references: new Map(),
				definitions: new Map(),
				diagnostics: [],
				lastUpdate: Date.now(),
				documentVersion: 0,
			})
		}

		const cache = this.lspCache.get(documentUri)!

		if (type === "references") {
			cache.references.set(symbol, data)
		} else if (type === "definition") {
			cache.definitions.set(symbol, data)
		}

		cache.lastUpdate = Date.now()
	}

	private findSymbolPosition(symbol: string, document: vscode.TextDocument): vscode.Position {
		// 在文档中查找符号的位置
		const text = document.getText()
		const lines = text.split("\n")

		for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
			const line = lines[lineIndex]
			const symbolIndex = line.indexOf(symbol)
			if (symbolIndex !== -1) {
				// 确保是完整的符号匹配，而不是部分匹配
				const beforeChar = symbolIndex > 0 ? line[symbolIndex - 1] : " "
				const afterChar = symbolIndex + symbol.length < line.length ? line[symbolIndex + symbol.length] : " "

				if (!/\w/.test(beforeChar) && !/\w/.test(afterChar)) {
					return new vscode.Position(lineIndex, symbolIndex)
				}
			}
		}

		return new vscode.Position(0, 0)
	}

	private findNodeAtPosition(ast: ASTNode, position: vscode.Position): ASTNode | null {
		// 递归查找包含指定位置的最小 AST 节点
		if (!this.nodeContainsPosition(ast, position)) {
			return null
		}

		// 检查子节点
		for (const child of ast.namedChildren) {
			const childResult = this.findNodeAtPosition(child, position)
			if (childResult) {
				return childResult
			}
		}

		// 如果没有子节点包含该位置，返回当前节点
		return ast
	}

	private nodeContainsPosition(node: ASTNode, position: vscode.Position): boolean {
		const startLine = node.startPosition.row
		const startColumn = node.startPosition.column
		const endLine = node.endPosition.row
		const endColumn = node.endPosition.column

		// 检查位置是否在节点范围内
		if (position.line < startLine || position.line > endLine) {
			return false
		}

		if (position.line === startLine && position.character < startColumn) {
			return false
		}

		if (position.line === endLine && position.character > endColumn) {
			return false
		}

		return true
	}

	private extractSymbolName(astNode: ASTNode): string | null {
		// 根据节点类型提取符号名
		if (!astNode.text) {
			return null
		}

		// 对于标识符节点，直接返回文本
		if (astNode.type === "identifier" || astNode.type === "property_identifier" || astNode.type === "type_identifier") {
			return astNode.text
		}

		// 对于函数声明，提取函数名
		if (astNode.type === "function_declaration" || astNode.type === "method_definition") {
			const nameChild = astNode.namedChildren.find(
				(child) => child.type === "identifier" || child.type === "property_identifier",
			)
			return nameChild?.text || null
		}

		// 对于变量声明，提取变量名
		if (astNode.type === "variable_declarator") {
			const nameChild = astNode.namedChildren.find((child) => child.type === "identifier")
			return nameChild?.text || null
		}

		// 对于类声明，提取类名
		if (astNode.type === "class_declaration") {
			const nameChild = astNode.namedChildren.find((child) => child.type === "type_identifier")
			return nameChild?.text || null
		}

		// 默认返回节点文本（可能包含额外字符）
		return astNode.text
	}

	private createAnchorFromLocation(location: vscode.Location, document: vscode.TextDocument): string {
		const line = document.lineAt(location.range.start.line)
		return line.text.trim()
	}

	private async isFunctionCall(location: vscode.Location, document: vscode.TextDocument): Promise<boolean> {
		// 检查指定位置是否是函数调用
		const line = document.lineAt(location.range.start.line)
		const text = line.text
		const startChar = location.range.start.character
		const endChar = location.range.end.character

		// 获取符号文本
		const symbol = text.substring(startChar, endChar)

		// 检查符号后面是否有括号，表示函数调用
		const afterSymbol = text.substring(endChar).trim()
		if (afterSymbol.startsWith("(")) {
			return true
		}

		// 检查是否是方法调用 (object.method())
		const beforeSymbol = text.substring(0, startChar)
		if (beforeSymbol.endsWith(".") && afterSymbol.startsWith("(")) {
			return true
		}

		// 使用 TreeSitter 进行更精确的检查
		try {
			const ast = await this.treeSitterService.parseFile(document.uri.toString(), document.getText())
			if (ast) {
				const node = this.findNodeAtPosition(ast, location.range.start)
				return node?.type === "call_expression" || node?.type === "function_call"
			}
		} catch (error) {
			console.warn("Failed to parse AST for function call check:", error)
		}

		return false
	}

	private generateUpdatedFunctionCall(oldSignature: string, newSignature: string): string {
		// 解析旧函数签名和新函数签名，生成更新的函数调用
		const oldMatch = oldSignature.match(/(\w+)\s*\((.*?)\)/)
		const newMatch = newSignature.match(/(\w+)\s*\((.*?)\)/)

		if (!oldMatch || !newMatch) {
			return newSignature
		}

		const oldName = oldMatch[1]
		const oldParams = oldMatch[2]
			.split(",")
			.map((p) => p.trim())
			.filter((p) => p)
		const newName = newMatch[1]
		const newParams = newMatch[2]
			.split(",")
			.map((p) => p.trim())
			.filter((p) => p)

		// 如果只是函数名变更
		if (oldParams.length === newParams.length) {
			return oldSignature.replace(oldName, newName)
		}

		// 如果参数数量变更，需要更智能的处理
		let updatedCall = `${newName}(`

		// 尝试映射旧参数到新参数
		for (let i = 0; i < newParams.length; i++) {
			if (i > 0) {
				updatedCall += ", "
			}

			if (i < oldParams.length) {
				// 使用旧参数的值
				const oldParamName = oldParams[i].split(":")[0].trim()
				updatedCall += oldParamName
			} else {
				// 新增的参数，使用默认值或提示
				const newParamName = newParams[i].split(":")[0].trim()
				const newParamType = newParams[i].split(":")[1]?.trim()

				if (newParamType?.includes("string")) {
					updatedCall += `"${newParamName}"`
				} else if (newParamType?.includes("number")) {
					updatedCall += "0"
				} else if (newParamType?.includes("boolean")) {
					updatedCall += "false"
				} else {
					updatedCall += `/* TODO: provide ${newParamName} */`
				}
			}
		}

		updatedCall += ")"
		return updatedCall
	}

	private isSymbolRename(diff: DocumentDiff): boolean {
		return diff.diffType === "symbol_rename"
	}

	private isFunctionSignatureChange(diff: DocumentDiff): boolean {
		return diff.diffType === "function_signature"
	}

	private isVariableDeclaration(diff: DocumentDiff): boolean {
		return diff.diffType === "variable_decl"
	}

	private isImportChange(diff: DocumentDiff): boolean {
		return diff.diffType === "import_statement"
	}

	private estimateCacheMemoryUsage(): number {
		// 估算缓存内存使用
		let totalSize = 0

		// 估算 AST 缓存大小
		this.astCache.forEach((entry, key) => {
			totalSize += key.length * 2 // 字符串大小（UTF-16）
			totalSize += this.estimateASTSize(entry.ast)
			totalSize += 64 // 其他字段的估算大小
		})

		// 估算 LSP 缓存大小
		this.lspCache.forEach((entry, key) => {
			totalSize += key.length * 2
			totalSize += JSON.stringify(entry.references).length * 2
			totalSize += JSON.stringify(entry.definitions).length * 2
			totalSize += 64
		})

		return totalSize
	}

	private estimateASTSize(ast: ASTNode): number {
		let size = 100 // 基础节点大小

		// 递归计算子节点大小
		for (const child of ast.namedChildren) {
			size += this.estimateASTSize(child)
		}

		return size
	}

	/**
	 * 生成符号重命名建议
	 */
	private async generateSymbolRenameSuggestions(
		_astNode: ASTNode | null,
		references: vscode.Location[],
		context: ASTLSPAnalysisContext,
	): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		// 检测重命名的符号
		const oldContent = context.diff.oldContent || ""
		const newContent = context.diff.newContent || ""

		// 简单的符号重命名检测
		const oldSymbols = this.extractSymbols(oldContent)
		const newSymbols = this.extractSymbols(newContent)

		for (const oldSymbol of oldSymbols) {
			if (!newSymbols.includes(oldSymbol)) {
				// 找到可能的新符号名
				const possibleNewSymbol = this.findReplacementSymbol(oldSymbol, newSymbols, oldSymbols)

				if (possibleNewSymbol) {
					// 为每个引用位置生成更新建议
					for (const reference of references) {
						const line = context.document.lineAt(reference.range.start.line)
						if (line.text.includes(oldSymbol)) {
							suggestions.push(
								this.createSuggestion(
									"modify",
									`Change: Update symbol reference from '${oldSymbol}' to '${possibleNewSymbol}'`,
									line.text.trim(),
									"replace",
									line.text.replace(oldSymbol, possibleNewSymbol),
								),
							)
						}
					}
				}
			}
		}

		return suggestions
	}

	/**
	 * 生成函数签名变更建议
	 */
	private async generateFunctionSignatureSuggestions(
		_astNode: ASTNode | null,
		references: vscode.Location[],
		context: ASTLSPAnalysisContext,
	): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		// 检测函数签名变更
		const oldContent = context.diff.oldContent || ""
		const newContent = context.diff.newContent || ""

		const oldFunctions = this.extractFunctionSignatures(oldContent)
		const newFunctions = this.extractFunctionSignatures(newContent)

		for (const oldFunc of oldFunctions) {
			const matchingNewFunc = newFunctions.find((newFunc) => this.isSameFunctionWithDifferentSignature(oldFunc, newFunc))

			if (matchingNewFunc) {
				// 为每个函数调用位置生成更新建议
				for (const reference of references) {
					if (await this.isFunctionCall(reference, context.document)) {
						const line = context.document.lineAt(reference.range.start.line)
						const updatedCall = this.generateUpdatedFunctionCall(oldFunc, matchingNewFunc)

						suggestions.push(
							this.createSuggestion(
								"modify",
								`Change: Update function call to match new signature`,
								line.text.trim(),
								"replace",
								line.text.replace(oldFunc.split("(")[0], updatedCall),
							),
						)
					}
				}
			}
		}

		return suggestions
	}

	/**
	 * 生成变量声明建议
	 */
	private async generateVariableDeclarationSuggestions(
		_astNode: ASTNode | null,
		_references: vscode.Location[],
		context: ASTLSPAnalysisContext,
	): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		// 检测新的变量声明
		const newContent = context.diff.newContent || ""
		const variableDeclarations = this.extractVariableDeclarations(newContent)

		for (const varDecl of variableDeclarations) {
			// 建议添加类型注解（如果是 TypeScript）
			if (context.document.languageId === "typescript" && !varDecl.includes(":")) {
				const varName = varDecl
					.split("=")[0]
					.trim()
					.replace(/^(let|const|var)\s+/, "")
				const varValue = varDecl.split("=")[1]?.trim()

				let suggestedType = "any"
				if (varValue) {
					if (varValue.startsWith('"') || varValue.startsWith("'")) {
						suggestedType = "string"
					} else if (/^\d+$/.test(varValue)) {
						suggestedType = "number"
					} else if (varValue === "true" || varValue === "false") {
						suggestedType = "boolean"
					} else if (varValue.startsWith("[")) {
						suggestedType = "any[]"
					} else if (varValue.startsWith("{")) {
						suggestedType = "object"
					}
				}

				suggestions.push(
					this.createSuggestion(
						"modify",
						`Change: Add type annotation to variable '${varName}'`,
						varDecl,
						"replace",
						varDecl.replace(varName, `${varName}: ${suggestedType}`),
					),
				)
			}
		}

		return suggestions
	}

	/**
	 * 生成导入变更建议
	 */
	private async generateImportChangeSuggestions(
		_astNode: ASTNode | null,
		_references: vscode.Location[],
		context: ASTLSPAnalysisContext,
	): Promise<Suggestion[]> {
		const suggestions: Suggestion[] = []

		// 检测导入变更
		const oldContent = context.diff.oldContent || ""
		const newContent = context.diff.newContent || ""

		const oldImports = this.extractImports(oldContent)
		const newImports = this.extractImports(newContent)

		// 检测新增的导入
		for (const newImport of newImports) {
			if (!oldImports.includes(newImport)) {
				suggestions.push(this.createSuggestion("add", `Change: Add import statement`, "top of file", "after", newImport))
			}
		}

		// 检测移除的导入
		for (const oldImport of oldImports) {
			if (!newImports.includes(oldImport)) {
				suggestions.push(this.createSuggestion("delete", `Change: Remove unused import`, oldImport, "replace", ""))
			}
		}

		return suggestions
	}

	// 辅助方法
	private extractSymbols(content: string): string[] {
		// 提取代码中的符号（变量名、函数名等）
		const symbols: string[] = []
		const symbolRegex = /\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g
		let match

		while ((match = symbolRegex.exec(content)) !== null) {
			const symbol = match[0]
			// 过滤掉关键字
			if (!this.isKeyword(symbol) && !symbols.includes(symbol)) {
				symbols.push(symbol)
			}
		}

		return symbols
	}

	private isKeyword(word: string): boolean {
		const keywords = [
			"function",
			"var",
			"let",
			"const",
			"if",
			"else",
			"for",
			"while",
			"do",
			"switch",
			"case",
			"default",
			"break",
			"continue",
			"return",
			"try",
			"catch",
			"finally",
			"throw",
			"new",
			"this",
			"super",
			"class",
			"extends",
			"implements",
			"interface",
			"type",
			"enum",
			"namespace",
			"import",
			"export",
			"from",
			"as",
			"default",
			"async",
			"await",
			"true",
			"false",
			"null",
			"undefined",
			"void",
			"never",
			"any",
			"string",
			"number",
			"boolean",
			"object",
			"symbol",
			"bigint",
		]
		return keywords.includes(word)
	}

	private findReplacementSymbol(oldSymbol: string, newSymbols: string[], oldSymbols: string[]): string | null {
		// 寻找最可能的替换符号
		for (const newSymbol of newSymbols) {
			if (!oldSymbols.includes(newSymbol)) {
				// 检查相似度
				if (this.calculateSimilarity(oldSymbol, newSymbol) > 0.6) {
					return newSymbol
				}
			}
		}
		return null
	}

	private calculateSimilarity(str1: string, str2: string): number {
		// 简单的字符串相似度计算（Levenshtein距离）
		const len1 = str1.length
		const len2 = str2.length
		const matrix = Array(len2 + 1)
			.fill(null)
			.map(() => Array(len1 + 1).fill(null))

		for (let i = 0; i <= len1; i++) {
			matrix[0][i] = i
		}
		for (let j = 0; j <= len2; j++) {
			matrix[j][0] = j
		}

		for (let j = 1; j <= len2; j++) {
			for (let i = 1; i <= len1; i++) {
				const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
				matrix[j][i] = Math.min(matrix[j][i - 1] + 1, matrix[j - 1][i] + 1, matrix[j - 1][i - 1] + indicator)
			}
		}

		const distance = matrix[len2][len1]
		return 1 - distance / Math.max(len1, len2)
	}

	private extractFunctionSignatures(content: string): string[] {
		const signatures: string[] = []

		// 匹配函数声明
		const functionRegex = /function\s+(\w+)\s*\([^)]*\)/g
		let match

		while ((match = functionRegex.exec(content)) !== null) {
			signatures.push(match[0])
		}

		// 匹配箭头函数
		const arrowFunctionRegex = /(\w+)\s*=\s*\([^)]*\)\s*=>/g
		while ((match = arrowFunctionRegex.exec(content)) !== null) {
			signatures.push(match[0])
		}

		// 匹配方法声明
		const methodRegex = /(\w+)\s*\([^)]*\)\s*[:{]/g
		while ((match = methodRegex.exec(content)) !== null) {
			signatures.push(match[0])
		}

		return signatures
	}

	private isSameFunctionWithDifferentSignature(oldFunc: string, newFunc: string): boolean {
		// 提取函数名
		const oldName = oldFunc.match(/(\w+)/)?.[1]
		const newName = newFunc.match(/(\w+)/)?.[1]

		return oldName === newName && oldFunc !== newFunc
	}

	private extractVariableDeclarations(content: string): string[] {
		const declarations: string[] = []

		// 匹配变量声明
		const varRegex = /(let|const|var)\s+[^;]+/g
		let match

		while ((match = varRegex.exec(content)) !== null) {
			declarations.push(match[0])
		}

		return declarations
	}

	private extractImports(content: string): string[] {
		const imports: string[] = []

		// 匹配 import 语句
		const importRegex = /import\s+[^;]+;?/g
		let match

		while ((match = importRegex.exec(content)) !== null) {
			imports.push(match[0])
		}

		return imports
	}

	private createSuggestion(
		type: "add" | "modify" | "delete",
		description: string,
		anchor: string,
		position: "before" | "after" | "replace",
		newContent: string,
	): Suggestion {
		return {
			id: `ast-lsp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
			type,
			description,
			location: {
				anchor,
				position,
			},
			patch: {
				new_content: newContent,
			},
			confidence: 0.8,
			source: "ast-lsp" as SuggestionSource,
			priority: 2,
			category: "syntax" as SuggestionCategory,
			tags: ["ast", "lsp", "auto-generated"],
			timestamp: Date.now(),
			impact: ImpactLevel.LOW,
		}
	}
}
