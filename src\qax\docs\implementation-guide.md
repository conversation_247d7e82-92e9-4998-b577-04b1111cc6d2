# QAX Next Edit 实现指南

## 核心实现要点

### 1. 建议数组维护机制

#### 文档级建议存储
```typescript
class DocumentSuggestionManager {
    private suggestions: Map<string, DocumentSuggestionData> = new Map()
    
    // 初始化文档建议数组
    initializeDocument(documentUri: string): void {
        this.suggestions.set(documentUri, {
            suggestions: [],
            lastUpdate: Date.now(),
            documentVersion: 0,
            analysisHistory: [],
            metadata: this.createDocumentMetadata(documentUri)
        })
    }
    
    // 文档变更时更新建议数组
    async updateSuggestionsOnDocumentChange(
        documentUri: string, 
        changes: vscode.TextDocumentChangeEvent[]
    ): Promise<void> {
        const data = this.suggestions.get(documentUri)
        if (!data) return
        
        // 过滤失效的建议
        const validSuggestions = await this.filterObsoleteSuggestions(
            data.suggestions, 
            changes
        )
        
        // 更新建议数组
        data.suggestions = validSuggestions
        data.lastUpdate = Date.now()
        data.documentVersion++
        
        // 触发更新事件
        this.onSuggestionsUpdated.fire(documentUri, validSuggestions)
    }
    
    // 过滤失效建议的核心逻辑
    private async filterObsoleteSuggestions(
        suggestions: Suggestion[], 
        changes: vscode.TextDocumentChangeEvent[]
    ): Promise<Suggestion[]> {
        const validSuggestions: Suggestion[] = []
        
        for (const suggestion of suggestions) {
            // 检查建议的anchor是否仍然存在
            const isValid = await this.validateSuggestionAnchor(suggestion, changes)
            if (isValid) {
                validSuggestions.push(suggestion)
            }
        }
        
        return validSuggestions
    }
}
```

### 2. 双模式推荐引擎实现

#### AST+LSP快速分析（0.1秒目标）
```typescript
class FastASTLSPAnalyzer {
    private astCache = new Map<string, ASTCacheEntry>()
    private lspCache = new Map<string, LSPCacheEntry>()
    
    async performFastAnalysis(
        document: vscode.TextDocument,
        position: vscode.Position,
        diff: DocumentDiff
    ): Promise<Suggestion[]> {
        const startTime = performance.now()
        
        try {
            // 1. 快速检测变更类型（目标：10ms）
            const changeType = await this.detectChangeTypeQuick(diff, document)
            if (!changeType) return []
            
            // 2. 增量AST解析（目标：30ms）
            const astNode = await this.getASTNodeIncremental(document, position)
            
            // 3. LSP查询优化（目标：50ms）
            const references = await this.findReferencesOptimized(astNode, document)
            
            // 4. 建议生成（目标：10ms）
            const suggestions = await this.generateSuggestionsQuick(
                changeType, 
                astNode, 
                references
            )
            
            const duration = performance.now() - startTime
            this.trackPerformance('ast-lsp-analysis', duration)
            
            return suggestions
        } catch (error) {
            this.handleAnalysisError(error, 'ast-lsp')
            return []
        }
    }
    
    // 优化的变更类型检测
    private async detectChangeTypeQuick(
        diff: DocumentDiff, 
        document: vscode.TextDocument
    ): Promise<ChangeType | null> {
        // 使用启发式规则快速判断
        if (this.isSymbolRename(diff)) return ChangeType.SYMBOL_RENAME
        if (this.isFunctionSignatureChange(diff)) return ChangeType.FUNCTION_SIGNATURE_CHANGE
        if (this.isVariableDeclaration(diff)) return ChangeType.VARIABLE_DECLARATION
        
        return null
    }
    
    // 增量AST解析
    private async getASTNodeIncremental(
        document: vscode.TextDocument,
        position: vscode.Position
    ): Promise<ASTNode> {
        const cacheKey = document.uri.toString()
        const cached = this.astCache.get(cacheKey)
        
        if (cached && cached.documentVersion === document.version) {
            // 缓存命中，直接返回
            return this.findNodeAtPosition(cached.ast, position)
        }
        
        // 增量更新AST
        const updatedAST = await this.updateASTIncremental(document, cached)
        return this.findNodeAtPosition(updatedAST, position)
    }
}
```

#### 模型深度分析（5秒目标）
```typescript
class DeepModelAnalyzer {
    async performDeepAnalysis(
        document: vscode.TextDocument,
        context: ModelAnalysisContext
    ): Promise<Suggestion[]> {
        const startTime = performance.now()
        
        try {
            // 1. 构建分析上下文（目标：500ms）
            const enrichedContext = await this.buildRichContext(document, context)
            
            // 2. 生成优化的提示词（目标：200ms）
            const prompt = await this.buildOptimizedPrompt(enrichedContext)
            
            // 3. 调用模型API（目标：4000ms）
            const response = await this.callModelAPI(prompt)
            
            // 4. 解析和验证响应（目标：300ms）
            const suggestions = await this.parseAndValidateResponse(response)
            
            const duration = performance.now() - startTime
            this.trackPerformance('model-analysis', duration)
            
            return suggestions
        } catch (error) {
            this.handleAnalysisError(error, 'model')
            return []
        }
    }
    
    // 构建丰富的分析上下文
    private async buildRichContext(
        document: vscode.TextDocument,
        baseContext: ModelAnalysisContext
    ): Promise<EnrichedAnalysisContext> {
        return {
            ...baseContext,
            // 收集最近变更（按用户要求）
            recentChanges: await this.collectRecentChanges(),
            // 相关文件内容
            relatedFiles: await this.collectRelatedFileContents(document),
            // 项目结构信息
            projectStructure: await this.analyzeProjectStructure(),
            // 代码质量指标
            qualityMetrics: await this.calculateQualityMetrics(document)
        }
    }
    
    // 生成针对性提示词
    private async buildOptimizedPrompt(context: EnrichedAnalysisContext): Promise<string> {
        const template = await this.loadPromptTemplate()
        
        return template
            .replace('{{CURRENT_FILE}}', context.currentContent)
            .replace('{{RECENT_CHANGES}}', this.formatRecentChanges(context.recentChanges))
            .replace('{{ANALYSIS_GOALS}}', this.formatAnalysisGoals([
                '不完整的逻辑',
                '未实现的函数', 
                '未调用的变量',
                '格式问题',
                'lint问题'
            ]))
    }
}
```

### 3. 精确触发机制实现

#### 位置检测逻辑
```typescript
class TriggerPositionAnalyzer {
    // 检查是否在行末
    isPositionAtLineEnd(document: vscode.TextDocument, position: vscode.Position): boolean {
        const line = document.lineAt(position.line)
        const trimmedLine = line.text.trimEnd()
        
        // 考虑空白字符，位置在实际内容末尾或之后
        return position.character >= trimmedLine.length
    }
    
    // 检查是否在行中间
    isPositionInLineMiddle(document: vscode.TextDocument, position: vscode.Position): boolean {
        const line = document.lineAt(position.line)
        const trimmedLine = line.text.trimEnd()
        
        // 位置在行的中间部分，且不在行首
        return position.character > 0 && position.character < trimmedLine.length
    }
    
    // 获取输入上下文
    getInputContext(document: vscode.TextDocument, position: vscode.Position): InputContext {
        const line = document.lineAt(position.line)
        const beforeCursor = line.text.substring(0, position.character)
        const afterCursor = line.text.substring(position.character)
        
        return {
            lineText: line.text,
            beforeCursor,
            afterCursor,
            isAtLineStart: position.character === 0,
            isAtLineEnd: this.isPositionAtLineEnd(document, position),
            isInMiddle: this.isPositionInLineMiddle(document, position),
            syntaxContext: this.analyzeSyntaxContext(document, position)
        }
    }
}
```

### 4. 完整Diff生成机制

#### 增量Diff生成器
```typescript
class IncrementalDiffGenerator {
    private documentStates = new Map<string, DocumentState>()
    
    // 生成完整的文档差异（不是单字符diff）
    generateCompleteDiff(
        documentUri: string,
        changes: vscode.TextDocumentChangeEvent[]
    ): DocumentDiff {
        const previousState = this.documentStates.get(documentUri)
        if (!previousState) {
            // 首次变更，建立基线
            return this.createBaselineDiff(changes)
        }
        
        // 累积变更，生成完整diff
        return this.accumulateChanges(previousState, changes)
    }
    
    // 累积变更逻辑
    private accumulateChanges(
        previousState: DocumentState,
        newChanges: vscode.TextDocumentChangeEvent[]
    ): DocumentDiff {
        const diff: DocumentDiff = {
            additions: [],
            deletions: [],
            modifications: [],
            isComplete: true,
            diffType: DiffType.UNKNOWN,
            affectedSymbols: [],
            contextLines: 3,
            metadata: {
                confidence: 0.9,
                complexity: 0,
                riskLevel: 'low',
                estimatedImpact: []
            }
        }
        
        // 分析每个变更的语义影响
        for (const change of newChanges) {
            this.analyzeChangeImpact(change, diff, previousState)
        }
        
        // 确定整体变更类型
        diff.diffType = this.determineOverallChangeType(diff)
        
        return diff
    }
    
    // 分析变更影响
    private analyzeChangeImpact(
        change: vscode.TextDocumentChangeEvent,
        diff: DocumentDiff,
        state: DocumentState
    ): void {
        const changeLines = change.text.split('\n')
        const rangeLines = change.range.end.line - change.range.start.line + 1
        
        if (change.text.length === 0) {
            // 删除操作
            for (let i = 0; i < rangeLines; i++) {
                diff.deletions.push({
                    lineNumber: change.range.start.line + i,
                    content: state.getLineContent(change.range.start.line + i),
                    type: 'removed',
                    indentLevel: this.calculateIndentLevel(state.getLineContent(change.range.start.line + i))
                })
            }
        } else if (rangeLines === 1 && changeLines.length === 1) {
            // 修改操作
            diff.modifications.push({
                lineNumber: change.range.start.line,
                content: changeLines[0],
                type: 'modified',
                indentLevel: this.calculateIndentLevel(changeLines[0])
            })
        } else {
            // 添加操作
            changeLines.forEach((line, index) => {
                diff.additions.push({
                    lineNumber: change.range.start.line + index,
                    content: line,
                    type: 'added',
                    indentLevel: this.calculateIndentLevel(line)
                })
            })
        }
    }
}
```

### 5. 建议JSON格式实现

#### 标准建议生成器
```typescript
class SuggestionGenerator {
    // 生成符合用户要求的JSON格式建议
    generateSuggestion(
        type: 'add' | 'modify' | 'delete',
        description: string,
        anchor: string,
        position: 'before' | 'after' | 'replace',
        newContent: string,
        oldContent?: string
    ): Suggestion {
        return {
            id: this.generateUniqueId(),
            type,
            description: this.formatDescription(type, description),
            location: {
                anchor,
                position,
                range: this.calculateRange(anchor),
                lineNumber: this.extractLineNumber(anchor),
                contextLines: this.extractContextLines(anchor)
            },
            patch: {
                new_content: newContent,  // 符合用户要求的字段名
                old_content: oldContent,
                encoding: 'utf-8',
                language: this.detectLanguage()
            },
            confidence: this.calculateConfidence(),
            source: SuggestionSource.AST_LSP,
            priority: this.calculatePriority(),
            timestamp: Date.now(),
            category: this.categorizeChange(type, description),
            impact: this.assessImpact(),
            tags: this.generateTags(),
            relatedFiles: this.findRelatedFiles(),
            dependencies: []
        }
    }
    
    // 格式化描述信息
    private formatDescription(type: string, description: string): string {
        switch (type) {
            case 'add':
                return `Change: Add ${description}`
            case 'delete':
                return `Change: Del ${description}`
            case 'modify':
                return `Change: ${description}`  // 假设description已包含 "xxx -> yyy" 格式
            default:
                return description
        }
    }
    
    // 生成唯一锚点
    generateUniqueAnchor(document: vscode.TextDocument, range: vscode.Range): string {
        const lineContent = document.lineAt(range.start.line).text
        const beforeContext = range.start.line > 0 ? 
            document.lineAt(range.start.line - 1).text : ''
        const afterContext = range.end.line < document.lineCount - 1 ? 
            document.lineAt(range.end.line + 1).text : ''
        
        // 创建唯一的代码模式用于定位
        return `${beforeContext.trim()}|${lineContent.trim()}|${afterContext.trim()}`
    }
}
```

## 性能优化策略

### 1. 响应时间优化
- **AST+LSP分析**：使用增量解析、缓存优化、并行处理
- **模型分析**：上下文优化、API调用优化、结果缓存

### 2. 内存管理
- **缓存策略**：LRU淘汰、定期清理、大小限制
- **对象池**：重用AST节点、建议对象

### 3. 并发控制
- **分析队列**：优先级调度、资源限制
- **防抖优化**：智能合并、取消机制

## 测试策略

### 1. 单元测试重点
- 建议数组维护逻辑
- 触发条件判断
- Diff生成准确性
- 建议格式验证

### 2. 集成测试重点
- 端到端工作流
- 性能基准测试
- 与autocomplete集成
- 错误恢复机制

### 3. 用户体验测试
- 响应时间验证
- 建议质量评估
- UI交互流畅性
- 错误处理友好性
