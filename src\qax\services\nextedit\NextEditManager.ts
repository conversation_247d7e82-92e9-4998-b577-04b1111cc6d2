/**
 * QAX Next Edit - 主管理器
 * 协调所有子组件，管理文档生命周期，处理用户交互
 */

import * as vscode from "vscode"
import { Suggestion } from "./types/Suggestion"
import { NextEditSettings, DEFAULT_NEXT_EDIT_SETTINGS } from "./types/NextEditSettings"
import { AnalysisType, AnalysisStatus } from "./types/AnalysisContext"
import { SuggestionManager } from "./managers/SuggestionManager"
import { TriggerManager } from "./managers/TriggerManager"
import { AnalysisEngine } from "./managers/AnalysisEngine"
import { DiffGenerator } from "./analyzers/DiffGenerator"
import { ASTLSPAnalyzer } from "./analyzers/ASTLSPAnalyzer"
import { ModelAnalyzer } from "./analyzers/ModelAnalyzer"
import { CacheManager } from "./cache/CacheManager"

/**
 * Next Edit 错误类型
 */
export class NextEditError extends Error {
	constructor(
		message: string,
		public readonly code: string,
		public readonly documentUri?: string,
	) {
		super(message)
		this.name = "NextEditError"
	}
}

/**
 * 分析状态信息
 */
export interface AnalysisStatusInfo {
	/** 分析类型 */
	type: AnalysisType
	/** 状态 */
	status: AnalysisStatus
	/** 开始时间 */
	startTime: number
	/** 预估完成时间 */
	estimatedCompletionTime?: number
}

/**
 * QAX Next Edit 主管理器
 */
export class NextEditManager {
	private settings: NextEditSettings
	private suggestionManager: SuggestionManager
	private triggerManager: TriggerManager
	private analysisEngine: AnalysisEngine
	private diffGenerator: DiffGenerator
	private astLspAnalyzer: ASTLSPAnalyzer
	private modelAnalyzer: ModelAnalyzer
	private cacheManager: CacheManager
	private context: vscode.ExtensionContext | null = null
	private disposables: vscode.Disposable[] = []
	private isInitialized = false

	// 事件回调
	private onSuggestionGeneratedCallbacks: ((suggestions: Suggestion[]) => void)[] = []
	private onAnalysisCompletedCallbacks: ((documentUri: string, duration: number) => void)[] = []
	private onErrorCallbacks: ((error: NextEditError) => void)[] = []

	constructor(settings: NextEditSettings = DEFAULT_NEXT_EDIT_SETTINGS) {
		this.settings = settings
		this.suggestionManager = new SuggestionManager(settings)
		this.triggerManager = new TriggerManager(settings, this.suggestionManager)
		this.diffGenerator = new DiffGenerator()
		this.astLspAnalyzer = new ASTLSPAnalyzer(settings)
		this.modelAnalyzer = new ModelAnalyzer(settings)
		this.cacheManager = new CacheManager(settings)
		this.analysisEngine = new AnalysisEngine(settings, this.diffGenerator)

		// 设置分析器依赖
		this.analysisEngine.setAnalyzers(this.astLspAnalyzer, this.modelAnalyzer)

		this.setupEventHandlers()
	}

	/**
	 * 初始化
	 */
	async initialize(context: vscode.ExtensionContext): Promise<void> {
		if (this.isInitialized) {
			return
		}

		this.context = context

		try {
			// 注册文档事件监听器
			this.registerDocumentEventListeners()

			// 注册命令
			this.registerCommands()

			// 注册配置变更监听
			this.registerConfigurationListener()

			this.isInitialized = true
			console.log("QAX Next Edit initialized successfully")
		} catch (error) {
			const nextEditError = new NextEditError(`Failed to initialize Next Edit: ${error}`, "INITIALIZATION_ERROR")
			this.notifyError(nextEditError)
			throw nextEditError
		}
	}

	/**
	 * 销毁
	 */
	async dispose(): Promise<void> {
		try {
			// 清理所有disposables
			this.disposables.forEach((disposable) => disposable.dispose())
			this.disposables = []

			// 清理各个组件
			this.analysisEngine.dispose()
			this.astLspAnalyzer.dispose()
			this.modelAnalyzer.dispose()
			this.cacheManager.dispose()

			this.isInitialized = false
			console.log("QAX Next Edit disposed successfully")
		} catch (error) {
			console.error("Error disposing Next Edit:", error)
		}
	}

	/**
	 * 更新配置
	 */
	updateConfiguration(config: NextEditSettings): void {
		this.settings = config
		this.suggestionManager.updateSettings(config)
		this.triggerManager.updateSettings(config)
		this.analysisEngine.updateSettings(config)
		this.astLspAnalyzer.updateSettings(config)
		this.modelAnalyzer.updateSettings(config)
		this.cacheManager.updateSettings(config)
	}

	/**
	 * 文档打开事件处理
	 */
	async onDocumentOpened(document: vscode.TextDocument): Promise<void> {
		if (!this.isInitialized || !this.settings.enabled) {
			return
		}

		try {
			const documentUri = document.uri.toString()

			// 初始化文档建议数组
			this.suggestionManager.initializeDocument(documentUri, document)

			// 设置文档为活跃状态
			this.triggerManager.setDocumentActive(documentUri)

			console.log(`Document opened: ${documentUri}`)
		} catch (error) {
			this.notifyError(
				new NextEditError(`Error handling document open: ${error}`, "DOCUMENT_OPEN_ERROR", document.uri.toString()),
			)
		}
	}

	/**
	 * 文档关闭事件处理
	 */
	async onDocumentClosed(document: vscode.TextDocument): Promise<void> {
		try {
			const documentUri = document.uri.toString()

			// 清理文档相关数据
			this.suggestionManager.cleanupDocument(documentUri)
			this.triggerManager.cleanupDocument(documentUri)
			this.diffGenerator.cleanupDocument(documentUri)

			console.log(`Document closed: ${documentUri}`)
		} catch (error) {
			console.error("Error handling document close:", error)
		}
	}

	/**
	 * 文档变更事件处理
	 */
	async onDocumentChanged(
		document: vscode.TextDocument,
		changes: readonly vscode.TextDocumentContentChangeEvent[],
	): Promise<void> {
		if (!this.isInitialized || !this.settings.enabled) {
			return
		}

		try {
			const documentUri = document.uri.toString()

			// 设置文档为活跃状态
			this.triggerManager.setDocumentActive(documentUri)

			// 过滤失效的建议
			await this.suggestionManager.filterObsoleteSuggestions(documentUri, Array.from(changes))

			// 更新文档状态
			this.diffGenerator.updateDocumentState(documentUri, Array.from(changes))

			// 检查触发条件（这里需要获取当前光标位置）
			const editor = vscode.window.activeTextEditor
			if (editor && editor.document === document) {
				await this.checkTriggerConditions(document, editor.selection.active)
			}
		} catch (error) {
			this.notifyError(
				new NextEditError(`Error handling document change: ${error}`, "DOCUMENT_CHANGE_ERROR", document.uri.toString()),
			)
		}
	}

	/**
	 * 文档保存事件处理
	 */
	async onDocumentSaved(document: vscode.TextDocument): Promise<void> {
		try {
			const documentUri = document.uri.toString()

			// 设置文档空闲（保存后通常会有一段空闲时间）
			setTimeout(() => {
				this.triggerManager.setDocumentIdle(documentUri)
			}, 1000)
		} catch (error) {
			console.error("Error handling document save:", error)
		}
	}

	/**
	 * 获取建议
	 */
	getSuggestions(documentUri: string): Suggestion[] {
		return this.suggestionManager.getSuggestions(documentUri)
	}

	/**
	 * 执行建议
	 */
	async executeSuggestion(suggestion: Suggestion): Promise<boolean> {
		try {
			// 找到对应的文档
			const document = vscode.workspace.textDocuments.find((doc) =>
				this.suggestionManager.getSuggestions(doc.uri.toString()).some((s) => s.id === suggestion.id),
			)

			if (!document) {
				throw new NextEditError("Document not found for suggestion", "DOCUMENT_NOT_FOUND")
			}

			// 执行编辑操作
			const success = await this.applySuggestionToDocument(suggestion, document)

			if (success) {
				// 移除已执行的建议
				await this.suggestionManager.removeSuggestion(document.uri.toString(), suggestion.id)
			}

			return success
		} catch (error) {
			this.notifyError(new NextEditError(`Error executing suggestion: ${error}`, "SUGGESTION_EXECUTION_ERROR"))
			return false
		}
	}

	/**
	 * 忽略建议
	 */
	async dismissSuggestion(suggestionId: string): Promise<void> {
		try {
			// 找到包含该建议的文档
			for (const document of vscode.workspace.textDocuments) {
				const documentUri = document.uri.toString()
				const suggestions = this.suggestionManager.getSuggestions(documentUri)

				if (suggestions.some((s) => s.id === suggestionId)) {
					await this.suggestionManager.removeSuggestion(documentUri, suggestionId)
					return
				}
			}
		} catch (error) {
			this.notifyError(new NextEditError(`Error dismissing suggestion: ${error}`, "SUGGESTION_DISMISS_ERROR"))
		}
	}

	/**
	 * 忽略文档的所有建议
	 */
	async dismissAllSuggestions(documentUri: string): Promise<void> {
		try {
			await this.suggestionManager.clearSuggestions(documentUri)
		} catch (error) {
			this.notifyError(
				new NextEditError(`Error dismissing all suggestions: ${error}`, "SUGGESTIONS_DISMISS_ALL_ERROR", documentUri),
			)
		}
	}

	/**
	 * 检查是否正在分析
	 */
	isAnalyzing(documentUri: string): boolean {
		return this.triggerManager.isAnalysisInProgress(documentUri)
	}

	/**
	 * 获取分析状态
	 */
	getAnalysisStatus(documentUri: string): AnalysisStatusInfo | null {
		if (this.triggerManager.isAnalysisInProgress(documentUri, AnalysisType.MODEL)) {
			return {
				type: AnalysisType.MODEL,
				status: AnalysisStatus.RUNNING,
				startTime: Date.now(),
				estimatedCompletionTime: Date.now() + 5000,
			}
		}

		if (this.triggerManager.isAnalysisInProgress(documentUri, AnalysisType.AST_LSP)) {
			return {
				type: AnalysisType.AST_LSP,
				status: AnalysisStatus.RUNNING,
				startTime: Date.now(),
				estimatedCompletionTime: Date.now() + 100,
			}
		}

		return null
	}

	/**
	 * 注册建议生成回调
	 */
	onSuggestionGenerated(callback: (suggestions: Suggestion[]) => void): void {
		this.onSuggestionGeneratedCallbacks.push(callback)
	}

	/**
	 * 注册分析完成回调
	 */
	onAnalysisCompleted(callback: (documentUri: string, duration: number) => void): void {
		this.onAnalysisCompletedCallbacks.push(callback)
	}

	/**
	 * 注册错误回调
	 */
	onError(callback: (error: NextEditError) => void): void {
		this.onErrorCallbacks.push(callback)
	}

	// 私有方法

	/**
	 * 设置事件处理器
	 */
	private setupEventHandlers(): void {
		// 监听建议更新事件
		this.suggestionManager.onSuggestionsUpdated((documentUri, suggestions) => {
			console.log(`📝 Suggestions updated for ${documentUri}: ${suggestions.length} suggestions`)
			console.log(
				`📝 Suggestion details:`,
				suggestions.map((s) => ({
					id: s.id,
					type: s.type,
					description: s.description,
					confidence: s.confidence,
					source: s.source,
					category: s.category,
				})),
			)
			this.notifySuggestionGenerated(suggestions)
		})

		// 监听触发条件满足事件
		this.triggerManager.onTriggerConditionMet((documentUri, analysisType) => {
			this.handleTriggerConditionMet(documentUri, analysisType)
		})

		// 监听分析被阻止事件
		this.triggerManager.onAnalysisBlocked((documentUri, reason) => {
			console.log(`Analysis blocked for ${documentUri}: ${reason}`)
		})

		// 监听分析引擎事件
		this.analysisEngine.onAnalysisStarted((taskId, analysisType) => {
			const documentUri = this.getDocumentUriFromTaskId(taskId)
			if (documentUri) {
				this.triggerManager.setAnalysisInProgress(documentUri, analysisType)
			}
		})

		this.analysisEngine.onAnalysisCompleted((taskId, result) => {
			const documentUri = this.getDocumentUriFromTaskId(taskId)
			if (documentUri) {
				this.triggerManager.setAnalysisCompleted(documentUri, result.analysisType)

				// 添加生成的建议
				if (result.suggestions.length > 0) {
					this.suggestionManager.addSuggestions(documentUri, result.suggestions)
				}

				this.notifyAnalysisCompleted(documentUri, result.duration)
			}
		})

		this.analysisEngine.onAnalysisError((taskId, error) => {
			const documentUri = this.getDocumentUriFromTaskId(taskId)
			if (documentUri) {
				this.notifyError(new NextEditError(`Analysis error: ${error.message}`, "ANALYSIS_ERROR", documentUri))
			}
		})
	}

	/**
	 * 注册文档事件监听器
	 */
	private registerDocumentEventListeners(): void {
		// 文档打开事件
		this.disposables.push(
			vscode.workspace.onDidOpenTextDocument((document) => {
				this.onDocumentOpened(document)
			}),
		)

		// 文档关闭事件
		this.disposables.push(
			vscode.workspace.onDidCloseTextDocument((document) => {
				this.onDocumentClosed(document)
			}),
		)

		// 文档变更事件
		this.disposables.push(
			vscode.workspace.onDidChangeTextDocument((event) => {
				this.onDocumentChanged(event.document, event.contentChanges)
			}),
		)

		// 文档保存事件
		this.disposables.push(
			vscode.workspace.onDidSaveTextDocument((document) => {
				this.onDocumentSaved(document)
			}),
		)

		// 光标位置变更事件
		this.disposables.push(
			vscode.window.onDidChangeTextEditorSelection((event) => {
				if (event.textEditor.document) {
					this.handleCursorPositionChange(event.textEditor.document, event.selections[0].active)
				}
			}),
		)
	}

	/**
	 * 注册命令
	 */
	private registerCommands(): void {
		if (!this.context) return

		// 执行建议命令
		this.disposables.push(
			vscode.commands.registerCommand("qax-nextedit.executeSuggestion", async (suggestionId: string) => {
				const suggestion = this.suggestionManager.getSuggestionById(suggestionId)
				if (suggestion) {
					await this.executeSuggestion(suggestion)
				}
			}),
		)

		// 忽略建议命令
		this.disposables.push(
			vscode.commands.registerCommand("qax-nextedit.dismissSuggestion", async (suggestionId: string) => {
				await this.dismissSuggestion(suggestionId)
			}),
		)

		// 忽略所有建议命令
		this.disposables.push(
			vscode.commands.registerCommand("qax-nextedit.dismissAllSuggestions", async () => {
				const editor = vscode.window.activeTextEditor
				if (editor) {
					await this.dismissAllSuggestions(editor.document.uri.toString())
				}
			}),
		)

		// 手动触发分析命令
		this.disposables.push(
			vscode.commands.registerCommand("qax-nextedit.triggerAnalysis", async () => {
				const editor = vscode.window.activeTextEditor
				if (editor) {
					await this.manualTriggerAnalysis(editor.document, editor.selection.active)
				}
			}),
		)
	}

	/**
	 * 注册配置变更监听
	 */
	private registerConfigurationListener(): void {
		this.disposables.push(
			vscode.workspace.onDidChangeConfiguration((event) => {
				if (event.affectsConfiguration("qax-codegen.nextEdit")) {
					this.loadConfiguration()
				}
			}),
		)
	}

	/**
	 * 加载配置
	 */
	private loadConfiguration(): void {
		const config = vscode.workspace.getConfiguration("qax-codegen.nextEdit")

		// 合并配置
		const newSettings: NextEditSettings = {
			...DEFAULT_NEXT_EDIT_SETTINGS,
			enabled: config.get("enabled", DEFAULT_NEXT_EDIT_SETTINGS.enabled),
			modelRecommendation: {
				...DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation,
				enabled: config.get("modelRecommendation.enabled", DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation.enabled),
				debounceMs: config.get(
					"modelRecommendation.debounceMs",
					DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation.debounceMs,
				),
				idleTimeMs: config.get(
					"modelRecommendation.idleTimeMs",
					DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation.idleTimeMs,
				),
				apiEndpoint: config.get(
					"modelRecommendation.apiEndpoint",
					DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation.apiEndpoint,
				),
				modelId: config.get("modelRecommendation.modelId", DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation.modelId),
			},
			astLspAnalysis: {
				...DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis,
				enabled: config.get("astLspAnalysis.enabled", DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis.enabled),
				debounceMs: config.get("astLspAnalysis.debounceMs", DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis.debounceMs),
				responseTimeTarget: config.get(
					"astLspAnalysis.responseTimeTarget",
					DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis.responseTimeTarget,
				),
			},
		}

		this.updateConfiguration(newSettings)
	}

	/**
	 * 检查触发条件
	 */
	private async checkTriggerConditions(document: vscode.TextDocument, position: vscode.Position): Promise<void> {
		const documentUri = document.uri.toString()

		try {
			// 检查是否应该触发 autocomplete（仅记录，不阻止其他分析）
			if (this.triggerManager.shouldTriggerAutocomplete(document, position)) {
				console.log("Should trigger autocomplete")
				// 不要 return，继续检查其他触发条件
			}

			// 检查是否应该触发 AST+LSP 分析
			if (await this.triggerManager.shouldTriggerASTLSPAnalysis(document, position)) {
				this.triggerManager.debounceASTLSPAnalysis(documentUri, () => {
					this.triggerASTLSPAnalysis(document, position)
				})
				return
			}

			// 设置文档空闲，稍后检查是否应该触发模型推荐
			setTimeout(async () => {
				this.triggerManager.setDocumentIdle(documentUri)

				if (await this.triggerManager.shouldTriggerModelRecommendation(document)) {
					this.triggerManager.debounceModelRecommendation(documentUri, () => {
						this.triggerModelAnalysis(document)
					})
				}
			}, 100)
		} catch (error) {
			this.notifyError(new NextEditError(`Error checking trigger conditions: ${error}`, "TRIGGER_CHECK_ERROR", documentUri))
		}
	}

	/**
	 * 处理光标位置变更
	 */
	private async handleCursorPositionChange(document: vscode.TextDocument, position: vscode.Position): Promise<void> {
		// 光标移动时重新检查触发条件
		await this.checkTriggerConditions(document, position)
	}

	/**
	 * 处理触发条件满足事件
	 */
	private async handleTriggerConditionMet(documentUri: string, analysisType: AnalysisType): Promise<void> {
		const document = vscode.workspace.textDocuments.find((doc) => doc.uri.toString() === documentUri)
		if (!document) return

		try {
			if (analysisType === AnalysisType.MODEL) {
				await this.triggerModelAnalysis(document)
			} else if (analysisType === AnalysisType.AST_LSP) {
				const editor = vscode.window.activeTextEditor
				if (editor && editor.document === document) {
					await this.triggerASTLSPAnalysis(document, editor.selection.active)
				}
			}
		} catch (error) {
			this.notifyError(
				new NextEditError(`Error handling trigger condition: ${error}`, "TRIGGER_HANDLER_ERROR", documentUri),
			)
		}
	}

	/**
	 * 触发模型分析
	 */
	private async triggerModelAnalysis(document: vscode.TextDocument): Promise<void> {
		const documentUri = document.uri.toString()

		try {
			// 构建分析上下文
			const context = await this.analysisEngine.buildAnalysisContext(document, AnalysisType.MODEL)

			// 调度分析任务
			const taskId = await this.analysisEngine.scheduleAnalysis(
				documentUri,
				AnalysisType.MODEL,
				5, // 高优先级
				context,
			)

			console.log(`Model analysis scheduled: ${taskId}`)
		} catch (error) {
			this.notifyError(new NextEditError(`Model analysis failed: ${error}`, "MODEL_ANALYSIS_ERROR", documentUri))
		}
	}

	/**
	 * 触发 AST+LSP 分析
	 */
	private async triggerASTLSPAnalysis(document: vscode.TextDocument, position: vscode.Position): Promise<void> {
		const documentUri = document.uri.toString()

		try {
			// 生成差异
			const diff = this.diffGenerator.generateIncrementalDiff(documentUri, [])

			// 构建分析上下文
			const context = await this.analysisEngine.buildAnalysisContext(document, AnalysisType.AST_LSP, position, diff)

			// 调度分析任务
			const taskId = await this.analysisEngine.scheduleAnalysis(
				documentUri,
				AnalysisType.AST_LSP,
				8, // 更高优先级
				context,
			)

			console.log(`AST+LSP analysis scheduled: ${taskId}`)
		} catch (error) {
			this.notifyError(new NextEditError(`AST+LSP analysis failed: ${error}`, "AST_LSP_ANALYSIS_ERROR", documentUri))
		}
	}

	/**
	 * 手动触发分析
	 */
	private async manualTriggerAnalysis(document: vscode.TextDocument, _position: vscode.Position): Promise<void> {
		const documentUri = document.uri.toString()

		try {
			// 检查是否可以触发分析
			if (!this.triggerManager.canTriggerAnalysis(documentUri, AnalysisType.MODEL)) {
				vscode.window.showWarningMessage("Analysis is already in progress or blocked")
				return
			}

			// 强制触发模型分析
			await this.triggerModelAnalysis(document)
		} catch (error) {
			this.notifyError(new NextEditError(`Manual analysis trigger failed: ${error}`, "MANUAL_TRIGGER_ERROR", documentUri))
		}
	}

	/**
	 * 应用建议到文档
	 */
	private async applySuggestionToDocument(suggestion: Suggestion, document: vscode.TextDocument): Promise<boolean> {
		try {
			const editor = vscode.window.visibleTextEditors.find((e) => e.document === document)
			if (!editor) {
				throw new Error("No active editor found for document")
			}

			// 根据建议的位置信息找到编辑位置
			const editPosition = await this.findSuggestionPosition(suggestion, document)
			if (!editPosition) {
				throw new Error("Could not find suggestion position in document")
			}

			// 执行编辑操作
			const success = await editor.edit((editBuilder) => {
				switch (suggestion.type) {
					case "add":
						if (suggestion.location.position === "before") {
							editBuilder.insert(editPosition, suggestion.patch.new_content + "\n")
						} else if (suggestion.location.position === "after") {
							const lineEnd = new vscode.Position(editPosition.line, document.lineAt(editPosition.line).text.length)
							editBuilder.insert(lineEnd, "\n" + suggestion.patch.new_content)
						}
						break

					case "modify":
					case "delete":
						if (suggestion.location.range) {
							if (suggestion.type === "delete") {
								editBuilder.delete(suggestion.location.range)
							} else {
								editBuilder.replace(suggestion.location.range, suggestion.patch.new_content)
							}
						}
						break
				}
			})

			return success
		} catch (error) {
			console.error("Error applying suggestion to document:", error)
			return false
		}
	}

	/**
	 * 查找建议在文档中的位置
	 */
	private async findSuggestionPosition(suggestion: Suggestion, document: vscode.TextDocument): Promise<vscode.Position | null> {
		try {
			const anchor = suggestion.location.anchor
			const text = document.getText()

			// 简单的锚点匹配逻辑
			const anchorParts = anchor
				.split("|")
				.map((part) => part.trim())
				.filter((part) => part.length > 0)

			for (const part of anchorParts) {
				const index = text.indexOf(part)
				if (index !== -1) {
					return document.positionAt(index)
				}
			}

			// 如果有行号信息，使用行号
			if (suggestion.location.lineNumber !== undefined) {
				const lineNumber = Math.min(suggestion.location.lineNumber, document.lineCount - 1)
				return new vscode.Position(lineNumber, suggestion.location.columnNumber || 0)
			}

			return null
		} catch (error) {
			console.error("Error finding suggestion position:", error)
			return null
		}
	}

	/**
	 * 通知建议生成
	 */
	private notifySuggestionGenerated(suggestions: Suggestion[]): void {
		this.onSuggestionGeneratedCallbacks.forEach((callback) => {
			try {
				callback(suggestions)
			} catch (error) {
				console.error("Error in suggestion generated callback:", error)
			}
		})
	}

	/**
	 * 通知分析完成
	 */
	private notifyAnalysisCompleted(documentUri: string, duration: number): void {
		this.onAnalysisCompletedCallbacks.forEach((callback) => {
			try {
				callback(documentUri, duration)
			} catch (error) {
				console.error("Error in analysis completed callback:", error)
			}
		})
	}

	/**
	 * 通知错误
	 */
	private notifyError(error: NextEditError): void {
		this.onErrorCallbacks.forEach((callback) => {
			try {
				callback(error)
			} catch (error) {
				console.error("Error in error callback:", error)
			}
		})
	}

	/**
	 * 从任务ID获取文档URI（简化实现）
	 */
	private getDocumentUriFromTaskId(taskId: string): string | null {
		// 使用分析引擎的任务映射
		try {
			const documentUri = this.analysisEngine.getTaskDocumentUri(taskId)

			if (documentUri) {
				console.log(`🔍 Found document URI for task ${taskId}: ${documentUri}`)
				return documentUri
			}

			console.warn(`⚠️ No document URI found for task ${taskId}`)
			return null
		} catch (error) {
			console.error("Failed to get document URI from task ID:", error)
			return null
		}
	}

	/**
	 * 获取系统状态
	 */
	async getSystemStatus(): Promise<{
		initialized: boolean
		activeDocuments: number
		totalSuggestions: number
		activeAnalyses: number
		cacheStats: any
	}> {
		const cacheStats = this.cacheManager.getCacheStats()
		const analysisQueue = this.analysisEngine.getAnalysisQueue()

		return {
			initialized: this.isInitialized,
			activeDocuments: await this.countActiveDocuments(),
			totalSuggestions: await this.countTotalSuggestions(),
			activeAnalyses: analysisQueue.filter((t) => t.status === "running").length,
			cacheStats,
		}
	}

	/**
	 * 获取分析队列状态
	 */
	getAnalysisQueueStatus(): any[] {
		return this.analysisEngine.getAnalysisQueue()
	}

	/**
	 * 清理缓存
	 */
	clearCache(): void {
		this.cacheManager.clearAll()
	}

	/**
	 * 统计活跃文档数量
	 */
	private async countActiveDocuments(): Promise<number> {
		try {
			// 获取当前打开的文档数量
			const openDocuments = vscode.workspace.textDocuments.length
			console.log(`📊 Active documents count: ${openDocuments}`)
			return openDocuments
		} catch (error) {
			console.warn("Failed to count active documents:", error)
			return 0
		}
	}

	/**
	 * 统计总建议数量
	 */
	private async countTotalSuggestions(): Promise<number> {
		try {
			// 统计所有打开文档的建议数量
			let totalCount = 0

			for (const document of vscode.workspace.textDocuments) {
				const suggestions = await this.suggestionManager.getSuggestions(document.uri.toString())
				totalCount += suggestions.length
			}

			console.log(`📊 Total suggestions count: ${totalCount}`)
			return totalCount
		} catch (error) {
			console.warn("Failed to count total suggestions:", error)
			return 0
		}
	}
}
