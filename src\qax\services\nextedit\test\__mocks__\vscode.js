/**
 * VSCode API Mock for Jest Testing
 * 这个文件模拟了 VSCode API，用于在 Jest 测试环境中运行测试
 */

// Mock Position class
class Position {
	constructor(line, character) {
		this.line = line
		this.character = character
	}

	isBefore() {
		return false
	}
	isBeforeOrEqual() {
		return false
	}
	isAfter() {
		return false
	}
	isAfterOrEqual() {
		return false
	}
	isEqual() {
		return false
	}
	compareTo() {
		return 0
	}
	translate() {
		return new Position(0, 0)
	}
	with() {
		return new Position(0, 0)
	}
}

// Mock Range class
class Range {
	constructor(startLine, startChar, endLine, endChar) {
		this.start = new Position(startLine, startChar)
		this.end = new Position(endLine, endChar)
		this.isEmpty = startLine === endLine && startChar === endChar
		this.isSingleLine = startLine === endLine
	}

	contains() {
		return false
	}
	isEqual() {
		return false
	}
	intersection() {
		return undefined
	}
	union() {
		return new Range(0, 0, 0, 0)
	}
	with() {
		return new Range(0, 0, 0, 0)
	}
}

// Mock Uri class
class Uri {
	constructor(scheme, authority, path, query, fragment) {
		this.scheme = scheme || "file"
		this.authority = authority || ""
		this.path = path || ""
		this.query = query || ""
		this.fragment = fragment || ""
		this.fsPath = path || ""
	}

	static parse(value) {
		return new Uri("file", "", value, "", "")
	}

	static file(path) {
		return new Uri("file", "", path, "", "")
	}

	toString() {
		return `${this.scheme}://${this.authority}${this.path}`
	}

	with() {
		return new Uri()
	}
}

// Mock Diagnostic class
class Diagnostic {
	constructor(range, message, severity) {
		this.range = range
		this.message = message
		this.severity = severity || DiagnosticSeverity.Error
		this.source = ""
		this.code = ""
		this.relatedInformation = []
	}
}

// Mock DiagnosticSeverity enum
const DiagnosticSeverity = {
	Error: 0,
	Warning: 1,
	Information: 2,
	Hint: 3,
}

// Mock TextDocument
const createMockTextDocument = (uri, content) => ({
	uri: typeof uri === "string" ? Uri.parse(uri) : uri,
	fileName: typeof uri === "string" ? uri : uri.toString(),
	isUntitled: false,
	languageId: "typescript",
	version: 1,
	isDirty: false,
	isClosed: false,
	save: () => Promise.resolve(true),
	eol: 1,
	lineCount: content.split("\n").length,
	lineAt: (line) => ({
		lineNumber: line,
		text: content.split("\n")[line] || "",
		range: new Range(line, 0, line, (content.split("\n")[line] || "").length),
		rangeIncludingLineBreak: new Range(line, 0, line + 1, 0),
		firstNonWhitespaceCharacterIndex: 0,
		isEmptyOrWhitespace: (content.split("\n")[line] || "").trim().length === 0,
	}),
	offsetAt: () => 0,
	positionAt: () => new Position(0, 0),
	getText: () => content,
	getWordRangeAtPosition: () => undefined,
	validateRange: (range) => range,
	validatePosition: (position) => position,
})

// Mock workspace
const workspace = {
	textDocuments: [],
	workspaceFolders: [],
	getConfiguration: () => ({
		get: () => undefined,
		has: () => false,
		inspect: () => undefined,
		update: () => Promise.resolve(),
	}),
	applyEdit: () => Promise.resolve(true),
	openTextDocument: () => Promise.resolve(createMockTextDocument("file:///test.ts", "")),
	findFiles: () => Promise.resolve([]),
	createFileSystemWatcher: () => ({
		onDidCreate: () => ({ dispose: () => {} }),
		onDidChange: () => ({ dispose: () => {} }),
		onDidDelete: () => ({ dispose: () => {} }),
		dispose: () => {},
	}),
}

// Mock languages
const languages = {
	getDiagnostics: () => [],
	createDiagnosticCollection: () => ({
		set: () => {},
		delete: () => {},
		clear: () => {},
		dispose: () => {},
	}),
	registerHoverProvider: () => ({ dispose: () => {} }),
	registerCompletionItemProvider: () => ({ dispose: () => {} }),
}

// Mock window
const window = {
	showInformationMessage: () => Promise.resolve(),
	showWarningMessage: () => Promise.resolve(),
	showErrorMessage: () => Promise.resolve(),
	createOutputChannel: () => ({
		append: () => {},
		appendLine: () => {},
		clear: () => {},
		show: () => {},
		hide: () => {},
		dispose: () => {},
	}),
}

// Mock commands
const commands = {
	registerCommand: () => ({ dispose: () => {} }),
	executeCommand: () => Promise.resolve(),
}

// Mock ExtensionContext
const createMockExtensionContext = () => ({
	subscriptions: [],
	workspaceState: {
		get: () => undefined,
		update: () => Promise.resolve(),
		keys: () => [],
	},
	globalState: {
		get: () => undefined,
		update: () => Promise.resolve(),
		keys: () => [],
		setKeysForSync: () => {},
	},
	secrets: {
		get: () => Promise.resolve(undefined),
		store: () => Promise.resolve(),
		delete: () => Promise.resolve(),
	},
	extensionUri: Uri.parse("file:///test"),
	extensionPath: "/test",
	environmentVariableCollection: {
		getScoped: () => ({}),
	},
	asAbsolutePath: (relativePath) => `/test/${relativePath}`,
	storageUri: Uri.parse("file:///test/storage"),
	storagePath: "/test/storage",
	globalStorageUri: Uri.parse("file:///test/global"),
	globalStoragePath: "/test/global",
	logUri: Uri.parse("file:///test/log"),
	logPath: "/test/log",
})

// Export all mocked VSCode API
module.exports = {
	Position,
	Range,
	Uri,
	Diagnostic,
	DiagnosticSeverity,
	workspace,
	languages,
	window,
	commands,
	createMockTextDocument,
	createMockExtensionContext,

	// Export commonly used enums and constants
	EndOfLine: {
		LF: 1,
		CRLF: 2,
	},

	TextDocumentSaveReason: {
		Manual: 1,
		AfterDelay: 2,
		FocusOut: 3,
	},

	CompletionItemKind: {
		Text: 0,
		Method: 1,
		Function: 2,
		Constructor: 3,
		Field: 4,
		Variable: 5,
		Class: 6,
		Interface: 7,
		Module: 8,
		Property: 9,
		Unit: 10,
		Value: 11,
		Enum: 12,
		Keyword: 13,
		Snippet: 14,
		Color: 15,
		File: 16,
		Reference: 17,
	},
}
