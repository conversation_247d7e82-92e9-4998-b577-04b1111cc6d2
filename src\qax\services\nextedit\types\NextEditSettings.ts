/**
 * QAX Next Edit - 配置数据结构
 */

import { AnalysisGoal, AnalysisType } from "./AnalysisContext"
import { SuggestionCategory, ImpactLevel } from "./Suggestion"

/**
 * QAX Next Edit 主配置接口
 */
export interface NextEditSettings {
	/** 总开关 */
	enabled: boolean

	/** 模型推荐配置 */
	modelRecommendation: ModelRecommendationConfig

	/** AST+LSP分析配置 */
	astLspAnalysis: ASTLSPAnalysisConfig

	/** 建议过滤配置 */
	suggestionFilter: SuggestionFilterConfig

	/** 触发机制配置 */
	triggerConfig: TriggerConfig

	/** 性能配置 */
	performance: PerformanceConfig

	/** UI配置 */
	ui: UIConfig

	/** 调试配置 */
	debug: DebugConfig
}

/**
 * 模型推荐配置
 */
export interface ModelRecommendationConfig {
	/** 是否启用模型推荐 */
	enabled: boolean
	/** 防抖时间（默认3000ms） */
	debounceMs: number
	/** 空闲时间要求（默认3000ms） */
	idleTimeMs: number
	/** 最大上下文行数 */
	maxContextLines: number
	/** API端点 */
	apiEndpoint: string
	/** 模型ID */
	modelId: string
	/** 最大token数 */
	maxTokens: number
	/** 温度参数 */
	temperature: number
	/** 请求超时时间 */
	timeout: number
	/** 重试次数 */
	retryAttempts: number
	/** 是否启用缓存 */
	enableCache: boolean
	/** 分析目标 */
	analysisGoals: AnalysisGoal[]
}

/**
 * AST+LSP分析配置
 */
export interface ASTLSPAnalysisConfig {
	/** 是否启用AST+LSP分析 */
	enabled: boolean
	/** 防抖时间（默认1000ms） */
	debounceMs: number
	/** 响应时间目标（默认100ms） */
	responseTimeTarget: number
	/** 是否启用增量缓存 */
	enableIncrementalCache: boolean
	/** 最大缓存大小（MB） */
	maxCacheSize: number
	/** 缓存过期时间 */
	cacheExpirationMs: number
	/** 是否启用并行解析 */
	enableParallelParsing: boolean
	/** 支持的语言 */
	supportedLanguages: string[]
	/** Tree-sitter配置 */
	treeSitterConfig: TreeSitterConfig
}

/**
 * Tree-sitter配置
 */
export interface TreeSitterConfig {
	/** 是否启用增量解析 */
	enableIncrementalParsing: boolean
	/** 解析超时时间 */
	parseTimeout: number
	/** 最大文件大小（字节） */
	maxFileSize: number
	/** 语言特定配置 */
	languageConfigs: Record<string, LanguageConfig>
}

/**
 * 语言配置
 */
export interface LanguageConfig {
	/** 查询文件路径 */
	queryPath: string
	/** 是否启用 */
	enabled: boolean
	/** 特定配置 */
	options: Record<string, any>
}

/**
 * 建议过滤配置
 */
export interface SuggestionFilterConfig {
	/** 最小置信度阈值 */
	minConfidence: number
	/** 每个文档最大建议数 */
	maxSuggestionsPerDocument: number
	/** 是否启用重复过滤 */
	enableDuplicateFilter: boolean
	/** 是否启用质量过滤 */
	enableQualityFilter: boolean
	/** 分类过滤器 */
	categoryFilters: Record<SuggestionCategory, boolean>
	/** 影响级别过滤器 */
	impactLevelFilters: Record<ImpactLevel, boolean>
	/** 黑名单模式 */
	blacklistPatterns: string[]
	/** 白名单模式 */
	whitelistPatterns: string[]
}

/**
 * 触发配置
 */
export interface TriggerConfig {
	/** 是否集成autocomplete */
	enableAutocompleteIntegration: boolean
	/** 行末触发autocomplete */
	lineEndTriggerAutocomplete: boolean
	/** 行中触发AST+LSP */
	lineMiddleTriggerASTLSP: boolean
	/** 文档空闲触发模型 */
	documentIdleTriggerModel: boolean
	/** 防止重复触发 */
	preventDuplicateTriggers: boolean
	/** 最大并发分析数 */
	maxConcurrentAnalyses: number
}

/**
 * 性能配置
 */
export interface PerformanceConfig {
	/** 是否启用性能指标 */
	enableMetrics: boolean
	/** 指标保留天数 */
	metricsRetentionDays: number
	/** 是否启用性能分析 */
	enableProfiling: boolean
	/** 内存限制（MB） */
	memoryLimitMB: number
	/** CPU节流阈值 */
	cpuThrottleThreshold: number
	/** 是否启用资源监控 */
	enableResourceMonitoring: boolean
}

/**
 * UI配置
 */
export interface UIConfig {
	/** 显示建议通知 */
	showSuggestionNotifications: boolean
	/** 建议显示模式 */
	suggestionDisplayMode: "inline" | "popup" | "panel"
	/** 启用悬停预览 */
	enableHoverPreview: boolean
	/** 启用快速操作 */
	enableQuickActions: boolean
	/** 主题集成 */
	themeIntegration: boolean
	/** 启用动画 */
	animationEnabled: boolean
}

/**
 * 调试配置
 */
export interface DebugConfig {
	/** 启用日志 */
	enableLogging: boolean
	/** 日志级别 */
	logLevel: "error" | "warn" | "info" | "debug"
	/** 启用追踪 */
	enableTracing: boolean
	/** 保存分析结果 */
	saveAnalysisResults: boolean
	/** 启用性能日志 */
	enablePerformanceLogging: boolean
	/** 调试输出路径 */
	debugOutputPath?: string
}

/**
 * 默认配置
 */
export const DEFAULT_NEXT_EDIT_SETTINGS: NextEditSettings = {
	enabled: true,

	modelRecommendation: {
		enabled: true,
		debounceMs: 3000,
		idleTimeMs: 3000,
		maxContextLines: 100,
		apiEndpoint: "https://aip.b.qianxin-inc.cn/v2",
		modelId: "Qwen3-Coder-480B-A35B-Instruct",
		maxTokens: 2000,
		temperature: 0.1,
		timeout: 30000,
		retryAttempts: 2,
		enableCache: true,
		analysisGoals: [
			AnalysisGoal.LOGIC_COMPLETION,
			AnalysisGoal.FUNCTION_IMPLEMENTATION,
			AnalysisGoal.VARIABLE_USAGE,
			AnalysisGoal.CODE_FORMATTING,
			AnalysisGoal.LINT_ISSUES,
		],
	},

	astLspAnalysis: {
		enabled: true,
		debounceMs: 1000,
		responseTimeTarget: 100,
		enableIncrementalCache: true,
		maxCacheSize: 50,
		cacheExpirationMs: 300000, // 5分钟
		enableParallelParsing: true,
		supportedLanguages: [
			"typescript",
			"javascript",
			"python",
			"rust",
			"go",
			"cpp",
			"c",
			"csharp",
			"java",
			"php",
			"swift",
			"kotlin",
		],
		treeSitterConfig: {
			enableIncrementalParsing: true,
			parseTimeout: 5000,
			maxFileSize: 1024 * 1024, // 1MB
			languageConfigs: {},
		},
	},

	suggestionFilter: {
		minConfidence: 0.6,
		maxSuggestionsPerDocument: 10,
		enableDuplicateFilter: true,
		enableQualityFilter: true,
		categoryFilters: {
			[SuggestionCategory.LOGIC_COMPLETION]: true,
			[SuggestionCategory.FUNCTION_IMPLEMENTATION]: true,
			[SuggestionCategory.VARIABLE_USAGE]: true,
			[SuggestionCategory.CODE_FORMATTING]: true,
			[SuggestionCategory.LINT_ISSUES]: true,
			[SuggestionCategory.REFACTORING]: true,
			[SuggestionCategory.PERFORMANCE]: true,
			[SuggestionCategory.SECURITY]: true,
			[SuggestionCategory.DOCUMENTATION]: false,
		},
		impactLevelFilters: {
			[ImpactLevel.LOW]: true,
			[ImpactLevel.MEDIUM]: true,
			[ImpactLevel.HIGH]: true,
			[ImpactLevel.CRITICAL]: false,
		},
		blacklistPatterns: [],
		whitelistPatterns: [],
	},

	triggerConfig: {
		enableAutocompleteIntegration: true,
		lineEndTriggerAutocomplete: true,
		lineMiddleTriggerASTLSP: true,
		documentIdleTriggerModel: true,
		preventDuplicateTriggers: true,
		maxConcurrentAnalyses: 3,
	},

	performance: {
		enableMetrics: true,
		metricsRetentionDays: 7,
		enableProfiling: false,
		memoryLimitMB: 100,
		cpuThrottleThreshold: 80,
		enableResourceMonitoring: true,
	},

	ui: {
		showSuggestionNotifications: true,
		suggestionDisplayMode: "inline",
		enableHoverPreview: true,
		enableQuickActions: true,
		themeIntegration: true,
		animationEnabled: true,
	},

	debug: {
		enableLogging: true,
		logLevel: "info",
		enableTracing: false,
		saveAnalysisResults: false,
		enablePerformanceLogging: true,
	},
}

/**
 * 配置验证错误
 */
export interface ConfigValidationError {
	/** 字段路径 */
	field: string
	/** 错误消息 */
	message: string
	/** 当前值 */
	value: any
}

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
	/** 是否有效 */
	isValid: boolean
	/** 错误列表 */
	errors: ConfigValidationError[]
	/** 警告列表 */
	warnings: ConfigValidationError[]
}

/**
 * 配置工具类
 */
export class NextEditConfigValidator {
	/**
	 * 验证配置
	 */
	static validate(config: Partial<NextEditSettings>): ConfigValidationResult {
		const errors: ConfigValidationError[] = []
		const warnings: ConfigValidationError[] = []

		// 验证模型推荐配置
		if (config.modelRecommendation) {
			const modelConfig = config.modelRecommendation

			if (modelConfig.debounceMs !== undefined && (modelConfig.debounceMs < 0 || modelConfig.debounceMs > 10000)) {
				errors.push({
					field: "modelRecommendation.debounceMs",
					message: "Debounce time must be between 0ms and 10000ms",
					value: modelConfig.debounceMs,
				})
			}

			if (modelConfig.maxTokens !== undefined && (modelConfig.maxTokens < 100 || modelConfig.maxTokens > 8000)) {
				warnings.push({
					field: "modelRecommendation.maxTokens",
					message: "Max tokens should be between 100 and 8000",
					value: modelConfig.maxTokens,
				})
			}
		}

		// 验证AST+LSP配置
		if (config.astLspAnalysis) {
			const astConfig = config.astLspAnalysis

			if (astConfig.responseTimeTarget !== undefined && astConfig.responseTimeTarget > 1000) {
				warnings.push({
					field: "astLspAnalysis.responseTimeTarget",
					message: "Response time target over 1000ms may affect user experience",
					value: astConfig.responseTimeTarget,
				})
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
		}
	}

	/**
	 * 合并配置
	 */
	static merge(base: NextEditSettings, override: Partial<NextEditSettings>): NextEditSettings {
		return {
			...base,
			...override,
			modelRecommendation: {
				...base.modelRecommendation,
				...override.modelRecommendation,
			},
			astLspAnalysis: {
				...base.astLspAnalysis,
				...override.astLspAnalysis,
			},
			suggestionFilter: {
				...base.suggestionFilter,
				...override.suggestionFilter,
			},
			triggerConfig: {
				...base.triggerConfig,
				...override.triggerConfig,
			},
			performance: {
				...base.performance,
				...override.performance,
			},
			ui: {
				...base.ui,
				...override.ui,
			},
			debug: {
				...base.debug,
				...override.debug,
			},
		}
	}
}
