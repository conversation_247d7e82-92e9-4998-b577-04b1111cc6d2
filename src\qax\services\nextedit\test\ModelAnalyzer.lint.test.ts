/**
 * QAX Next Edit - ModelAnalyzer Lint 集成测试
 * 测试 ModelAnalyzer 收集和处理 VS Code lint 问题的功能
 */

import * as vscode from "vscode"
import { ModelAnalyzer } from "../analyzers/ModelAnalyzer"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { expect, createMockDocument, createMockAnalysisContext, createMockPosition } from "./test-utils"

// 模拟VSCode API
const mockVscode = {
	Range: class Range {
		constructor(
			public startLine: number,
			public startChar: number,
			public endLine: number,
			public endChar: number,
		) {
			this.start = { line: startLine, character: startChar }
			this.end = { line: endLine, character: endChar }
		}
		start: { line: number; character: number }
		end: { line: number; character: number }
	},
	Diagnostic: class Diagnostic {
		constructor(
			public range: any,
			public message: string,
			public severity?: number,
		) {
			this.source = "eslint"
			this.code = "no-unused-vars"
		}
		source?: string
		code?: string
	},
	DiagnosticSeverity: {
		Error: 0,
		Warning: 1,
		Information: 2,
		Hint: 3,
	},
	languages: {
		getDiagnostics: () => [],
	},
}

describe("ModelAnalyzer Lint Integration", () => {
	let analyzer: ModelAnalyzer
	const testDocumentUri = "file:///test/example.ts"

	beforeEach(() => {
		analyzer = new ModelAnalyzer(DEFAULT_NEXT_EDIT_SETTINGS)
	})

	afterEach(() => {
		analyzer.dispose()
	})

	describe("Lint 问题收集", () => {
		test("应该能够收集 VS Code 诊断信息", async () => {
			const content = `
function testFunction() {
    const unusedVar = "not used";
    console.log("Hello World")
    
    let message = "test";
    console.log(message);
}
`
			const document = createMockDocument(testDocumentUri, content)

			// 使用反射访问私有方法进行测试
			const collectLintIssues = (analyzer as any).collectLintIssues.bind(analyzer)
			const lintIssues = await collectLintIssues(document)

			expect(Array.isArray(lintIssues)).toBe(true)
			expect(lintIssues.length).toBeGreaterThan(0)

			// 验证 lint 问题的结构
			const firstIssue = lintIssues[0]
			expect(firstIssue.message).toBeDefined()
			expect(firstIssue.range).toBeDefined()
			expect(firstIssue.severity).toBeDefined()

			console.log(`收集到 ${lintIssues.length} 个 lint 问题`)
		})

		test("应该能够过滤 lint 相关的诊断信息", async () => {
			const document = createMockDocument(testDocumentUri, "test content")

			// 模拟包含不同来源的诊断信息
			const originalGetDiagnostics = mockVscode.languages.getDiagnostics
			;(mockVscode.languages.getDiagnostics as any) = () => {
				const eslintDiag = new mockVscode.Diagnostic(new mockVscode.Range(0, 0, 0, 5), "ESLint error") as any
				eslintDiag.source = "eslint"

				const tsDiag = new mockVscode.Diagnostic(
					new mockVscode.Range(1, 0, 1, 5),
					"TypeScript error",
					mockVscode.DiagnosticSeverity.Error,
				) as any
				tsDiag.source = "typescript"

				const infoDiag = new mockVscode.Diagnostic(
					new mockVscode.Range(2, 0, 2, 5),
					"Info message",
					mockVscode.DiagnosticSeverity.Information,
				) as any
				infoDiag.source = "other"

				return [eslintDiag, tsDiag, infoDiag]
			}

			const collectLintIssues = (analyzer as any).collectLintIssues.bind(analyzer)
			const lintIssues = await collectLintIssues(document)

			// 恢复原始方法
			mockVscode.languages.getDiagnostics = originalGetDiagnostics

			expect(lintIssues.length).toBeGreaterThan(1) // 至少包含 ESLint 和 TypeScript 错误

			// 验证 ESLint 问题被包含
			const eslintIssue = lintIssues.find((issue: any) => issue.source === "eslint")
			expect(eslintIssue).toBeDefined()

			console.log("Lint 过滤测试通过")
		})

		test("应该能够按行号排序 lint 问题", async () => {
			const document = createMockDocument(testDocumentUri, "test content")

			// 模拟乱序的诊断信息
			const originalGetDiagnostics = mockVscode.languages.getDiagnostics
			;(mockVscode.languages.getDiagnostics as any) = () => {
				const diag1 = new mockVscode.Diagnostic(new mockVscode.Range(5, 0, 5, 5), "Line 5 error") as any
				diag1.source = "eslint"

				const diag2 = new mockVscode.Diagnostic(new mockVscode.Range(1, 0, 1, 5), "Line 1 error") as any
				diag2.source = "eslint"

				const diag3 = new mockVscode.Diagnostic(new mockVscode.Range(3, 0, 3, 5), "Line 3 error") as any
				diag3.source = "eslint"

				return [diag1, diag2, diag3]
			}

			const collectLintIssues = (analyzer as any).collectLintIssues.bind(analyzer)
			const lintIssues = await collectLintIssues(document)

			// 恢复原始方法
			mockVscode.languages.getDiagnostics = originalGetDiagnostics

			// 验证排序
			for (let i = 1; i < lintIssues.length; i++) {
				expect(lintIssues[i].range.start.line >= lintIssues[i - 1].range.start.line).toBe(true)
			}

			console.log("Lint 排序测试通过")
		})
	})

	describe("Lint 问题格式化", () => {
		test("应该能够格式化 lint 问题为文本", () => {
			const diag1 = new mockVscode.Diagnostic(
				new mockVscode.Range(2, 6, 2, 15),
				"Variable 'unusedVar' is defined but never used.",
				mockVscode.DiagnosticSeverity.Warning,
			) as any
			diag1.source = "eslint"
			diag1.code = "no-unused-vars"

			const diag2 = new mockVscode.Diagnostic(
				new mockVscode.Range(5, 0, 5, 8),
				"Missing semicolon.",
				mockVscode.DiagnosticSeverity.Error,
			) as any
			diag2.source = "eslint"
			diag2.code = "semi"

			const mockLintIssues = [diag1, diag2]

			const formatLintIssues = (analyzer as any).formatLintIssues.bind(analyzer)
			const formattedText = formatLintIssues(mockLintIssues)

			expect(typeof formattedText).toBe("string")
			expect(formattedText).toContain("检测到 2 个 lint 问题")
			expect(formattedText).toContain("第3行:7列") // 2+1, 6+1
			expect(formattedText).toContain("第6行:1列") // 5+1, 0+1
			expect(formattedText).toContain("警告")
			expect(formattedText).toContain("错误")
			expect(formattedText).toContain("eslint")
			expect(formattedText).toContain("no-unused-vars")
			expect(formattedText).toContain("semi")

			console.log("Lint 格式化测试通过")
		})

		test("应该能够处理空的 lint 问题列表", () => {
			const formatLintIssues = (analyzer as any).formatLintIssues.bind(analyzer)
			const formattedText = formatLintIssues([])

			expect(formattedText).toBe("无检测到的 lint 问题")

			console.log("空 lint 列表处理测试通过")
		})

		test("应该能够正确显示严重程度", () => {
			const getSeverityText = (analyzer as any).getSeverityText.bind(analyzer)

			expect(getSeverityText(mockVscode.DiagnosticSeverity.Error)).toBe("错误")
			expect(getSeverityText(mockVscode.DiagnosticSeverity.Warning)).toBe("警告")
			expect(getSeverityText(mockVscode.DiagnosticSeverity.Information)).toBe("信息")
			expect(getSeverityText(mockVscode.DiagnosticSeverity.Hint)).toBe("提示")
			expect(getSeverityText(undefined)).toBe("未知")

			console.log("严重程度显示测试通过")
		})
	})

	describe("提示词集成", () => {
		test("应该能够在提示词中包含 lint 问题", async () => {
			const content = `
function testFunction() {
    const unusedVar = "not used";
    console.log("Hello World")
}
`
			const document = createMockDocument(testDocumentUri, content)

			const context = createMockAnalysisContext(document, content)

			const prompt = await analyzer.buildOptimizedPrompt(context)

			expect(typeof prompt).toBe("string")
			expect(prompt).toContain("VS Code 检测到的 Lint 问题")
			expect(prompt).toContain("检测到") // 应该包含格式化的 lint 问题

			console.log("提示词 lint 集成测试通过")
		})

		test("应该能够处理没有 lint 问题的情况", async () => {
			const document = createMockDocument(testDocumentUri, "clean code")

			// 模拟没有诊断信息
			const originalGetDiagnostics = mockVscode.languages.getDiagnostics
			mockVscode.languages.getDiagnostics = () => []

			const context = createMockAnalysisContext(document, "clean code")

			const prompt = await analyzer.buildOptimizedPrompt(context)

			// 恢复原始方法
			mockVscode.languages.getDiagnostics = originalGetDiagnostics

			expect(prompt).toContain("无检测到的 lint 问题")

			console.log("无 lint 问题处理测试通过")
		})
	})

	describe("错误处理", () => {
		test("应该能够处理 getDiagnostics 抛出的错误", async () => {
			const document = createMockDocument(testDocumentUri, "test content")

			// 模拟 getDiagnostics 抛出错误
			const originalGetDiagnostics = mockVscode.languages.getDiagnostics
			mockVscode.languages.getDiagnostics = () => {
				throw new Error("Diagnostics error")
			}

			const collectLintIssues = (analyzer as any).collectLintIssues.bind(analyzer)
			const lintIssues = await collectLintIssues(document)

			// 恢复原始方法
			mockVscode.languages.getDiagnostics = originalGetDiagnostics

			// 应该返回空数组而不是抛出错误
			expect(Array.isArray(lintIssues)).toBe(true)
			expect(lintIssues.length).toBe(0)

			console.log("错误处理测试通过")
		})

		test("应该能够处理无效的诊断数据", () => {
			const invalidDiagnostics = [
				null,
				undefined,
				{ message: "test" }, // 缺少 range
				{ range: new mockVscode.Range(0, 0, 0, 5) }, // 缺少 message
			]

			const formatLintIssues = (analyzer as any).formatLintIssues.bind(analyzer)

			// 应该不会抛出错误
			expect(() => {
				formatLintIssues(invalidDiagnostics.filter((d) => d && d.message && d.range))
			}).not.toThrow()

			console.log("无效数据处理测试通过")
		})
	})
})
