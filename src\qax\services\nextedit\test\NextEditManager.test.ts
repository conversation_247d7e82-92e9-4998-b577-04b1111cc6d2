/**
 * QAX Next Edit - 测试文件
 * 验证核心功能的正确性
 */

import * as assert from "assert"
import * as vscode from "vscode"
import { NextEditManager, NextEditError } from "../NextEditManager"
import { SuggestionBuilder, SuggestionType, SuggestionSource, SuggestionCategory, ImpactLevel } from "../types/Suggestion"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { expect, createMockDocument, createMockAnalysisContext, createMockPosition } from "./test-utils"

/**
 * 模拟文档
 */
class MockTextDocument implements vscode.TextDocument {
	uri: vscode.Uri
	fileName: string
	isUntitled: boolean = false
	languageId: string = "typescript"
	version: number = 1
	isDirty: boolean = false
	isClosed: boolean = false
	eol: vscode.EndOfLine = vscode.EndOfLine.LF
	lineCount: number

	private content: string

	constructor(uri: string, content: string) {
		this.uri = vscode.Uri.parse(uri)
		this.fileName = this.uri.fsPath
		this.content = content
		this.lineCount = content.split("\n").length
	}

	save(): Thenable<boolean> {
		return Promise.resolve(true)
	}

	lineAt(line: number | vscode.Position): vscode.TextLine {
		const lineNumber = typeof line === "number" ? line : line.line
		const lines = this.content.split("\n")
		const text = lines[lineNumber] || ""

		return {
			lineNumber,
			text,
			range: new vscode.Range(lineNumber, 0, lineNumber, text.length),
			rangeIncludingLineBreak: new vscode.Range(lineNumber, 0, lineNumber + 1, 0),
			firstNonWhitespaceCharacterIndex: text.search(/\S/),
			isEmptyOrWhitespace: text.trim().length === 0,
		}
	}

	offsetAt(position: vscode.Position): number {
		const lines = this.content.split("\n")
		let offset = 0
		for (let i = 0; i < position.line; i++) {
			offset += lines[i].length + 1 // +1 for newline
		}
		return offset + position.character
	}

	positionAt(offset: number): vscode.Position {
		const lines = this.content.split("\n")
		let currentOffset = 0

		for (let line = 0; line < lines.length; line++) {
			const lineLength = lines[line].length
			if (currentOffset + lineLength >= offset) {
				return new vscode.Position(line, offset - currentOffset)
			}
			currentOffset += lineLength + 1 // +1 for newline
		}

		return new vscode.Position(lines.length - 1, lines[lines.length - 1].length)
	}

	getText(range?: vscode.Range): string {
		if (!range) {
			return this.content
		}

		const start = this.offsetAt(range.start)
		const end = this.offsetAt(range.end)
		return this.content.substring(start, end)
	}

	getWordRangeAtPosition(position: vscode.Position, regex?: RegExp): vscode.Range | undefined {
		return undefined
	}

	validateRange(range: vscode.Range): vscode.Range {
		return range
	}

	validatePosition(position: vscode.Position): vscode.Position {
		return position
	}
}

/**
 * 模拟扩展上下文
 */
class MockExtensionContext implements vscode.ExtensionContext {
	subscriptions: vscode.Disposable[] = []
	workspaceState: vscode.Memento = {} as any
	globalState: vscode.Memento & { setKeysForSync(keys: readonly string[]): void } = {
		setKeysForSync: () => {},
		get: () => undefined,
		update: () => Promise.resolve(),
		keys: () => [],
	} as any
	secrets: vscode.SecretStorage = {} as any
	extensionUri: vscode.Uri = vscode.Uri.parse("file:///test")
	extensionPath: string = "/test"
	environmentVariableCollection: vscode.GlobalEnvironmentVariableCollection = {
		getScoped: () => ({}) as any,
	} as any
	asAbsolutePath(relativePath: string): string {
		return `/test/${relativePath}`
	}
	storageUri: vscode.Uri = vscode.Uri.parse("file:///test/storage")
	storagePath: string = "/test/storage"
	globalStorageUri: vscode.Uri = vscode.Uri.parse("file:///test/global")
	globalStoragePath: string = "/test/global"
	logUri: vscode.Uri = vscode.Uri.parse("file:///test/log")
	logPath: string = "/test/log"
	extensionMode: vscode.ExtensionMode = vscode.ExtensionMode.Test
	extension: vscode.Extension<any> = {} as any
}

describe("NextEditManager", () => {
	let manager: NextEditManager
	let mockContext: MockExtensionContext
	let mockDocument: MockTextDocument

	beforeEach(() => {
		manager = new NextEditManager(DEFAULT_NEXT_EDIT_SETTINGS)
		mockContext = new MockExtensionContext()
		mockDocument = new MockTextDocument("file:///test/example.ts", 'function example() {\n  console.log("hello");\n}\n')
	})

	afterEach(async () => {
		await manager.dispose()
	})

	describe("初始化", () => {
		it("应该成功初始化", async () => {
			await manager.initialize(mockContext)
			assert.strictEqual(manager.isAnalyzing("file:///test/example.ts"), false)
		})

		it("应该处理重复初始化", async () => {
			await manager.initialize(mockContext)
			await manager.initialize(mockContext) // 不应该抛出错误
		})
	})

	describe("文档生命周期", () => {
		beforeEach(async () => {
			await manager.initialize(mockContext)
		})

		it("应该处理文档打开", async () => {
			await manager.onDocumentOpened(mockDocument)
			const suggestions = manager.getSuggestions(mockDocument.uri.toString())
			assert.strictEqual(Array.isArray(suggestions), true)
		})

		it("应该处理文档关闭", async () => {
			await manager.onDocumentOpened(mockDocument)
			await manager.onDocumentClosed(mockDocument)
			// 文档关闭后应该清理相关数据
		})

		it("应该处理文档变更", async () => {
			await manager.onDocumentOpened(mockDocument)

			const changes: vscode.TextDocumentContentChangeEvent[] = [
				{
					range: new vscode.Range(1, 0, 1, 0),
					rangeOffset: 20,
					rangeLength: 0,
					text: "  // new comment\n",
				},
			]

			await manager.onDocumentChanged(mockDocument, changes)
			// 应该触发相应的分析逻辑
		})
	})

	describe("建议管理", () => {
		beforeEach(async () => {
			await manager.initialize(mockContext)
			await manager.onDocumentOpened(mockDocument)
		})

		it("应该获取空的建议列表", () => {
			const suggestions = manager.getSuggestions(mockDocument.uri.toString())
			assert.strictEqual(suggestions.length, 0)
		})

		it("应该忽略所有建议", async () => {
			await manager.dismissAllSuggestions(mockDocument.uri.toString())
			const suggestions = manager.getSuggestions(mockDocument.uri.toString())
			assert.strictEqual(suggestions.length, 0)
		})
	})

	describe("错误处理", () => {
		it("应该处理初始化错误", async () => {
			let errorCaught = false
			manager.onError((error) => {
				errorCaught = true
				assert.strictEqual(error instanceof NextEditError, true)
			})

			// 模拟初始化错误的情况
			// 这里可能需要更复杂的模拟逻辑
		})
	})

	describe("配置管理", () => {
		it("应该更新配置", () => {
			const newSettings = {
				...DEFAULT_NEXT_EDIT_SETTINGS,
				enabled: false,
			}

			manager.updateConfiguration(newSettings)
			// 验证配置是否正确更新
		})
	})
})

describe("SuggestionBuilder", () => {
	it("应该创建有效的建议", () => {
		const suggestion = SuggestionBuilder.create(
			"add",
			"Add error handling",
			"function example() {",
			"after",
			"try {\n  // existing code\n} catch (error) {\n  console.error(error);\n}",
			{
				confidence: 0.9,
				source: SuggestionSource.MODEL,
				category: SuggestionCategory.LOGIC_COMPLETION,
				impact: ImpactLevel.MEDIUM,
			},
		)

		assert.strictEqual(suggestion.type, "add")
		assert.strictEqual(suggestion.description, "Change: Add Add error handling")
		assert.strictEqual(suggestion.location.anchor, "function example() {")
		assert.strictEqual(suggestion.location.position, "after")
		assert.strictEqual(suggestion.confidence, 0.9)
		assert.strictEqual(suggestion.source, SuggestionSource.MODEL)
	})

	it("应该格式化描述信息", () => {
		const addSuggestion = SuggestionBuilder.create("add", "new function", "anchor", "after", "content")
		assert.strictEqual(addSuggestion.description, "Change: Add new function")

		const deleteSuggestion = SuggestionBuilder.create("delete", "unused variable", "anchor", "replace", "")
		assert.strictEqual(deleteSuggestion.description, "Change: Del unused variable")

		const modifySuggestion = SuggestionBuilder.create("modify", "oldName -> newName", "anchor", "replace", "newName")
		assert.strictEqual(modifySuggestion.description, "Change: oldName -> newName")
	})
})

describe("集成测试", () => {
	let manager: NextEditManager
	let mockContext: MockExtensionContext

	beforeEach(async () => {
		manager = new NextEditManager(DEFAULT_NEXT_EDIT_SETTINGS)
		mockContext = new MockExtensionContext()
		await manager.initialize(mockContext)
	})

	afterEach(async () => {
		await manager.dispose()
	})

	it("应该完成完整的工作流", async () => {
		// 1. 打开文档
		const document = new MockTextDocument("file:///test/workflow.ts", "function test() {\n  // TODO: implement\n}\n")

		await manager.onDocumentOpened(document)

		// 2. 模拟文档变更
		const changes: vscode.TextDocumentContentChangeEvent[] = [
			{
				range: new vscode.Range(1, 2, 1, 21),
				rangeOffset: 20,
				rangeLength: 19,
				text: 'console.log("implemented");',
			},
		]

		await manager.onDocumentChanged(document, changes)

		// 3. 验证状态
		const suggestions = manager.getSuggestions(document.uri.toString())
		assert.strictEqual(Array.isArray(suggestions), true)

		// 4. 关闭文档
		await manager.onDocumentClosed(document)
	})
})
