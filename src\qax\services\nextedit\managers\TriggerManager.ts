/**
 * QAX Next Edit - 触发管理器
 * 负责判断何时触发不同类型的分析，管理防抖机制，确保与 autocomplete 的互斥触发
 */

import * as vscode from "vscode"
import { AnalysisType } from "../types/AnalysisContext"
import { NextEditSettings } from "../types/NextEditSettings"
import { SuggestionManager } from "./SuggestionManager"
import { DebounceManager } from "../utils/DebounceManager"

/**
 * 输入上下文
 */
export interface InputContext {
	/** 行文本 */
	lineText: string
	/** 光标前的文本 */
	beforeCursor: string
	/** 光标后的文本 */
	afterCursor: string
	/** 是否在行首 */
	isAtLineStart: boolean
	/** 是否在行末 */
	isAtLineEnd: boolean
	/** 是否在行中间 */
	isInMiddle: boolean
	/** 语法上下文 */
	syntaxContext?: any
}

/**
 * 触发管理器
 */
export class TriggerManager {
	private settings: NextEditSettings
	private suggestionManager: SuggestionManager
	private debounceManager: DebounceManager
	private documentIdleTimes: Map<string, number> = new Map()
	private analysisInProgress: Map<string, Set<AnalysisType>> = new Map()
	private onTriggerCallbacks: ((documentUri: string, analysisType: AnalysisType) => void)[] = []
	private onAnalysisBlockedCallbacks: ((documentUri: string, reason: string) => void)[] = []

	constructor(settings: NextEditSettings, suggestionManager: SuggestionManager) {
		this.settings = settings
		this.suggestionManager = suggestionManager
		this.debounceManager = new DebounceManager()
	}

	/**
	 * 更新配置
	 */
	updateSettings(settings: NextEditSettings): void {
		this.settings = settings
	}

	/**
	 * 检查是否应该触发模型推荐
	 */
	async shouldTriggerModelRecommendation(document: vscode.TextDocument): Promise<boolean> {
		const documentUri = document.uri.toString()

		// 1. 检查是否启用
		if (!this.settings.modelRecommendation.enabled) {
			return false
		}

		// 2. 检查文档是否空闲3秒
		const idleTime = this.getDocumentIdleTime(documentUri)
		if (idleTime < this.settings.modelRecommendation.idleTimeMs) {
			return false
		}

		// 3. 检查是否有未执行的建议
		const hasUnexecuted = this.suggestionManager.hasUnexecutedSuggestions(documentUri)
		if (hasUnexecuted) {
			return false
		}

		// 4. 检查是否正在进行其他分析
		if (this.isAnalysisInProgress(documentUri)) {
			return false
		}

		// 5. 检查文档是否符合分析条件
		return this.isDocumentEligibleForAnalysis(document)
	}

	/**
	 * 检查是否应该触发AST+LSP分析
	 */
	async shouldTriggerASTLSPAnalysis(document: vscode.TextDocument, position: vscode.Position): Promise<boolean> {
		const documentUri = document.uri.toString()

		// 1. 检查是否启用
		if (!this.settings.astLspAnalysis.enabled) {
			return false
		}

		// 2. 检查输入位置是否在行中间
		if (!this.isPositionInLineMiddle(document, position)) {
			return false
		}

		// 3. 检查是否正在进行分析
		if (this.isAnalysisInProgress(documentUri, AnalysisType.AST_LSP)) {
			return false
		}

		// 4. 检查文档是否符合分析条件
		return this.isDocumentEligibleForAnalysis(document)
	}

	/**
	 * 检查是否应该触发autocomplete
	 */
	shouldTriggerAutocomplete(document: vscode.TextDocument, position: vscode.Position): boolean {
		if (!this.settings.triggerConfig.enableAutocompleteIntegration) {
			return false
		}

		return this.isPositionAtLineEnd(document, position)
	}

	/**
	 * 检查位置是否在行末
	 */
	isPositionAtLineEnd(document: vscode.TextDocument, position: vscode.Position): boolean {
		const line = document.lineAt(position.line)
		const trimmedLine = line.text.trimEnd()

		// 考虑空白字符，位置在实际内容末尾或之后
		return position.character >= trimmedLine.length
	}

	/**
	 * 检查位置是否在行中间
	 */
	isPositionInLineMiddle(document: vscode.TextDocument, position: vscode.Position): boolean {
		const line = document.lineAt(position.line)
		const trimmedLine = line.text.trimEnd()

		// 位置在行的中间部分，且不在行首
		return position.character > 0 && position.character < trimmedLine.length
	}

	/**
	 * 获取输入上下文
	 */
	getInputContext(document: vscode.TextDocument, position: vscode.Position): InputContext {
		const line = document.lineAt(position.line)
		const beforeCursor = line.text.substring(0, position.character)
		const afterCursor = line.text.substring(position.character)

		return {
			lineText: line.text,
			beforeCursor,
			afterCursor,
			isAtLineStart: position.character === 0,
			isAtLineEnd: this.isPositionAtLineEnd(document, position),
			isInMiddle: this.isPositionInLineMiddle(document, position),
			syntaxContext: this.analyzeSyntaxContext(document, position),
		}
	}

	/**
	 * 模型推荐防抖
	 */
	debounceModelRecommendation(documentUri: string, callback: () => void): void {
		const key = `model_${documentUri}`
		this.debounceManager.debounce(key, callback, this.settings.modelRecommendation.debounceMs)
	}

	/**
	 * AST+LSP分析防抖
	 */
	debounceASTLSPAnalysis(documentUri: string, callback: () => void): void {
		const key = `astlsp_${documentUri}`
		this.debounceManager.debounce(key, callback, this.settings.astLspAnalysis.debounceMs)
	}

	/**
	 * 取消待处理的分析
	 */
	cancelPendingAnalysis(documentUri: string, analysisType?: AnalysisType): void {
		if (analysisType) {
			const key = `${analysisType}_${documentUri}`
			this.debounceManager.cancel(key)
		} else {
			// 取消所有类型的分析
			this.debounceManager.cancel(`model_${documentUri}`)
			this.debounceManager.cancel(`astlsp_${documentUri}`)
		}
	}

	/**
	 * 设置文档空闲
	 */
	setDocumentIdle(documentUri: string): void {
		this.documentIdleTimes.set(documentUri, Date.now())
	}

	/**
	 * 设置文档活跃
	 */
	setDocumentActive(documentUri: string): void {
		this.documentIdleTimes.delete(documentUri)
	}

	/**
	 * 获取文档空闲时间
	 */
	getDocumentIdleTime(documentUri: string): number {
		const idleStartTime = this.documentIdleTimes.get(documentUri)
		return idleStartTime ? Date.now() - idleStartTime : 0
	}

	/**
	 * 检查文档是否空闲
	 */
	isDocumentIdle(documentUri: string, requiredIdleMs: number): boolean {
		return this.getDocumentIdleTime(documentUri) >= requiredIdleMs
	}

	/**
	 * 设置分析进行中
	 */
	setAnalysisInProgress(documentUri: string, analysisType: AnalysisType): void {
		if (!this.analysisInProgress.has(documentUri)) {
			this.analysisInProgress.set(documentUri, new Set())
		}
		this.analysisInProgress.get(documentUri)!.add(analysisType)
	}

	/**
	 * 设置分析完成
	 */
	setAnalysisCompleted(documentUri: string, analysisType: AnalysisType): void {
		const analyses = this.analysisInProgress.get(documentUri)
		if (analyses) {
			analyses.delete(analysisType)
			if (analyses.size === 0) {
				this.analysisInProgress.delete(documentUri)
			}
		}
	}

	/**
	 * 检查是否有分析在进行中
	 */
	isAnalysisInProgress(documentUri: string, analysisType?: AnalysisType): boolean {
		const analyses = this.analysisInProgress.get(documentUri)
		if (!analyses) return false

		if (analysisType) {
			return analyses.has(analysisType)
		} else {
			return analyses.size > 0
		}
	}

	/**
	 * 检查是否可以触发分析
	 */
	canTriggerAnalysis(documentUri: string, analysisType: AnalysisType): boolean {
		// 检查并发限制
		const totalAnalyses = Array.from(this.analysisInProgress.values()).reduce((total, analyses) => total + analyses.size, 0)

		if (totalAnalyses >= this.settings.triggerConfig.maxConcurrentAnalyses) {
			this.notifyAnalysisBlocked(documentUri, "Maximum concurrent analyses reached")
			return false
		}

		// 检查是否已经在进行相同类型的分析
		if (this.isAnalysisInProgress(documentUri, analysisType)) {
			this.notifyAnalysisBlocked(documentUri, `${analysisType} analysis already in progress`)
			return false
		}

		return true
	}

	/**
	 * 阻止分析一段时间
	 */
	blockAnalysis(documentUri: string, analysisType: AnalysisType, durationMs: number): void {
		// 简单实现：设置一个临时的分析状态
		this.setAnalysisInProgress(documentUri, analysisType)

		setTimeout(() => {
			this.setAnalysisCompleted(documentUri, analysisType)
		}, durationMs)
	}

	/**
	 * 注册触发条件满足回调
	 */
	onTriggerConditionMet(callback: (documentUri: string, analysisType: AnalysisType) => void): void {
		this.onTriggerCallbacks.push(callback)
	}

	/**
	 * 注册分析被阻止回调
	 */
	onAnalysisBlocked(callback: (documentUri: string, reason: string) => void): void {
		this.onAnalysisBlockedCallbacks.push(callback)
	}

	/**
	 * 清理文档相关数据
	 */
	cleanupDocument(documentUri: string): void {
		this.documentIdleTimes.delete(documentUri)
		this.analysisInProgress.delete(documentUri)
		this.debounceManager.cancel(`model_${documentUri}`)
		this.debounceManager.cancel(`astlsp_${documentUri}`)
	}

	// 私有方法

	private isDocumentEligibleForAnalysis(document: vscode.TextDocument): boolean {
		// 检查文件大小
		const maxSize = this.settings.astLspAnalysis.treeSitterConfig.maxFileSize
		if (document.getText().length > maxSize) {
			return false
		}

		// 检查语言支持
		const supportedLanguages = this.settings.astLspAnalysis.supportedLanguages
		if (!supportedLanguages.includes(document.languageId)) {
			return false
		}

		// 检查文件类型（排除某些特殊文件）
		const fileName = document.fileName.toLowerCase()
		if (
			fileName.includes("node_modules") ||
			fileName.includes(".git") ||
			fileName.endsWith(".min.js") ||
			fileName.endsWith(".bundle.js")
		) {
			return false
		}

		return true
	}

	private analyzeSyntaxContext(document: vscode.TextDocument, position: vscode.Position): any {
		// 简单的语法上下文分析，实际实现可能需要更复杂的逻辑
		const line = document.lineAt(position.line)
		const beforeCursor = line.text.substring(0, position.character)

		return {
			inString: this.isInString(beforeCursor),
			inComment: this.isInComment(beforeCursor),
			inFunction: this.isInFunction(document, position),
			indentLevel: this.getIndentLevel(line.text),
		}
	}

	private isInString(text: string): boolean {
		const quotes = ['"', "'", "`"]
		for (const quote of quotes) {
			const count = (text.match(new RegExp(quote, "g")) || []).length
			if (count % 2 === 1) {
				return true
			}
		}
		return false
	}

	private isInComment(text: string): boolean {
		return (
			text.trim().startsWith("//") ||
			text.trim().startsWith("/*") ||
			text.trim().startsWith("*") ||
			text.trim().startsWith("#")
		)
	}

	private isInFunction(document: vscode.TextDocument, position: vscode.Position): boolean {
		// 简单实现：向上查找函数定义
		for (let i = position.line; i >= 0; i--) {
			const line = document.lineAt(i).text
			if (line.includes("function ") || line.includes("def ") || line.includes("fn ") || line.match(/\w+\s*\(/)) {
				return true
			}
		}
		return false
	}

	private getIndentLevel(text: string): number {
		const match = text.match(/^(\s*)/)
		return match ? match[1].length : 0
	}

	private notifyTriggerConditionMet(documentUri: string, analysisType: AnalysisType): void {
		this.onTriggerCallbacks.forEach((callback) => {
			try {
				callback(documentUri, analysisType)
			} catch (error) {
				console.error("Error in trigger condition met callback:", error)
			}
		})
	}

	private notifyAnalysisBlocked(documentUri: string, reason: string): void {
		this.onAnalysisBlockedCallbacks.forEach((callback) => {
			try {
				callback(documentUri, reason)
			} catch (error) {
				console.error("Error in analysis blocked callback:", error)
			}
		})
	}

	/**
	 * 检查触发条件
	 */
	async checkTriggerConditions(document: vscode.TextDocument, position: vscode.Position, changeType: string): Promise<boolean> {
		try {
			// console.log(`🔍 Checking trigger conditions for ${document.uri.toString()} at ${position.line}:${position.character}`)

			// 检查基本条件
			if (!this.settings.enabled) {
				// console.log('❌ NextEdit is disabled')
				return false
			}

			// 检查文档类型
			if (!this.isLanguageSupported(document.languageId)) {
				// console.log(`❌ Language ${document.languageId} not supported`)
				return false
			}

			// 检查变更类型
			const triggerConfig = this.settings.triggerConfig
			switch (changeType) {
				case "text_change":
					return triggerConfig.lineMiddleTriggerASTLSP || triggerConfig.lineEndTriggerAutocomplete
				case "cursor_move":
					return triggerConfig.lineMiddleTriggerASTLSP
				case "document_save":
					return triggerConfig.documentIdleTriggerModel
				case "idle":
					return triggerConfig.documentIdleTriggerModel
				default:
					return false
			}
		} catch (error) {
			console.error("Error checking trigger conditions:", error)
			return false
		}
	}

	/**
	 * 检查语言是否支持
	 */
	private isLanguageSupported(languageId: string): boolean {
		const supportedLanguages = [
			"typescript",
			"javascript",
			"python",
			"rust",
			"go",
			"cpp",
			"c",
			"csharp",
			"java",
			"php",
			"swift",
			"kotlin",
		]
		return supportedLanguages.includes(languageId)
	}

	/**
	 * 释放资源
	 */
	dispose(): void {
		// 清理文档空闲时间记录
		this.documentIdleTimes.clear()

		// 清理正在进行的分析
		this.analysisInProgress.clear()

		// 清理回调函数
		this.onTriggerCallbacks = []
		this.onAnalysisBlockedCallbacks = []

		// 取消所有防抖
		this.debounceManager.cancelAll()
	}
}
