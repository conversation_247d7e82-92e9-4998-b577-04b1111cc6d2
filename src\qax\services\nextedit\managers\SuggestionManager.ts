/**
 * QAX Next Edit - 建议管理器
 * 负责维护每个文档的建议数组，处理建议的增删改查，以及建议的过滤和验证
 */

import * as vscode from "vscode"
import {
	Suggestion,
	DocumentSuggestionData,
	DocumentSuggestionStore,
	SuggestionState,
	SuggestionType,
	SuggestionSource,
	SuggestionCategory,
	DocumentMetadata,
	SuggestionStats,
} from "../types/Suggestion"
import { NextEditSettings } from "../types/NextEditSettings"

/**
 * 建议管理器
 */
export class SuggestionManager {
	private suggestions: DocumentSuggestionStore = {}
	private settings: NextEditSettings
	private onSuggestionsUpdatedCallbacks: ((documentUri: string, suggestions: Suggestion[]) => void)[] = []
	private onSuggestionRemovedCallbacks: ((suggestionId: string) => void)[] = []

	constructor(settings: NextEditSettings) {
		this.settings = settings
	}

	/**
	 * 更新配置
	 */
	updateSettings(settings: NextEditSettings): void {
		this.settings = settings
	}

	/**
	 * 初始化文档建议数组
	 */
	initializeDocument(documentUri: string, document: vscode.TextDocument): void {
		if (!this.suggestions[documentUri]) {
			this.suggestions[documentUri] = {
				suggestions: [],
				lastUpdate: Date.now(),
				documentVersion: document.version,
				analysisHistory: [],
				metadata: this.createDocumentMetadata(document),
			}
		}
	}

	/**
	 * 添加建议
	 */
	async addSuggestions(documentUri: string, suggestions: Suggestion[]): Promise<void> {
		this.ensureDocumentExists(documentUri)

		const data = this.suggestions[documentUri]
		const filteredSuggestions = await this.filterNewSuggestions(suggestions, data.suggestions)

		// 添加新建议
		data.suggestions.push(...filteredSuggestions)

		// 应用过滤规则
		data.suggestions = await this.applySuggestionFilters(data.suggestions)

		// 更新元数据
		data.lastUpdate = Date.now()
		data.metadata.suggestionStats.totalGenerated += filteredSuggestions.length

		// 触发更新事件
		this.notifyUpdated(documentUri, data.suggestions)
	}

	/**
	 * 获取建议
	 */
	getSuggestions(documentUri: string): Suggestion[] {
		const data = this.suggestions[documentUri]
		return data ? [...data.suggestions] : []
	}

	/**
	 * 根据ID获取建议
	 */
	getSuggestionById(suggestionId: string): Suggestion | null {
		for (const data of Object.values(this.suggestions)) {
			const suggestion = data.suggestions.find((s) => s.id === suggestionId)
			if (suggestion) {
				return suggestion
			}
		}
		return null
	}

	/**
	 * 移除建议
	 */
	async removeSuggestion(documentUri: string, suggestionId: string): Promise<boolean> {
		const data = this.suggestions[documentUri]
		if (!data) return false

		const index = data.suggestions.findIndex((s) => s.id === suggestionId)
		if (index === -1) return false

		data.suggestions.splice(index, 1)
		data.lastUpdate = Date.now()

		// 触发移除事件
		this.notifyRemoved(suggestionId)
		this.notifyUpdated(documentUri, data.suggestions)

		return true
	}

	/**
	 * 清除文档的所有建议
	 */
	async clearSuggestions(documentUri: string): Promise<void> {
		const data = this.suggestions[documentUri]
		if (!data) return

		data.suggestions = []
		data.lastUpdate = Date.now()

		this.notifyUpdated(documentUri, [])
	}

	/**
	 * 清除所有建议
	 */
	async clearAllSuggestions(): Promise<void> {
		for (const documentUri of Object.keys(this.suggestions)) {
			await this.clearSuggestions(documentUri)
		}
	}

	/**
	 * 文档变更时过滤失效建议（核心功能）
	 */
	async filterObsoleteSuggestions(
		documentUri: string,
		changes: vscode.TextDocumentContentChangeEvent[],
	): Promise<Suggestion[]> {
		const data = this.suggestions[documentUri]
		if (!data) return []

		const validSuggestions: Suggestion[] = []

		for (const suggestion of data.suggestions) {
			// 验证建议的anchor是否仍然有效
			const isValid = await this.validateSuggestionAnchor(suggestion, documentUri, changes)
			if (isValid) {
				validSuggestions.push(suggestion)
			}
		}

		// 更新建议数组
		data.suggestions = validSuggestions
		data.lastUpdate = Date.now()
		data.documentVersion++

		this.notifyUpdated(documentUri, validSuggestions)
		return validSuggestions
	}

	/**
	 * 验证单个建议
	 */
	async validateSuggestion(suggestion: Suggestion, document: vscode.TextDocument): Promise<boolean> {
		try {
			// 检查anchor是否仍然存在
			const anchorExists = await this.findAnchorInDocument(suggestion.location.anchor, document)
			if (!anchorExists) {
				return false
			}

			// 检查建议的置信度
			if (suggestion.confidence < this.settings.suggestionFilter.minConfidence) {
				return false
			}

			// 检查分类过滤器
			if (!this.settings.suggestionFilter.categoryFilters[suggestion.category]) {
				return false
			}

			// 检查影响级别过滤器
			if (!this.settings.suggestionFilter.impactLevelFilters[suggestion.impact]) {
				return false
			}

			return true
		} catch (error) {
			console.error("Error validating suggestion:", error)
			return false
		}
	}

	/**
	 * 验证所有建议
	 */
	async validateAllSuggestions(documentUri: string, document: vscode.TextDocument): Promise<void> {
		const data = this.suggestions[documentUri]
		if (!data) return

		const validSuggestions: Suggestion[] = []

		for (const suggestion of data.suggestions) {
			if (await this.validateSuggestion(suggestion, document)) {
				validSuggestions.push(suggestion)
			}
		}

		data.suggestions = validSuggestions
		data.lastUpdate = Date.now()

		this.notifyUpdated(documentUri, validSuggestions)
	}

	/**
	 * 获取建议数量
	 */
	getSuggestionCount(documentUri: string): number {
		const data = this.suggestions[documentUri]
		return data ? data.suggestions.length : 0
	}

	/**
	 * 根据类型获取建议
	 */
	getSuggestionsByType(documentUri: string, type: SuggestionType): Suggestion[] {
		const data = this.suggestions[documentUri]
		return data ? data.suggestions.filter((s) => s.type === type) : []
	}

	/**
	 * 根据来源获取建议
	 */
	getSuggestionsBySource(documentUri: string, source: SuggestionSource): Suggestion[] {
		const data = this.suggestions[documentUri]
		return data ? data.suggestions.filter((s) => s.source === source) : []
	}

	/**
	 * 根据分类获取建议
	 */
	getSuggestionsByCategory(documentUri: string, category: SuggestionCategory): Suggestion[] {
		const data = this.suggestions[documentUri]
		return data ? data.suggestions.filter((s) => s.category === category) : []
	}

	/**
	 * 检查是否有未执行的建议
	 */
	hasUnexecutedSuggestions(documentUri: string): boolean {
		const data = this.suggestions[documentUri]
		return data ? data.suggestions.length > 0 : false
	}

	/**
	 * 按优先级排序建议
	 */
	sortSuggestionsByPriority(suggestions: Suggestion[]): Suggestion[] {
		return suggestions.sort((a, b) => {
			// 优先级高的在前（数字大的优先级高）
			if (a.priority !== b.priority) {
				return b.priority - a.priority
			}
			// 置信度高的在前
			if (a.confidence !== b.confidence) {
				return b.confidence - a.confidence
			}
			// 时间新的在前
			return b.timestamp - a.timestamp
		})
	}

	/**
	 * 更新建议优先级
	 */
	async updateSuggestionPriority(suggestionId: string, priority: number): Promise<void> {
		const suggestion = this.getSuggestionById(suggestionId)
		if (suggestion) {
			suggestion.priority = priority

			// 找到对应的文档并触发更新
			for (const [documentUri, data] of Object.entries(this.suggestions)) {
				if (data.suggestions.some((s) => s.id === suggestionId)) {
					this.notifyUpdated(documentUri, data.suggestions)
					break
				}
			}
		}
	}

	/**
	 * 注册建议更新回调
	 */
	onSuggestionsUpdated(callback: (documentUri: string, suggestions: Suggestion[]) => void): void {
		this.onSuggestionsUpdatedCallbacks.push(callback)
	}

	/**
	 * 注册建议移除回调
	 */
	onSuggestionRemoved(callback: (suggestionId: string) => void): void {
		this.onSuggestionRemovedCallbacks.push(callback)
	}

	/**
	 * 获取文档元数据
	 */
	getDocumentMetadata(documentUri: string): DocumentMetadata | null {
		const data = this.suggestions[documentUri]
		return data ? data.metadata : null
	}

	/**
	 * 清理文档数据
	 */
	cleanupDocument(documentUri: string): void {
		delete this.suggestions[documentUri]
	}

	/**
	 * 释放资源
	 */
	dispose(): void {
		// 清理所有建议
		this.suggestions = {}

		// 清理事件监听器
		this.onSuggestionsUpdatedCallbacks = []
		this.onSuggestionRemovedCallbacks = []
	}

	// 私有方法

	private ensureDocumentExists(documentUri: string): void {
		if (!this.suggestions[documentUri]) {
			throw new Error(`Document ${documentUri} not initialized`)
		}
	}

	private async filterNewSuggestions(newSuggestions: Suggestion[], existingSuggestions: Suggestion[]): Promise<Suggestion[]> {
		if (!this.settings.suggestionFilter.enableDuplicateFilter) {
			return newSuggestions
		}

		return newSuggestions.filter((newSuggestion) => {
			return !existingSuggestions.some((existing) => this.areSuggestionsEqual(newSuggestion, existing))
		})
	}

	private areSuggestionsEqual(a: Suggestion, b: Suggestion): boolean {
		return (
			a.location.anchor === b.location.anchor &&
			a.location.position === b.location.position &&
			a.patch.new_content === b.patch.new_content
		)
	}

	private async applySuggestionFilters(suggestions: Suggestion[]): Promise<Suggestion[]> {
		let filtered = suggestions

		// 应用置信度过滤
		filtered = filtered.filter((s) => s.confidence >= this.settings.suggestionFilter.minConfidence)

		// 应用数量限制
		if (filtered.length > this.settings.suggestionFilter.maxSuggestionsPerDocument) {
			filtered = this.sortSuggestionsByPriority(filtered).slice(0, this.settings.suggestionFilter.maxSuggestionsPerDocument)
		}

		return filtered
	}

	private async validateSuggestionAnchor(
		suggestion: Suggestion,
		documentUri: string,
		changes: vscode.TextDocumentContentChangeEvent[],
	): Promise<boolean> {
		try {
			// 获取当前文档
			const document = vscode.workspace.textDocuments.find((doc) => doc.uri.toString() === documentUri)
			if (!document) return false

			// 检查anchor是否仍然存在
			return await this.findAnchorInDocument(suggestion.location.anchor, document)
		} catch (error) {
			console.error("Error validating suggestion anchor:", error)
			return false
		}
	}

	private async findAnchorInDocument(anchor: string, document: vscode.TextDocument): Promise<boolean> {
		const text = document.getText()
		const anchorParts = anchor.split("|").map((part) => part.trim())

		// 简单的锚点匹配逻辑，实际实现可能需要更复杂的算法
		for (const part of anchorParts) {
			if (part && text.includes(part)) {
				return true
			}
		}

		return false
	}

	private createDocumentMetadata(document: vscode.TextDocument): DocumentMetadata {
		return {
			language: document.languageId,
			fileSize: document.getText().length,
			lineCount: document.lineCount,
			lastAnalysisTime: Date.now(),
			analysisCount: 0,
			suggestionStats: {
				totalGenerated: 0,
				totalExecuted: 0,
				totalDismissed: 0,
				averageConfidence: 0,
				categoryDistribution: {} as Record<SuggestionCategory, number>,
			},
		}
	}

	private notifyUpdated(documentUri: string, suggestions: Suggestion[]): void {
		this.onSuggestionsUpdatedCallbacks.forEach((callback) => {
			try {
				callback(documentUri, suggestions)
			} catch (error) {
				console.error("Error in suggestions updated callback:", error)
			}
		})
	}

	private notifyRemoved(suggestionId: string): void {
		this.onSuggestionRemovedCallbacks.forEach((callback) => {
			try {
				callback(suggestionId)
			} catch (error) {
				console.error("Error in suggestion removed callback:", error)
			}
		})
	}
}
