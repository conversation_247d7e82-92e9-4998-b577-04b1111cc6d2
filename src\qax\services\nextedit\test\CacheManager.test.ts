/**
 * QAX Next Edit - CacheManager 单元测试
 * 测试LRU缓存、内存限制、缓存失效、性能统计等功能
 */

import * as vscode from "vscode"
import { CacheManager } from "../cache/CacheManager"
import { ASTNode } from "../types/AnalysisContext"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { expect } from "./test-utils"

import { mockVscode } from "./test-utils"

/**
 * 创建测试AST节点
 */
function createTestASTNode(type: string, childrenCount: number = 0): ASTNode {
	const children: ASTNode[] = []
	for (let i = 0; i < childrenCount; i++) {
		children.push({
			type: `child_${i}`,
			range: [i * 10, (i + 1) * 10],
			startPosition: { row: i, column: 0 },
			endPosition: { row: i, column: 10 },
			children: [],
			namedChildren: [],
			text: `child_${i}_text`,
		})
	}

	return {
		type,
		range: [0, childrenCount * 10],
		startPosition: { row: 0, column: 0 },
		endPosition: { row: childrenCount, column: 0 },
		children,
		namedChildren: children,
		text: `${type}_text`,
	}
}

/**
 * 创建测试位置
 */
function createTestLocation(uri: string, line: number, char: number): vscode.Location {
	return new mockVscode.Location(mockVscode.Uri.parse(uri), new mockVscode.Range(line, char, line, char + 5))
}

/**
 * 创建测试诊断
 */
function createTestDiagnostic(line: number, message: string): vscode.Diagnostic {
	return new mockVscode.Diagnostic(new mockVscode.Range(line, 0, line, 10), message)
}

describe("CacheManager", () => {
	let cacheManager: CacheManager
	const testDocumentUri = "file:///test/example.ts"

	beforeEach(() => {
		cacheManager = new CacheManager({
			...DEFAULT_NEXT_EDIT_SETTINGS,
			astLspAnalysis: {
				...DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis,
				maxCacheSize: 10, // 限制缓存大小便于测试
				cacheExpirationMs: 1000, // 1秒过期
			},
		})
	})

	afterEach(() => {
		cacheManager.dispose()
	})

	describe("AST缓存管理", () => {
		test("应该能够设置和获取AST缓存", () => {
			const ast = createTestASTNode("program", 3)

			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")
			const cached = cacheManager.getAST(testDocumentUri)

			expect(cached).toBeDefined()
			expect(cached!.ast.type).toBe("program")
			expect(cached!.documentVersion).toBe(1)
			expect(cached!.languageId).toBe("typescript")
		})

		test("应该能够检查AST缓存有效性", () => {
			const ast = createTestASTNode("program", 2)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			// 相同版本应该有效
			expect(cacheManager.isASTCacheValid(testDocumentUri, 1)).toBe(true)

			// 不同版本应该无效
			expect(cacheManager.isASTCacheValid(testDocumentUri, 2)).toBe(false)

			// 不存在的文档应该无效
			expect(cacheManager.isASTCacheValid("file:///nonexistent.ts", 1)).toBe(false)
		})

		test("应该能够处理AST缓存过期", async () => {
			const ast = createTestASTNode("program", 1)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			// 立即检查应该有效
			expect(cacheManager.isASTCacheValid(testDocumentUri, 1)).toBe(true)

			// 等待过期
			await new Promise((resolve) => setTimeout(resolve, 1100))

			// 过期后应该无效
			expect(cacheManager.isASTCacheValid(testDocumentUri, 1)).toBe(false)
		})

		test("应该能够更新AST缓存的访问时间", () => {
			const ast = createTestASTNode("program", 2)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			const firstAccess = cacheManager.getAST(testDocumentUri)
			const firstAccessTime = firstAccess!.lastAccessedAt

			// 稍等一下再访问
			setTimeout(() => {
				const secondAccess = cacheManager.getAST(testDocumentUri)
				const secondAccessTime = secondAccess!.lastAccessedAt

				expect(secondAccessTime).toBeGreaterThan(firstAccessTime)
			}, 10)
		})
	})

	describe("LSP缓存管理", () => {
		test("应该能够设置和获取LSP缓存", () => {
			const references = new Map([
				["symbol1", [createTestLocation(testDocumentUri, 1, 0)]],
				["symbol2", [createTestLocation(testDocumentUri, 2, 0)]],
			])
			const definitions = new Map([["symbol1", createTestLocation(testDocumentUri, 0, 0)]])
			const diagnostics = [createTestDiagnostic(1, "Error 1"), createTestDiagnostic(2, "Error 2")]

			cacheManager.setLSP(testDocumentUri, references, definitions, diagnostics, 1)
			const cached = cacheManager.getLSP(testDocumentUri)

			expect(cached).toBeDefined()
			expect(cached!.references.size).toBe(2)
			expect(cached!.definitions.size).toBe(1)
			expect(cached!.diagnostics).toHaveLength(2)
			expect(cached!.documentVersion).toBe(1)
		})

		test("应该能够检查LSP缓存有效性", () => {
			const references = new Map()
			const definitions = new Map()
			const diagnostics: vscode.Diagnostic[] = []

			cacheManager.setLSP(testDocumentUri, references, definitions, diagnostics, 1)

			// 相同版本应该有效
			expect(cacheManager.isLSPCacheValid(testDocumentUri, 1)).toBe(true)

			// 不同版本应该无效
			expect(cacheManager.isLSPCacheValid(testDocumentUri, 2)).toBe(false)
		})

		test("应该能够处理LSP缓存过期", () => {
			const references = new Map()
			const definitions = new Map()
			const diagnostics: vscode.Diagnostic[] = []

			cacheManager.setLSP(testDocumentUri, references, definitions, diagnostics, 1)

			// 立即检查应该有效
			expect(cacheManager.isLSPCacheValid(testDocumentUri, 1)).toBe(true)

			// 测试不同版本号应该无效（这是一种"过期"）
			expect(cacheManager.isLSPCacheValid(testDocumentUri, 2)).toBe(false)

			// 测试不存在的URI应该无效
			expect(cacheManager.isLSPCacheValid("file:///nonexistent.ts", 1)).toBe(false)
		})
	})

	describe("LRU淘汰策略", () => {
		test("应该在达到最大缓存大小时淘汰最久未使用的AST", () => {
			// 创建一个小缓存大小的设置用于测试
			const smallCacheSettings = {
				...DEFAULT_NEXT_EDIT_SETTINGS,
				astLspAnalysis: {
					...DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis,
					maxCacheSize: 1, // 1MB限制，很小
				},
			}
			const smallCacheManager = new CacheManager(smallCacheSettings)

			// 添加一些缓存项
			const ast1 = createTestASTNode("program_1", 10) // 较大的AST
			const ast2 = createTestASTNode("program_2", 10)
			const ast3 = createTestASTNode("program_3", 10)

			smallCacheManager.setAST("file:///test1.ts", ast1, 1, "typescript")
			smallCacheManager.setAST("file:///test2.ts", ast2, 1, "typescript")

			// 访问第一个，使其成为最近使用的
			smallCacheManager.getAST("file:///test1.ts")

			// 添加第三个，可能会触发清理
			smallCacheManager.setAST("file:///test3.ts", ast3, 1, "typescript")

			// 检查缓存状态
			const stats = smallCacheManager.getCacheStats()
			expect(stats.ast.size).toBeGreaterThan(0) // 至少有一些缓存

			smallCacheManager.dispose()
		})

		test("应该在达到最大缓存大小时淘汰最久未使用的LSP", () => {
			// 填满LSP缓存
			for (let i = 0; i < 10; i++) {
				const references = new Map([["symbol", [createTestLocation(`file:///test${i}.ts`, 1, 0)]]])
				const definitions = new Map()
				const diagnostics: vscode.Diagnostic[] = []
				cacheManager.setLSP(`file:///test${i}.ts`, references, definitions, diagnostics, 1)
			}

			// 访问第一个缓存项
			cacheManager.getLSP("file:///test0.ts")

			// 添加新的缓存项
			const newReferences = new Map([["newSymbol", [createTestLocation("file:///new.ts", 1, 0)]]])
			cacheManager.setLSP("file:///new.ts", newReferences, new Map(), [], 1)

			// test0.ts应该仍然存在
			expect(cacheManager.getLSP("file:///test0.ts")).toBeDefined()
		})
	})

	describe("内存限制控制", () => {
		test("应该能够估算缓存内存使用", () => {
			const ast = createTestASTNode("program", 5) // 较大的AST
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			const stats = cacheManager.getCacheStats()
			expect(stats.totalMemoryMB).toBeGreaterThan(0)
		})

		test("应该在内存使用过高时进行清理", () => {
			// 创建大量缓存项
			for (let i = 0; i < 20; i++) {
				const ast = createTestASTNode(`program_${i}`, 10) // 大的AST
				cacheManager.setAST(`file:///test${i}.ts`, ast, 1, "typescript")
			}

			const stats = cacheManager.getCacheStats()

			// 由于内存限制，应该有少于或等于20个缓存项
			expect(stats.ast.size).toBeLessThanOrEqual(20)
		})
	})

	describe("缓存清理", () => {
		test("应该能够删除特定文档的缓存", () => {
			const ast = createTestASTNode("program", 2)
			const references = new Map([["symbol", [createTestLocation(testDocumentUri, 1, 0)]]])

			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")
			cacheManager.setLSP(testDocumentUri, references, new Map(), [], 1)

			// 删除缓存
			cacheManager.deleteDocument(testDocumentUri)

			// 应该都被删除了
			expect(cacheManager.getAST(testDocumentUri)).toBeUndefined()
			expect(cacheManager.getLSP(testDocumentUri)).toBeUndefined()
		})

		test("应该能够清空所有缓存", () => {
			// 添加多个缓存项
			for (let i = 0; i < 5; i++) {
				const ast = createTestASTNode(`program_${i}`, 1)
				cacheManager.setAST(`file:///test${i}.ts`, ast, 1, "typescript")
			}

			cacheManager.clearAll()

			const stats = cacheManager.getCacheStats()
			expect(stats.ast.size).toBe(0)
			expect(stats.lsp.size).toBe(0)
		})

		test("应该能够执行定期清理", async () => {
			// 添加一些会过期的缓存项
			const ast = createTestASTNode("program", 1)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			// 等待过期
			await new Promise((resolve) => setTimeout(resolve, 1100))

			// 手动触发清理
			cacheManager.performCleanup()

			// 过期的缓存应该被清理
			expect(cacheManager.getAST(testDocumentUri)).toBeUndefined()
		})
	})

	describe("性能统计", () => {
		test("应该能够获取缓存统计信息", () => {
			const ast = createTestASTNode("program", 3)
			const references = new Map([["symbol", [createTestLocation(testDocumentUri, 1, 0)]]])

			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")
			cacheManager.setLSP(testDocumentUri, references, new Map(), [], 1)

			const stats = cacheManager.getCacheStats()

			expect(stats.ast.size).toBe(1)
			expect(stats.lsp.size).toBe(1)
			expect(stats.totalMemoryMB).toBeGreaterThan(0)
			expect(typeof stats.ast.hitRate).toBe("number")
			expect(typeof stats.lsp.hitRate).toBe("number")
		})

		test("应该能够计算缓存命中率", () => {
			const ast = createTestASTNode("program", 2)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			// 多次访问以提高命中率
			cacheManager.getAST(testDocumentUri)
			cacheManager.getAST(testDocumentUri)
			cacheManager.getAST(testDocumentUri)

			const stats = cacheManager.getCacheStats()
			expect(stats.ast.hitRate).toBeGreaterThan(0)
		})

		test("应该能够跟踪总访问次数", () => {
			const ast = createTestASTNode("program", 1)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			// 多次访问
			for (let i = 0; i < 5; i++) {
				cacheManager.getAST(testDocumentUri)
			}

			const stats = cacheManager.getCacheStats()
			expect(stats.ast.totalAccesses).toBeGreaterThan(1)
		})
	})

	describe("并发操作", () => {
		test("应该能够处理并发的缓存设置", async () => {
			const promises = []

			for (let i = 0; i < 10; i++) {
				const ast = createTestASTNode(`program_${i}`, 1)
				promises.push(Promise.resolve().then(() => cacheManager.setAST(`file:///test${i}.ts`, ast, 1, "typescript")))
			}

			await Promise.all(promises)

			const stats = cacheManager.getCacheStats()
			expect(stats.ast.size).toBeGreaterThan(0)
		})

		test("应该能够处理并发的缓存访问", async () => {
			const ast = createTestASTNode("program", 2)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			const promises = []
			for (let i = 0; i < 10; i++) {
				promises.push(Promise.resolve().then(() => cacheManager.getAST(testDocumentUri)))
			}

			const results = await Promise.all(promises)

			// 所有访问都应该成功
			expect(results.every((result) => result !== undefined)).toBe(true)
		})
	})

	describe("错误处理", () => {
		test("应该处理无效的文档URI", () => {
			const ast = createTestASTNode("program", 1)

			expect(() => {
				cacheManager.setAST("", ast, 1, "typescript")
			}).not.toThrow()

			expect(cacheManager.getAST("")).toBeDefined()
		})

		test("应该处理null/undefined输入", () => {
			// 测试空字符串URI
			expect(() => {
				cacheManager.setAST("", createTestASTNode("test"), 1, "typescript")
			}).not.toThrow()

			// 测试空数组的LSP数据
			expect(() => {
				cacheManager.setLSP(testDocumentUri, [], [], [], 1)
			}).not.toThrow()

			// 测试获取不存在的缓存
			expect(cacheManager.getAST("file:///nonexistent.ts")).toBeUndefined()
			expect(cacheManager.getLSP("file:///nonexistent.ts")).toBeUndefined()
		})

		test("应该处理负数版本号", () => {
			const ast = createTestASTNode("program", 1)
			cacheManager.setAST(testDocumentUri, ast, -1, "typescript")

			expect(cacheManager.isASTCacheValid(testDocumentUri, -1)).toBe(true)
		})
	})

	describe("性能测试", () => {
		test("缓存设置应该在1ms内完成", () => {
			const ast = createTestASTNode("program", 5)

			const startTime = performance.now()
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")
			const duration = performance.now() - startTime

			expect(duration).toBeLessThan(1)
		})

		test("缓存获取应该在1ms内完成", () => {
			const ast = createTestASTNode("program", 5)
			cacheManager.setAST(testDocumentUri, ast, 1, "typescript")

			const startTime = performance.now()
			cacheManager.getAST(testDocumentUri)
			const duration = performance.now() - startTime

			expect(duration).toBeLessThan(1)
		})

		test("大量缓存操作应该保持高性能", () => {
			const startTime = performance.now()

			// 执行大量操作
			for (let i = 0; i < 100; i++) {
				const ast = createTestASTNode(`program_${i}`, 2)
				cacheManager.setAST(`file:///test${i}.ts`, ast, 1, "typescript")
				cacheManager.getAST(`file:///test${i}.ts`)
			}

			const duration = performance.now() - startTime
			expect(duration).toBeLessThan(100) // 100次操作应该在100ms内完成
		})
	})
})
