/**
 * QAX Next Edit - SuggestionManager 单元测试
 * 测试建议数组维护、过滤逻辑、验证机制、并发操作等核心功能
 */

import * as vscode from "vscode"
import { SuggestionManager } from "../managers/SuggestionManager"
import { Suggestion, SuggestionBuilder, SuggestionSource, SuggestionCategory, ImpactLevel } from "../types/Suggestion"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { expect, createMockDocument, createMockAnalysisContext, createMockPosition } from "./test-utils"

// 模拟VSCode API
const mockVscode = {
	Range: class Range {
		constructor(
			public startLine: number,
			public startChar: number,
			public endLine: number,
			public endChar: number,
		) {
			this.start = { line: startLine, character: startChar }
			this.end = { line: endLine, character: endChar }
		}
		start: { line: number; character: number }
		end: { line: number; character: number }
	},
}

/**
 * 创建测试建议
 */
function createTestSuggestion(id: string, anchor: string, type: "add" | "modify" | "delete" = "add"): Suggestion {
	return SuggestionBuilder.create(type, `Test suggestion ${id}`, anchor, "after", `test content ${id}`, {
		confidence: 0.8,
		source: SuggestionSource.MODEL,
		category: SuggestionCategory.LOGIC_COMPLETION,
		impact: ImpactLevel.MEDIUM,
		tags: ["test"],
	})
}

/**
 * 创建模拟文档变更
 */
function createMockChange(
	startLine: number,
	startChar: number,
	endLine: number,
	endChar: number,
	text: string,
): vscode.TextDocumentContentChangeEvent {
	return {
		range: {
			start: {
				line: startLine,
				character: startChar,
				isBefore: () => false,
				isBeforeOrEqual: () => false,
				isAfter: () => false,
				isAfterOrEqual: () => false,
				isEqual: () => false,
				compareTo: () => 0,
				translate: () => ({}) as any,
				with: () => ({}) as any,
			} as vscode.Position,
			end: {
				line: endLine,
				character: endChar,
				isBefore: () => false,
				isBeforeOrEqual: () => false,
				isAfter: () => false,
				isAfterOrEqual: () => false,
				isEqual: () => false,
				compareTo: () => 0,
				translate: () => ({}) as any,
				with: () => ({}) as any,
			} as vscode.Position,
			isEmpty: false,
			isSingleLine: startLine === endLine,
			contains: () => false,
			isEqual: () => false,
			intersection: () => undefined,
			union: () => ({}) as any,
			with: () => ({}) as any,
		} as vscode.Range,
		rangeOffset: startLine * 100 + startChar,
		rangeLength: (endLine - startLine) * 100 + (endChar - startChar),
		text,
	}
}

describe("SuggestionManager", () => {
	let suggestionManager: SuggestionManager
	const testDocumentUri = "file:///test/example.ts"

	beforeEach(() => {
		suggestionManager = new SuggestionManager(DEFAULT_NEXT_EDIT_SETTINGS)
	})

	afterEach(() => {
		suggestionManager.dispose()
	})

	describe("建议数组维护", () => {
		test("应该能够添加建议到文档", async () => {
			const suggestions = [createTestSuggestion("1", "function test() {"), createTestSuggestion("2", "const x = 5;")]

			await suggestionManager.addSuggestions(testDocumentUri, suggestions)
			const result = await suggestionManager.getSuggestions(testDocumentUri)

			expect(result).toHaveLength(2)
			expect(result[0].description).toBe("Test suggestion 1")
			expect(result[1].description).toBe("Test suggestion 2")
		})

		test("应该能够获取空文档的建议", async () => {
			const result = await suggestionManager.getSuggestions("file:///nonexistent.ts")
			expect(result).toHaveLength(0)
		})

		test("应该能够移除单个建议", async () => {
			const suggestion = createTestSuggestion("1", "function test() {")
			await suggestionManager.addSuggestions(testDocumentUri, [suggestion])

			await suggestionManager.removeSuggestion(testDocumentUri, suggestion.id)
			const result = await suggestionManager.getSuggestions(testDocumentUri)

			expect(result).toHaveLength(0)
		})

		test("应该能够清空文档的所有建议", async () => {
			const suggestions = [createTestSuggestion("1", "function test() {"), createTestSuggestion("2", "const x = 5;")]

			await suggestionManager.addSuggestions(testDocumentUri, suggestions)
			await suggestionManager.clearSuggestions(testDocumentUri)
			const result = await suggestionManager.getSuggestions(testDocumentUri)

			expect(result).toHaveLength(0)
		})

		test("应该能够检查是否有未执行的建议", async () => {
			const suggestion = createTestSuggestion("1", "function test() {")
			await suggestionManager.addSuggestions(testDocumentUri, [suggestion])

			const hasUnexecuted = await suggestionManager.hasUnexecutedSuggestions(testDocumentUri)
			expect(hasUnexecuted).toBe(true)

			// 清理建议来模拟执行
			await suggestionManager.clearSuggestions(testDocumentUri)

			const hasUnexecutedAfter = await suggestionManager.hasUnexecutedSuggestions(testDocumentUri)
			expect(hasUnexecutedAfter).toBe(false)
		})
	})

	describe("建议过滤逻辑", () => {
		test("应该能够过滤失效的建议", async () => {
			const suggestions = [
				createTestSuggestion("1", "function test() {"), // 这个会保留
				createTestSuggestion("2", "function removed() {"), // 这个会被过滤
				createTestSuggestion("3", "const x = 5;"), // 这个会保留
			]

			await suggestionManager.addSuggestions(testDocumentUri, suggestions)

			// 模拟文档变更，删除了 'function removed() {' 这一行
			const changes = [
				createMockChange(1, 0, 2, 0, ""), // 删除第1-2行
			]

			const validSuggestions = await suggestionManager.filterObsoleteSuggestions(testDocumentUri, changes)

			// 应该过滤掉包含 'function removed() {' 的建议
			expect(validSuggestions).toHaveLength(2)
			expect(validSuggestions.find((s) => s.location.anchor.includes("removed"))).toBeUndefined()
		})

		test("应该能够处理空的变更列表", async () => {
			const suggestions = [createTestSuggestion("1", "function test() {}")]
			await suggestionManager.addSuggestions(testDocumentUri, suggestions)

			const validSuggestions = await suggestionManager.filterObsoleteSuggestions(testDocumentUri, [])
			expect(validSuggestions).toHaveLength(1)
		})

		test("应该能够处理不存在的文档", async () => {
			const changes = [createMockChange(0, 0, 1, 0, "new content")]
			const validSuggestions = await suggestionManager.filterObsoleteSuggestions("file:///nonexistent.ts", changes)
			expect(validSuggestions).toHaveLength(0)
		})
	})

	describe("建议验证机制", () => {
		test("应该能够验证有效的建议", async () => {
			const validSuggestion = createTestSuggestion("1", "function test() {")
			const mockDocument = createMockDocument(testDocumentUri, "test content")
			const isValid = await suggestionManager.validateSuggestion(validSuggestion, mockDocument)
			expect(isValid).toBe(true)
		})

		test("应该能够拒绝无效的建议", async () => {
			const invalidSuggestion = {
				...createTestSuggestion("1", "function test() {"),
				location: {
					anchor: "", // 空锚点
					position: "after" as const,
				},
			}

			const mockDocument = createMockDocument(testDocumentUri, "test content")
			const isValid = await suggestionManager.validateSuggestion(invalidSuggestion, mockDocument)
			expect(isValid).toBe(false)
		})

		test("应该能够批量验证建议", async () => {
			const suggestions = [
				createTestSuggestion("1", "function test() {"), // 有效
				{
					...createTestSuggestion("2", "function test2() {"),
					confidence: 0.3, // 置信度太低
				},
				createTestSuggestion("3", "const x = 5;"), // 有效
			]

			const mockDocument = createMockDocument(testDocumentUri, "test content")
			await suggestionManager.validateAllSuggestions(testDocumentUri, mockDocument)
			// 验证功能正常运行
			expect(true).toBe(true)
		})
	})

	describe("建议去重和排序", () => {
		test("应该能够去除重复的建议", async () => {
			const suggestion1 = createTestSuggestion("1", "function test() {")
			const suggestion2 = createTestSuggestion("2", "function test() {") // 相同锚点
			const suggestion3 = createTestSuggestion("3", "const x = 5;")

			await suggestionManager.addSuggestions(testDocumentUri, [suggestion1, suggestion2, suggestion3])
			const result = await suggestionManager.getSuggestions(testDocumentUri)

			// 应该去重，只保留2个不同锚点的建议
			expect(result).toHaveLength(2)
		})

		test("应该能够按优先级排序建议", async () => {
			const lowPriority = { ...createTestSuggestion("1", "function test1() {"), priority: 3 }
			const highPriority = { ...createTestSuggestion("2", "function test2() {"), priority: 8 }
			const mediumPriority = { ...createTestSuggestion("3", "function test3() {"), priority: 5 }

			await suggestionManager.addSuggestions(testDocumentUri, [lowPriority, highPriority, mediumPriority])
			const result = await suggestionManager.getSuggestions(testDocumentUri)

			// 应该按优先级降序排列
			expect(result[0].priority).toBe(8)
			expect(result[1].priority).toBe(5)
			expect(result[2].priority).toBe(3)
		})
	})

	describe("统计和查询功能", () => {
		test("应该能够获取建议数量", async () => {
			const suggestions = [
				createTestSuggestion("1", "function test1() {"),
				createTestSuggestion("2", "function test2() {}"),
			]

			await suggestionManager.addSuggestions(testDocumentUri, suggestions)
			const count = await suggestionManager.getSuggestionCount(testDocumentUri)

			expect(count).toBe(2)
		})

		test("应该能够按类型查询建议", async () => {
			const addSuggestion = createTestSuggestion("1", "function test() {", "add")
			const modifySuggestion = createTestSuggestion("2", "var x = 5;", "modify")

			await suggestionManager.addSuggestions(testDocumentUri, [addSuggestion, modifySuggestion])

			const addSuggestions = await suggestionManager.getSuggestionsByType(testDocumentUri, "add")
			const modifySuggestions = await suggestionManager.getSuggestionsByType(testDocumentUri, "modify")

			expect(addSuggestions).toHaveLength(1)
			expect(modifySuggestions).toHaveLength(1)
			expect(addSuggestions[0].type).toBe("add")
			expect(modifySuggestions[0].type).toBe("modify")
		})

		test("应该能够按来源查询建议", async () => {
			const modelSuggestion = {
				...createTestSuggestion("1", "function test() {"),
				source: SuggestionSource.MODEL,
			}
			const astLspSuggestion = {
				...createTestSuggestion("2", "const x = 5;"),
				source: SuggestionSource.AST_LSP,
			}

			await suggestionManager.addSuggestions(testDocumentUri, [modelSuggestion, astLspSuggestion])

			const modelSuggestions = await suggestionManager.getSuggestionsBySource(testDocumentUri, SuggestionSource.MODEL)
			const astLspSuggestions = await suggestionManager.getSuggestionsBySource(testDocumentUri, SuggestionSource.AST_LSP)

			expect(modelSuggestions).toHaveLength(1)
			expect(astLspSuggestions).toHaveLength(1)
		})
	})

	describe("并发操作", () => {
		test("应该能够处理并发的建议添加", async () => {
			const promises = []

			for (let i = 0; i < 10; i++) {
				const suggestion = createTestSuggestion(i.toString(), `function test${i}() {`)
				promises.push(suggestionManager.addSuggestions(testDocumentUri, [suggestion]))
			}

			await Promise.all(promises)
			const result = await suggestionManager.getSuggestions(testDocumentUri)

			expect(result).toHaveLength(10)
		})

		test("应该能够处理并发的建议过滤", async () => {
			const suggestions = Array.from({ length: 5 }, (_, i) => createTestSuggestion(i.toString(), `function test${i}() {`))

			await suggestionManager.addSuggestions(testDocumentUri, suggestions)

			const changes = [createMockChange(0, 0, 1, 0, "new content")]
			const promises = Array.from({ length: 3 }, () =>
				suggestionManager.filterObsoleteSuggestions(testDocumentUri, changes),
			)

			const results = await Promise.all(promises)

			// 所有结果应该一致
			expect(results[0]).toHaveLength(results[1].length)
			expect(results[1]).toHaveLength(results[2].length)
		})
	})

	describe("事件处理", () => {
		test("应该能够触发建议更新事件", async () => {
			let eventTriggered = false

			suggestionManager.onSuggestionsUpdated((documentUri, suggestions) => {
				expect(documentUri).toBe(testDocumentUri)
				expect(suggestions).toHaveLength(1)
				eventTriggered = true
			})

			const suggestion = createTestSuggestion("1", "function test() {")
			await suggestionManager.addSuggestions(testDocumentUri, [suggestion])

			// 等待事件触发
			await new Promise((resolve) => setTimeout(resolve, 100))
			expect(eventTriggered).toBe(true)
		})

		test("应该能够处理多个事件监听器", async () => {
			let callCount = 0

			suggestionManager.onSuggestionsUpdated(() => callCount++)
			suggestionManager.onSuggestionsUpdated(() => callCount++)

			const suggestion = createTestSuggestion("1", "function test() {")
			await suggestionManager.addSuggestions(testDocumentUri, [suggestion])

			// 等待事件处理
			await new Promise((resolve) => setTimeout(resolve, 10))
			expect(callCount).toBe(2)
		})
	})

	describe("错误处理", () => {
		test("应该能够处理无效的文档URI", async () => {
			const invalidUri = ""
			const suggestion = createTestSuggestion("1", "function test() {")

			await expect(suggestionManager.addSuggestions(invalidUri, [suggestion])).rejects.toThrow()
		})

		test("应该能够处理空的建议数组", async () => {
			await suggestionManager.addSuggestions(testDocumentUri, [])
			const result = await suggestionManager.getSuggestions(testDocumentUri)
			expect(result).toHaveLength(0)
		})

		test("应该能够处理null/undefined输入", async () => {
			await expect(suggestionManager.addSuggestions(testDocumentUri, null as any)).rejects.toThrow()
			await expect(suggestionManager.filterObsoleteSuggestions(testDocumentUri, null as any)).rejects.toThrow()
		})
	})
})
