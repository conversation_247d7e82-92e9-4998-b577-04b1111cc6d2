/**
 * QAX Next Edit - TreeSitter 集成测试
 * 测试与现有 TreeSitter 服务的集成
 */

import * as vscode from "vscode"
import { ASTLSPAnalyzer } from "../analyzers/ASTLSPAnalyzer"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { ChangeType } from "../types/AnalysisContext"
import { expect, createMockDocument, createMockAnalysisContext, createMockPosition } from "./test-utils"

describe("TreeSitter Integration", () => {
	let analyzer: ASTLSPAnalyzer
	const testDocumentUri = "file:///test/example.ts"

	beforeEach(() => {
		analyzer = new ASTLSPAnalyzer(DEFAULT_NEXT_EDIT_SETTINGS)
	})

	afterEach(() => {
		analyzer.dispose()
	})

	describe("AST 解析集成", () => {
		test("应该能够解析 TypeScript 文件", async () => {
			const content = `
function greet(name: string): string {
    return "Hello, " + name;
}

const message = greet("World");
console.log(message);
`
			const document = createMockDocument(testDocumentUri, content)

			// 模拟 workspace.textDocuments - 不需要实际设置，只是测试文档对象
			expect(document).toBeDefined()

			// 测试快速分析
			const context = {
				document,
				diff: {
					additions: [],
					deletions: [],
					modifications: [],
					isComplete: true,
					diffType: "unknown" as any,
					affectedSymbols: [],
					contextLines: 3,
					metadata: {
						confidence: 0.9,
						complexity: 0,
						riskLevel: "low" as any,
						estimatedImpact: [],
					},
					oldContent: "",
					newContent: content,
				},
				position: createMockPosition(1, 10),
				symbolInfo: [],
				lspDiagnostics: [],
				recentChanges: [],
				relatedFiles: [],
				workspaceRoot: "/test",
				timestamp: Date.now(),
			}

			const suggestions = await analyzer.performFastAnalysis(context)

			// 验证分析结果
			expect(Array.isArray(suggestions)).toBe(true)
			console.log(`TreeSitter 分析生成了 ${suggestions.length} 个建议`)
		})

		test("应该能够检测符号重命名", async () => {
			const oldContent = `
function oldFunction() {
    return "old";
}
`
			const newContent = `
function newFunction() {
    return "old";
}
`
			const document = createMockDocument(testDocumentUri, newContent)
			expect(document).toBeDefined()

			const context = {
				document,
				diff: {
					additions: [],
					deletions: [],
					modifications: [],
					isComplete: true,
					diffType: "unknown" as any,
					affectedSymbols: [],
					contextLines: 3,
					metadata: {
						confidence: 0.9,
						complexity: 0,
						riskLevel: "low" as any,
						estimatedImpact: [],
					},
					oldContent,
					newContent,
				},
				position: createMockPosition(1, 10),
				symbolInfo: [],
				lspDiagnostics: [],
				recentChanges: [],
				relatedFiles: [],
				workspaceRoot: "/test",
				timestamp: Date.now(),
			}

			const suggestions = await analyzer.performFastAnalysis(context)

			// 验证能够检测到变更
			expect(Array.isArray(suggestions)).toBe(true)
			console.log("符号重命名检测完成")
		})

		test("应该能够处理 JavaScript 文件", async () => {
			const content = `
function calculate(a, b) {
    return a + b;
}

const result = calculate(5, 3);
`
			const document = createMockDocument("file:///test/example.js", content, "javascript")
			expect(document).toBeDefined()

			const context = {
				document,
				diff: {
					additions: [],
					deletions: [],
					modifications: [],
					isComplete: true,
					diffType: "unknown" as any,
					affectedSymbols: [],
					contextLines: 3,
					metadata: {
						confidence: 0.9,
						complexity: 0,
						riskLevel: "low" as any,
						estimatedImpact: [],
					},
					oldContent: "",
					newContent: content,
				},
				position: createMockPosition(1, 5),
				symbolInfo: [],
				lspDiagnostics: [],
				recentChanges: [],
				relatedFiles: [],
				workspaceRoot: "/test",
				timestamp: Date.now(),
			}

			const suggestions = await analyzer.performFastAnalysis(context)

			expect(Array.isArray(suggestions)).toBe(true)
			console.log("JavaScript 文件分析完成")
		})
	})

	describe("缓存集成", () => {
		test("应该能够缓存 AST 解析结果", async () => {
			const content = `
class TestClass {
    constructor(name) {
        this.name = name;
    }
    
    getName() {
        return this.name;
    }
}
`
			const document = createMockDocument(testDocumentUri, content)
			expect(document).toBeDefined()

			// 第一次解析
			const startTime1 = performance.now()
			await analyzer.updateASTIncremental(testDocumentUri, [])
			const duration1 = performance.now() - startTime1

			// 第二次解析（应该使用缓存）
			const startTime2 = performance.now()
			await analyzer.updateASTIncremental(testDocumentUri, [])
			const duration2 = performance.now() - startTime2

			console.log(`第一次解析: ${duration1.toFixed(2)}ms`)
			console.log(`第二次解析: ${duration2.toFixed(2)}ms`)

			// 验证缓存生效（第二次应该更快）
			expect(duration2).toBeLessThan(duration1 + 10) // 允许一些误差
		})

		test("应该能够获取缓存统计", () => {
			const stats = analyzer.getCacheStats()

			expect(typeof stats.ast).toBe("number")
			expect(typeof stats.lsp).toBe("number")
			expect(typeof stats.totalMemory).toBe("number")

			console.log("缓存统计:", stats)
		})
	})

	describe("错误处理", () => {
		test("应该能够处理无效的文件内容", async () => {
			const invalidContent = `
function incomplete(
    // 缺少闭合括号和函数体
`
			const document = createMockDocument(testDocumentUri, invalidContent)
			expect(document).toBeDefined()

			const context = {
				document,
				diff: {
					additions: [],
					deletions: [],
					modifications: [],
					isComplete: true,
					diffType: "unknown" as any,
					affectedSymbols: [],
					contextLines: 3,
					metadata: {
						confidence: 0.9,
						complexity: 0,
						riskLevel: "low" as any,
						estimatedImpact: [],
					},
					oldContent: "",
					newContent: invalidContent,
				},
				position: createMockPosition(1, 5),
				symbolInfo: [],
				lspDiagnostics: [],
				recentChanges: [],
				relatedFiles: [],
				workspaceRoot: "/test",
				timestamp: Date.now(),
			}

			// 应该不会抛出错误
			const suggestions = await analyzer.performFastAnalysis(context)
			expect(Array.isArray(suggestions)).toBe(true)

			console.log("无效内容处理完成")
		})

		test("应该能够处理空文件", async () => {
			const document = createMockDocument(testDocumentUri, "")
			expect(document).toBeDefined()

			const context = {
				document,
				diff: {
					additions: [],
					deletions: [],
					modifications: [],
					isComplete: true,
					diffType: "unknown" as any,
					affectedSymbols: [],
					contextLines: 3,
					metadata: {
						confidence: 0.9,
						complexity: 0,
						riskLevel: "low" as any,
						estimatedImpact: [],
					},
					oldContent: "",
					newContent: "",
				},
				position: createMockPosition(0, 0),
				symbolInfo: [],
				lspDiagnostics: [],
				recentChanges: [],
				relatedFiles: [],
				workspaceRoot: "/test",
				timestamp: Date.now(),
			}

			const suggestions = await analyzer.performFastAnalysis(context)
			expect(Array.isArray(suggestions)).toBe(true)

			console.log("空文件处理完成")
		})
	})

	describe("性能测试", () => {
		test("快速分析应该在100ms内完成", async () => {
			const content = `
import { Component } from 'react';

class MyComponent extends Component {
    constructor(props) {
        super(props);
        this.state = { count: 0 };
    }
    
    increment = () => {
        this.setState({ count: this.state.count + 1 });
    }
    
    render() {
        return (
            <div>
                <p>Count: {this.state.count}</p>
                <button onClick={this.increment}>Increment</button>
            </div>
        );
    }
}
`
			const document = createMockDocument(testDocumentUri, content)
			expect(document).toBeDefined()

			const context = {
				document,
				diff: {
					additions: [],
					deletions: [],
					modifications: [],
					isComplete: true,
					diffType: "unknown" as any,
					affectedSymbols: [],
					contextLines: 3,
					metadata: {
						confidence: 0.9,
						complexity: 0,
						riskLevel: "low" as any,
						estimatedImpact: [],
					},
					oldContent: "",
					newContent: content,
				},
				position: createMockPosition(5, 10),
				symbolInfo: [],
				lspDiagnostics: [],
				recentChanges: [],
				relatedFiles: [],
				workspaceRoot: "/test",
				timestamp: Date.now(),
			}

			const startTime = performance.now()
			const suggestions = await analyzer.performFastAnalysis(context)
			const duration = performance.now() - startTime

			console.log(`快速分析耗时: ${duration.toFixed(2)}ms`)
			expect(duration).toBeLessThan(100)
			expect(Array.isArray(suggestions)).toBe(true)
		})
	})
})
