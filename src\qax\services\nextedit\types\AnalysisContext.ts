/**
 * QAX Next Edit - 分析上下文数据结构
 */

import * as vscode from "vscode"
import { SuggestionCategory, ImpactLevel } from "./Suggestion"

/**
 * 分析类型
 */
export enum AnalysisType {
	MODEL = "model",
	AST_LSP = "ast-lsp",
	HYBRID = "hybrid",
}

/**
 * 变更类型
 */
export enum ChangeType {
	SYMBOL_RENAME = "symbol_rename",
	FUNCTION_SIGNATURE_CHANGE = "function_signature_change",
	VARIABLE_DECLARATION = "variable_declaration",
	IMPORT_CHANGE = "import_change",
	CLASS_DEFINITION = "class_definition",
	METHOD_IMPLEMENTATION = "method_implementation",
	BLOCK_RESTRUCTURE = "block_restructure",
	FORMATTING_CHANGE = "formatting",
	UNKNOWN = "unknown",
}

/**
 * 差异类型
 */
export enum DiffType {
	SYMBOL_RENAME = "symbol_rename",
	FUNCTION_SIGNATURE = "function_signature",
	VARIABLE_DECL = "variable_decl",
	IMPORT_STATEMENT = "import_statement",
	CLASS_DEFINITION = "class_definition",
	METHOD_IMPL = "method_impl",
	BLOCK_RESTRUCTURE = "block_restructure",
	FORMATTING = "formatting",
	UNKNOWN = "unknown",
}

/**
 * 基础分析上下文
 */
export interface AnalysisContext {
	/** 当前文档 */
	document: vscode.TextDocument
	/** 最近变更 */
	recentChanges: RecentChange[]
	/** 相关文件 */
	relatedFiles: string[]
	/** 工作区根目录 */
	workspaceRoot: string
	/** 分析时间戳 */
	timestamp: number
}

/**
 * 模型分析上下文
 */
export interface ModelAnalysisContext extends AnalysisContext {
	/** 当前文件内容 */
	currentContent: string
	/** 变更历史 */
	changeHistory: DocumentChange[]
	/** 项目上下文 */
	projectContext: ProjectContext
	/** 代码库统计 */
	codebaseStats: CodebaseStats
	/** 分析目标 */
	analysisGoals: AnalysisGoal[]
}

/**
 * AST+LSP分析上下文
 */
export interface ASTLSPAnalysisContext extends AnalysisContext {
	/** 文档差异 */
	diff: DocumentDiff
	/** 当前位置 */
	position: vscode.Position
	/** AST节点 */
	astNode?: ASTNode
	/** 符号信息 */
	symbolInfo: SymbolInfo[]
	/** LSP诊断信息 */
	lspDiagnostics: vscode.Diagnostic[]
}

/**
 * 最近变更
 */
export interface RecentChange {
	/** 文件路径 */
	filePath: string
	/** 变更类型 */
	changeType: "create" | "modify" | "delete" | "rename"
	/** 变更时间戳 */
	timestamp: number
	/** 差异内容 */
	diff?: string
	/** 变更作者 */
	author?: string
	/** 提交哈希 */
	commitHash?: string
	/** 变更大小（行数） */
	changeSize: number
	/** 影响级别 */
	impactLevel: ImpactLevel
}

/**
 * 文档变更
 */
export interface DocumentChange {
	/** 变更ID */
	id: string
	/** 变更时间 */
	timestamp: number
	/** 变更类型 */
	type: ChangeType
	/** 变更内容 */
	content: string
	/** 影响范围 */
	range: vscode.Range
}

/**
 * 文档差异（完整的diff，不是单字符diff）
 */
export interface DocumentDiff {
	/** 新增行 */
	additions: DiffLine[]
	/** 删除行 */
	deletions: DiffLine[]
	/** 修改行 */
	modifications: DiffLine[]
	/** 是否为完整diff */
	isComplete: boolean
	/** 差异类型 */
	diffType: DiffType
	/** 受影响的符号 */
	affectedSymbols: string[]
	/** 上下文行数 */
	contextLines: number
	/** 差异元数据 */
	metadata: DiffMetadata
	/** 旧内容（用于TreeSitter变更检测） */
	oldContent?: string
	/** 新内容（用于TreeSitter变更检测） */
	newContent?: string
}

/**
 * 差异行
 */
export interface DiffLine {
	/** 行号 */
	lineNumber: number
	/** 行内容 */
	content: string
	/** 行类型 */
	type: "added" | "removed" | "modified"
	/** 缩进级别 */
	indentLevel: number
	/** 语法类型 */
	syntaxType?: string
}

/**
 * 差异元数据
 */
export interface DiffMetadata {
	/** 差异检测置信度 */
	confidence: number
	/** 变更复杂度 */
	complexity: number
	/** 风险级别 */
	riskLevel: "low" | "medium" | "high"
	/** 预估影响范围 */
	estimatedImpact: string[]
}

/**
 * 项目上下文
 */
export interface ProjectContext {
	/** 包信息 */
	packageInfo: PackageInfo
	/** 依赖信息 */
	dependencies: DependencyInfo[]
	/** 构建配置 */
	buildConfig: BuildConfig
	/** 测试文件 */
	testFiles: string[]
	/** 文档文件 */
	documentationFiles: string[]
}

/**
 * 包信息
 */
export interface PackageInfo {
	/** 包名 */
	name: string
	/** 版本 */
	version: string
	/** 描述 */
	description?: string
	/** 主文件 */
	main?: string
	/** 脚本 */
	scripts?: Record<string, string>
}

/**
 * 依赖信息
 */
export interface DependencyInfo {
	/** 依赖名 */
	name: string
	/** 版本 */
	version: string
	/** 类型 */
	type: "dependency" | "devDependency" | "peerDependency"
}

/**
 * 构建配置
 */
export interface BuildConfig {
	/** 构建工具 */
	tool: string
	/** 配置文件路径 */
	configPath?: string
	/** 输出目录 */
	outputDir?: string
}

/**
 * 代码库统计
 */
export interface CodebaseStats {
	/** 总文件数 */
	totalFiles: number
	/** 总行数 */
	totalLines: number
	/** 语言分布 */
	languageDistribution: Record<string, number>
	/** 复杂度指标 */
	complexityMetrics: ComplexityMetrics
	/** 质量指标 */
	qualityMetrics: QualityMetrics
}

/**
 * 复杂度指标
 */
export interface ComplexityMetrics {
	/** 平均圈复杂度 */
	averageCyclomaticComplexity: number
	/** 最大函数长度 */
	maxFunctionLength: number
	/** 平均函数长度 */
	averageFunctionLength: number
}

/**
 * 质量指标
 */
export interface QualityMetrics {
	/** 测试覆盖率 */
	testCoverage: number
	/** 代码重复率 */
	duplicationRate: number
	/** 技术债务指数 */
	technicalDebtIndex: number
}

/**
 * AST节点
 */
export interface ASTNode {
	/** 节点类型 */
	type: string
	/** 字节范围 */
	range: [number, number]
	/** 开始位置 */
	startPosition: Position
	/** 结束位置 */
	endPosition: Position
	/** 子节点 */
	children: ASTNode[]
	/** 父节点 */
	parent?: ASTNode
	/** 节点文本 */
	text?: string
	/** 命名子节点 */
	namedChildren: ASTNode[]
	/** 字段名 */
	fieldName?: string
}

/**
 * 位置
 */
export interface Position {
	/** 行号 */
	row: number
	/** 列号 */
	column: number
}

/**
 * 符号信息
 */
export interface SymbolInfo {
	/** 符号名称 */
	name: string
	/** 符号类型 */
	type: string
	/** 符号种类 */
	kind: vscode.SymbolKind
	/** 符号范围 */
	range: vscode.Range
	/** 选择范围 */
	selectionRange: vscode.Range
	/** 引用位置 */
	references: vscode.Location[]
	/** 定义位置 */
	definition?: vscode.Location
	/** 实现位置 */
	implementations: vscode.Location[]
	/** 容器名称 */
	containerName?: string
	/** 详细信息 */
	detail?: string
	/** 文档说明 */
	documentation?: string
}

/**
 * 分析目标
 */
export enum AnalysisGoal {
	LOGIC_COMPLETION = "logic_completion",
	FUNCTION_IMPLEMENTATION = "function_implementation",
	VARIABLE_USAGE = "variable_usage",
	CODE_FORMATTING = "code_formatting",
	LINT_ISSUES = "lint_issues",
	PERFORMANCE_OPTIMIZATION = "performance_optimization",
	SECURITY_ANALYSIS = "security_analysis",
	DOCUMENTATION = "documentation",
}

/**
 * 分析状态
 */
export enum AnalysisStatus {
	PENDING = "pending",
	RUNNING = "running",
	COMPLETED = "completed",
	FAILED = "failed",
	CANCELLED = "cancelled",
}

/**
 * 分析任务
 */
export interface AnalysisTask {
	/** 任务ID */
	id: string
	/** 文档URI */
	documentUri: string
	/** 分析类型 */
	analysisType: AnalysisType
	/** 优先级 */
	priority: number
	/** 创建时间 */
	createdAt: number
	/** 开始时间 */
	startedAt?: number
	/** 完成时间 */
	completedAt?: number
	/** 状态 */
	status: AnalysisStatus
	/** 上下文 */
	context: AnalysisContext
}

/**
 * 分析结果
 */
export interface AnalysisResult {
	/** 任务ID */
	taskId: string
	/** 分析类型 */
	analysisType: AnalysisType
	/** 建议列表 */
	suggestions: import("./Suggestion").Suggestion[]
	/** 分析耗时 */
	duration: number
	/** 错误信息 */
	error?: string
	/** 元数据 */
	metadata: {
		/** 分析的代码行数 */
		analyzedLines: number
		/** 检测到的问题数 */
		issuesFound: number
		/** 置信度 */
		confidence: number
	}
}
