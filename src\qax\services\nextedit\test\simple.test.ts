/**
 * 简单测试 - 验证 Jest 配置是否正确
 */

import * as vscode from "vscode"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { CacheManager } from "../cache/CacheManager"

describe("简单测试套件", () => {
	test("应该能够导入 VSCode mock", () => {
		expect(vscode).toBeDefined()
		expect(vscode.Position).toBeDefined()
		expect(vscode.Range).toBeDefined()
		expect(vscode.Uri).toBeDefined()
	})

	test("应该能够创建 VSCode 对象", () => {
		const position = new vscode.Position(1, 5)
		expect(position.line).toBe(1)
		expect(position.character).toBe(5)

		const range = new vscode.Range(0, 0, 1, 10)
		expect(range.start.line).toBe(0)
		expect(range.end.line).toBe(1)

		const uri = vscode.Uri.parse("file:///test.ts")
		expect(uri.scheme).toBe("file")
	})

	test("应该能够加载默认设置", () => {
		expect(DEFAULT_NEXT_EDIT_SETTINGS).toBeDefined()
		expect(DEFAULT_NEXT_EDIT_SETTINGS.triggerConfig).toBeDefined()
		expect(DEFAULT_NEXT_EDIT_SETTINGS.astLspAnalysis).toBeDefined()
		expect(DEFAULT_NEXT_EDIT_SETTINGS.modelRecommendation).toBeDefined()
	})

	test("应该能够创建 CacheManager", () => {
		const cacheManager = new CacheManager(DEFAULT_NEXT_EDIT_SETTINGS)
		expect(cacheManager).toBeDefined()

		const stats = cacheManager.getCacheStats()
		expect(stats).toBeDefined()
		expect(stats.ast).toBeDefined()
		expect(stats.lsp).toBeDefined()

		// 清理
		cacheManager.dispose()
	})

	test("应该能够测试异步功能", async () => {
		const promise = Promise.resolve("test")
		const result = await promise
		expect(result).toBe("test")
	})
})
