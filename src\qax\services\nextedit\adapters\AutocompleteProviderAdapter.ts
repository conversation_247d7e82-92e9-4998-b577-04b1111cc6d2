/**
 * QAX Next Edit - AutocompleteProvider 集成适配器
 * 集成现有的 AutocompleteProvider 和相关组件
 */

import * as vscode from "vscode"

/**
 * 现有 AutocompleteProvider 的接口定义
 */
interface ExistingAutocompleteProvider {
	provideInlineCompletionItems(
		document: vscode.TextDocument,
		position: vscode.Position,
		context: vscode.InlineCompletionContext,
		token: vscode.CancellationToken,
	): Promise<vscode.InlineCompletionItem[] | null>
}

/**
 * 现有上下文提取器的接口定义
 */
interface ExistingContextExtractor {
	extractIntelligentContext(
		document: vscode.TextDocument,
		position: vscode.Position,
		maxLines?: number,
	): Promise<{
		contextCode: string
		cursorLineInContext: number
		cursorCharInContext: number
		usedFullFile: boolean
		strategy: string
	}>
}

/**
 * 现有模板系统的接口定义
 */
interface ExistingTemplate {
	getSystemPrompt(): string
	template(codeContext: any, document: vscode.TextDocument, position: vscode.Position, snippets: any[]): Promise<string>
}

/**
 * AutocompleteProvider 集成适配器
 * 将现有的 AutocompleteProvider 功能集成到 QAX Next Edit 系统中
 */
export class AutocompleteProviderAdapter {
	private contextExtractor: ExistingContextExtractor | null = null
	private template: ExistingTemplate | null = null
	private provider: ExistingAutocompleteProvider | null = null

	constructor() {
		this.initializeComponents()
	}

	/**
	 * 初始化现有组件
	 */
	private initializeComponents(): void {
		try {
			// 尝试动态导入现有组件
			// 在实际环境中，这里会导入真实的组件
			// const { extractIntelligentContext } = require('../../../services/autocomplete/utils/contextExtractor')
			// const { holeFillerTemplate } = require('../../../services/autocomplete/templating/AutocompleteTemplate')

			// 模拟组件（用于开发和测试）
			this.contextExtractor = {
				extractIntelligentContext: async (document, position, maxLines = 100) => {
					// 模拟智能上下文提取
					const lines = document.getText().split("\n")
					const startLine = Math.max(0, position.line - 20)
					const endLine = Math.min(lines.length, position.line + 20)

					const contextLines = lines.slice(startLine, endLine)
					const contextCode = contextLines.join("\n")

					return {
						contextCode,
						cursorLineInContext: position.line - startLine,
						cursorCharInContext: position.character,
						usedFullFile: false,
						strategy: "context-window",
					}
				},
			}

			this.template = {
				getSystemPrompt: () => {
					return `You are a professional code completion assistant. Your task is to provide accurate, contextually appropriate code completions.

Rules:
1. Analyze the code context carefully
2. Provide only the necessary code to complete the current line or block
3. Maintain consistent coding style and patterns
4. Consider the programming language and its conventions
5. Respond with ONLY the completion code, no explanations or markdown

Focus on:
- Completing function implementations
- Adding missing imports
- Fixing syntax errors
- Following established patterns in the codebase`
				},
				template: async (codeContext, document, position, snippets) => {
					// 模拟模板生成
					const languageId = document.languageId
					const currentLine = document.lineAt(position.line).text
					const prefix = currentLine.substring(0, position.character)
					const suffix = currentLine.substring(position.character)

					return `Language: ${languageId}
Current context: ${prefix}{{CURSOR}}${suffix}

Please provide the appropriate code completion for the cursor position.

Context:
${codeContext.contextCode || "No additional context"}

Completion:`
				},
			}

			this.provider = {
				provideInlineCompletionItems: async (document, position, context, token) => {
					// 模拟代码补全
					const line = document.lineAt(position.line).text
					const prefix = line.substring(0, position.character)

					// 简单的补全逻辑
					if (prefix.endsWith("console.")) {
						return [
							{
								insertText: "log()",
								range: new vscode.Range(position, position),
							},
						]
					}

					if (prefix.endsWith("function ")) {
						return [
							{
								insertText: "name() {\n  \n}",
								range: new vscode.Range(position, position),
							},
						]
					}

					return null
				},
			}

			console.log("AutocompleteProvider components initialized successfully")
		} catch (error) {
			console.warn("Failed to initialize AutocompleteProvider components, using mock implementation:", error.message)
		}
	}

	/**
	 * 提取智能上下文
	 */
	async extractContext(
		document: vscode.TextDocument,
		position: vscode.Position,
		maxLines: number = 100,
	): Promise<{
		contextCode: string
		cursorLineInContext: number
		cursorCharInContext: number
		usedFullFile: boolean
		strategy: string
	}> {
		if (!this.contextExtractor) {
			throw new Error("Context extractor not initialized")
		}

		return await this.contextExtractor.extractIntelligentContext(document, position, maxLines)
	}

	/**
	 * 生成英文提示词模板
	 */
	async generateEnglishPrompt(
		codeContext: any,
		document: vscode.TextDocument,
		position: vscode.Position,
		snippets: any[] = [],
	): Promise<string> {
		if (!this.template) {
			throw new Error("Template not initialized")
		}

		// 获取系统提示词（英文）
		const systemPrompt = this.template.getSystemPrompt()

		// 生成用户提示词
		const userPrompt = await this.template.template(codeContext, document, position, snippets)

		return `${systemPrompt}\n\n${userPrompt}`
	}

	/**
	 * 获取代码补全建议
	 */
	async getCompletionSuggestions(
		document: vscode.TextDocument,
		position: vscode.Position,
		context: vscode.InlineCompletionContext,
		token: vscode.CancellationToken,
	): Promise<vscode.InlineCompletionItem[] | null> {
		if (!this.provider) {
			throw new Error("Provider not initialized")
		}

		return await this.provider.provideInlineCompletionItems(document, position, context, token)
	}

	/**
	 * 构建代码上下文对象
	 */
	async buildCodeContext(document: vscode.TextDocument, position: vscode.Position): Promise<any> {
		try {
			// 提取智能上下文
			const extractedContext = await this.extractContext(document, position)

			// 获取当前行和周围行
			const currentLine = document.lineAt(position.line).text
			const precedingLines: string[] = []
			const followingLines: string[] = []

			// 获取前面的行
			for (let i = Math.max(0, position.line - 10); i < position.line; i++) {
				precedingLines.push(document.lineAt(i).text)
			}

			// 获取后面的行
			for (let i = position.line + 1; i < Math.min(document.lineCount, position.line + 11); i++) {
				followingLines.push(document.lineAt(i).text)
			}

			// 简单的导入语句提取
			const imports: string[] = []
			const text = document.getText()
			const importRegex = /^(import|from|#include|using|require).*$/gm
			let match
			while ((match = importRegex.exec(text)) !== null) {
				imports.push(match[0])
			}

			return {
				currentLine,
				precedingLines,
				followingLines,
				imports,
				contextCode: extractedContext.contextCode,
				strategy: extractedContext.strategy,
				usedFullFile: extractedContext.usedFullFile,
				definitions: [], // 可以扩展为包含函数定义等
			}
		} catch (error) {
			console.error("Error building code context:", error)
			return {
				currentLine: document.lineAt(position.line).text,
				precedingLines: [],
				followingLines: [],
				imports: [],
				contextCode: "",
				strategy: "fallback",
				usedFullFile: false,
				definitions: [],
			}
		}
	}

	/**
	 * 处理补全响应，确保只返回需要的结果
	 */
	processCompletionResponse(response: string): string {
		try {
			// 移除多余的格式化
			let processed = response.trim()

			// 移除 markdown 代码块标记
			processed = processed.replace(/^```[\w]*\n?/gm, "")
			processed = processed.replace(/\n?```$/gm, "")

			// 移除解释性文本，只保留代码
			const lines = processed.split("\n")
			const codeLines: string[] = []

			for (const line of lines) {
				// 跳过解释性文本
				if (line.startsWith("//") && line.includes("explanation")) continue
				if (line.startsWith("/*") && line.includes("explanation")) continue
				if (line.toLowerCase().includes("explanation:")) continue
				if (line.toLowerCase().includes("here is")) continue
				if (line.toLowerCase().includes("this code")) continue

				codeLines.push(line)
			}

			return codeLines.join("\n").trim()
		} catch (error) {
			console.warn("Error processing completion response:", error)
			return response
		}
	}

	/**
	 * 释放资源
	 */
	dispose(): void {
		this.contextExtractor = null
		this.template = null
		this.provider = null
	}
}
