/**
 * QAX Codegen 平台模型获取服务
 * <AUTHOR>
 */

import { Controller } from "@/core/controller"
import { StringArray } from "@shared/proto/common"
import { OpenAiModelsRequest } from "@shared/proto/models"
import { getQaxCodegenModelsUrl, getQaxDefaultModels } from "@shared/qax"
import { QaxAuthService } from "@/qax/services/auth/QaxAuthService"
import { isValidJWTToken } from "@/qax/utils/jwt"

/**
 * Fetches available models from QAX Codegen
 * @param controller The controller instance
 * @param request The request (not used, uses JWT token from QAX Account)
 * @returns Array of QAX Codegen model names
 */
export async function getQaxCodegenModels(controller: Controller, request: OpenAiModelsRequest): Promise<StringArray> {
	try {
		// Get JWT token from QAX Account service
		const qaxAuthService = QaxAuthService.getInstance(controller.context)
		const jwtToken = await qaxAuthService.getAuthToken()

		// 使用统一的 JWT 验证工具
		if (!isValidJWTToken(jwtToken || "")) {
			// Return default models when not authenticated
			return StringArray.create({
				values: Array.from(getQaxDefaultModels()),
			})
		}

		const response = await fetch(getQaxCodegenModelsUrl(), {
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${jwtToken}`,
			},
		})

		if (!response.ok) {
			// Enhanced error handling following Cline 3.18.12 pattern
			let errorMessage = `Failed to fetch QAX Codegen models (HTTP ${response.status})`

			if (response.status === 401) {
				errorMessage = "QAX Codegen authentication failed. Please login again."
			} else if (response.status === 403) {
				errorMessage = "QAX Codegen access denied. Please check your account permissions."
			} else if (response.status === 429) {
				errorMessage = "QAX Codegen rate limit exceeded. Please try again later."
			} else if (response.status >= 500) {
				errorMessage = "QAX Codegen server error. Please try again later."
			}

			console.warn(`[QAX Codegen] ${errorMessage}`)

			// Return default models on API failure
			return StringArray.create({
				values: Array.from(getQaxDefaultModels()),
			})
		}

		const result = await response.json()
		const modelsData = result.data || []

		// Extract model IDs from the [displayName, modelId] format
		const modelIds = modelsData.map((model: [string, string]) => model[1])

		return StringArray.create({ values: modelIds })
	} catch (error) {
		// Enhanced error handling following Cline 3.18.12 pattern
		const errorMessage = error instanceof Error ? error.message : `${error}`
		console.error("[QAX Codegen] Failed to fetch models:", errorMessage)

		// Return default models on any error
		return StringArray.create({
			values: Array.from(getQaxDefaultModels()),
		})
	}
}
