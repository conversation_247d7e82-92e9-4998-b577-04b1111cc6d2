/**
 * QAX Next Edit - 分析引擎
 * 负责协调 AST+LSP 分析器和模型分析器，管理分析任务调度
 */

import * as vscode from "vscode"
import {
	AnalysisType,
	AnalysisTask,
	AnalysisResult,
	AnalysisStatus,
	AnalysisContext,
	ModelAnalysisContext,
	ASTLSPAnalysisContext,
	RecentChange,
	DocumentDiff,
	ProjectContext,
	CodebaseStats,
} from "../types/AnalysisContext"
import { Suggestion } from "../types/Suggestion"
import { NextEditSettings } from "../types/NextEditSettings"
import { DiffGenerator } from "../analyzers/DiffGenerator"

/**
 * 分析任务优先级队列
 */
class AnalysisTaskQueue {
	private tasks: AnalysisTask[] = []
	private runningTasks: Map<string, AnalysisTask> = new Map()

	/**
	 * 添加任务
	 */
	enqueue(task: AnalysisTask): void {
		// 按优先级插入
		const insertIndex = this.tasks.findIndex((t) => t.priority < task.priority)
		if (insertIndex === -1) {
			this.tasks.push(task)
		} else {
			this.tasks.splice(insertIndex, 0, task)
		}
	}

	/**
	 * 获取下一个任务
	 */
	dequeue(): AnalysisTask | null {
		return this.tasks.shift() || null
	}

	/**
	 * 取消任务
	 */
	cancel(taskId: string): boolean {
		// 从队列中移除
		const queueIndex = this.tasks.findIndex((t) => t.id === taskId)
		if (queueIndex !== -1) {
			this.tasks.splice(queueIndex, 1)
			return true
		}

		// 从运行中任务移除
		if (this.runningTasks.has(taskId)) {
			const task = this.runningTasks.get(taskId)!
			task.status = AnalysisStatus.CANCELLED
			this.runningTasks.delete(taskId)
			return true
		}

		return false
	}

	/**
	 * 标记任务为运行中
	 */
	markRunning(task: AnalysisTask): void {
		task.status = AnalysisStatus.RUNNING
		task.startedAt = Date.now()
		this.runningTasks.set(task.id, task)
	}

	/**
	 * 标记任务完成
	 */
	markCompleted(taskId: string): void {
		const task = this.runningTasks.get(taskId)
		if (task) {
			task.status = AnalysisStatus.COMPLETED
			task.completedAt = Date.now()
			this.runningTasks.delete(taskId)
		}
	}

	/**
	 * 获取队列状态
	 */
	getStatus(): { pending: number; running: number; total: number } {
		return {
			pending: this.tasks.length,
			running: this.runningTasks.size,
			total: this.tasks.length + this.runningTasks.size,
		}
	}

	/**
	 * 获取所有任务
	 */
	getAllTasks(): AnalysisTask[] {
		return [...this.tasks, ...Array.from(this.runningTasks.values())]
	}
}

/**
 * 分析引擎
 */
export class AnalysisEngine {
	private settings: NextEditSettings
	private diffGenerator: DiffGenerator
	private taskQueue: AnalysisTaskQueue
	private maxConcurrentTasks: number = 3
	private recentChanges: Map<string, RecentChange[]> = new Map()
	private astLspAnalyzer: any // 延迟注入
	private modelAnalyzer: any // 延迟注入

	// 事件回调
	private onAnalysisStartedCallbacks: ((taskId: string, analysisType: AnalysisType) => void)[] = []
	private onAnalysisCompletedCallbacks: ((taskId: string, result: AnalysisResult) => void)[] = []
	private onAnalysisErrorCallbacks: ((taskId: string, error: Error) => void)[] = []

	constructor(settings: NextEditSettings, diffGenerator: DiffGenerator) {
		this.settings = settings
		this.diffGenerator = diffGenerator
		this.taskQueue = new AnalysisTaskQueue()
		this.maxConcurrentTasks = settings.triggerConfig.maxConcurrentAnalyses
	}

	/**
	 * 设置分析器（依赖注入）
	 */
	setAnalyzers(astLspAnalyzer: any, modelAnalyzer: any): void {
		this.astLspAnalyzer = astLspAnalyzer
		this.modelAnalyzer = modelAnalyzer
	}

	/**
	 * 更新配置
	 */
	updateSettings(settings: NextEditSettings): void {
		this.settings = settings
		this.maxConcurrentTasks = settings.triggerConfig.maxConcurrentAnalyses
	}

	/**
	 * 调度分析任务
	 */
	async scheduleAnalysis(
		documentUri: string,
		analysisType: AnalysisType,
		priority: number,
		context: AnalysisContext,
	): Promise<string> {
		const taskId = this.generateTaskId()

		const task: AnalysisTask = {
			id: taskId,
			documentUri,
			analysisType,
			priority,
			createdAt: Date.now(),
			status: AnalysisStatus.PENDING,
			context,
		}

		// 添加到队列
		this.taskQueue.enqueue(task)

		// 尝试立即执行
		this.processQueue()

		return taskId
	}

	/**
	 * 取消分析任务
	 */
	async cancelAnalysis(taskId: string): Promise<boolean> {
		return this.taskQueue.cancel(taskId)
	}

	/**
	 * 获取分析队列状态
	 */
	getAnalysisQueue(): AnalysisTask[] {
		return this.taskQueue.getAllTasks()
	}

	/**
	 * 收集最近变更
	 */
	async collectRecentChanges(documentUri?: string, timeWindowMs: number = 300000): Promise<RecentChange[]> {
		const cutoffTime = Date.now() - timeWindowMs

		if (documentUri) {
			const changes = this.recentChanges.get(documentUri) || []
			return changes.filter((change) => change.timestamp > cutoffTime)
		}

		// 收集所有文档的最近变更
		const allChanges: RecentChange[] = []
		for (const changes of Array.from(this.recentChanges.values())) {
			allChanges.push(...changes.filter((change) => change.timestamp > cutoffTime))
		}

		// 按时间排序
		return allChanges.sort((a, b) => b.timestamp - a.timestamp)
	}

	/**
	 * 收集相关文件
	 */
	async collectRelatedFiles(document: vscode.TextDocument): Promise<string[]> {
		const relatedFiles: string[] = []
		const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri)

		if (!workspaceFolder) {
			return relatedFiles
		}

		try {
			// 查找同目录下的相关文件
			const dirPath = document.uri.fsPath.substring(0, document.uri.fsPath.lastIndexOf("/"))
			const files = await vscode.workspace.findFiles(
				new vscode.RelativePattern(dirPath, "*.{ts,js,tsx,jsx,py,rs,go,cpp,c,cs,java,php}"),
				null,
				10,
			)

			for (const file of files) {
				if (file.toString() !== document.uri.toString()) {
					relatedFiles.push(file.toString())
				}
			}

			// 查找导入的文件
			const importedFiles = await this.extractImportedFiles(document)
			relatedFiles.push(...importedFiles)
		} catch (error) {
			console.error("Error collecting related files:", error)
		}

		return Array.from(new Set(relatedFiles)) // 去重
	}

	/**
	 * 构建分析上下文
	 */
	async buildAnalysisContext(
		document: vscode.TextDocument,
		analysisType: AnalysisType,
		position?: vscode.Position,
		diff?: DocumentDiff,
	): Promise<AnalysisContext | ModelAnalysisContext | ASTLSPAnalysisContext> {
		const documentUri = document.uri.toString()

		const baseContext: AnalysisContext = {
			document,
			recentChanges: await this.collectRecentChanges(documentUri),
			relatedFiles: await this.collectRelatedFiles(document),
			workspaceRoot: vscode.workspace.getWorkspaceFolder(document.uri)?.uri.fsPath || "",
			timestamp: Date.now(),
		}

		if (analysisType === AnalysisType.MODEL) {
			return this.buildModelAnalysisContext(baseContext)
		} else if (analysisType === AnalysisType.AST_LSP && position && diff) {
			return await this.buildASTLSPAnalysisContext(baseContext, position, diff)
		}

		return baseContext
	}

	/**
	 * 合并分析结果
	 */
	mergeAnalysisResults(results: AnalysisResult[]): Suggestion[] {
		const allSuggestions: Suggestion[] = []

		for (const result of results) {
			allSuggestions.push(...result.suggestions)
		}

		return this.filterDuplicateSuggestions(allSuggestions)
	}

	/**
	 * 过滤重复建议
	 */
	filterDuplicateSuggestions(suggestions: Suggestion[]): Suggestion[] {
		const seen = new Set<string>()
		const filtered: Suggestion[] = []

		for (const suggestion of suggestions) {
			// 创建唯一标识符
			const key = `${suggestion.location.anchor}:${suggestion.location.position}:${suggestion.patch.new_content}`

			if (!seen.has(key)) {
				seen.add(key)
				filtered.push(suggestion)
			}
		}

		return filtered
	}

	/**
	 * 记录文档变更
	 */
	recordDocumentChange(documentUri: string, changeType: "create" | "modify" | "delete" | "rename", diff?: string): void {
		if (!this.recentChanges.has(documentUri)) {
			this.recentChanges.set(documentUri, [])
		}

		const changes = this.recentChanges.get(documentUri)!
		const change: RecentChange = {
			filePath: documentUri,
			changeType,
			timestamp: Date.now(),
			diff,
			changeSize: diff ? diff.split("\n").length : 1,
			impactLevel: this.assessChangeImpact(changeType, diff),
		}

		changes.push(change)

		// 保持最近100个变更
		if (changes.length > 100) {
			changes.shift()
		}
	}

	/**
	 * 注册事件回调
	 */
	onAnalysisStarted(callback: (taskId: string, analysisType: AnalysisType) => void): void {
		this.onAnalysisStartedCallbacks.push(callback)
	}

	onAnalysisCompleted(callback: (taskId: string, result: AnalysisResult) => void): void {
		this.onAnalysisCompletedCallbacks.push(callback)
	}

	onAnalysisError(callback: (taskId: string, error: Error) => void): void {
		this.onAnalysisErrorCallbacks.push(callback)
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		// 取消所有待处理的任务
		const allTasks = this.taskQueue.getAllTasks()
		for (const task of allTasks) {
			this.taskQueue.cancel(task.id)
		}

		// 清理变更历史
		this.recentChanges.clear()
	}

	// 私有方法

	/**
	 * 处理任务队列
	 */
	private async processQueue(): Promise<void> {
		const status = this.taskQueue.getStatus()

		// 检查是否可以执行更多任务
		if (status.running >= this.maxConcurrentTasks) {
			return
		}

		const task = this.taskQueue.dequeue()
		if (!task) {
			return
		}

		// 标记任务为运行中
		this.taskQueue.markRunning(task)
		this.notifyAnalysisStarted(task.id, task.analysisType)

		try {
			// 执行分析任务
			const result = await this.executeAnalysisTask(task)

			// 标记任务完成
			this.taskQueue.markCompleted(task.id)
			this.notifyAnalysisCompleted(task.id, result)
		} catch (error) {
			// 处理错误
			this.taskQueue.markCompleted(task.id)
			this.notifyAnalysisError(task.id, error as Error)
		}

		// 继续处理队列
		setTimeout(() => this.processQueue(), 0)
	}

	/**
	 * 执行分析任务
	 */
	private async executeAnalysisTask(task: AnalysisTask): Promise<AnalysisResult> {
		const startTime = Date.now()

		// 根据分析类型执行不同的分析逻辑
		let suggestions: Suggestion[] = []

		if (task.analysisType === AnalysisType.MODEL) {
			// 调用 ModelAnalyzer 进行深度分析
			console.log(`🤖 Starting model analysis for ${task.context.document.uri.toString()}`)
			console.log(`🤖 Model analysis context:`, {
				documentLength: task.context.document.getText().length,
				languageId: task.context.document.languageId,
				analysisGoals: (task.context as ModelAnalysisContext).analysisGoals,
			})
			suggestions = await this.performModelAnalysis(task.context as ModelAnalysisContext)
			console.log(`🤖 Model analysis completed: ${suggestions.length} suggestions generated`)
			console.log(
				`🤖 Model suggestions summary:`,
				suggestions.map((s) => ({
					type: s.type,
					confidence: s.confidence,
					category: s.category,
					description: s.description.substring(0, 100) + "...",
				})),
			)
		} else if (task.analysisType === AnalysisType.AST_LSP) {
			// 调用 ASTLSPAnalyzer 进行语法树和LSP分析
			console.log(`🌳 Starting AST/LSP analysis for ${task.context.document.uri.toString()}`)
			const astLspContext = task.context as ASTLSPAnalysisContext
			console.log(`🌳 AST/LSP analysis context:`, {
				documentLength: task.context.document.getText().length,
				languageId: task.context.document.languageId,
				hasDiff: !!astLspContext.diff,
				diffLength: astLspContext.diff?.newContent?.length || 0,
				hasPosition: !!astLspContext.position,
				symbolInfoCount: astLspContext.symbolInfo?.length || 0,
				diagnosticsCount: astLspContext.lspDiagnostics?.length || 0,
			})

			// 检查语法树缓存状态（如果有缓存管理器的话）
			console.log(`🌳 AST cache status: Cache manager not directly accessible from AnalysisEngine`)

			// 检测到的 diff 信息
			if (astLspContext.diff) {
				console.log(`🔍 Detected diff:`, {
					oldContentLength: astLspContext.diff.oldContent?.length || 0,
					newContentLength: astLspContext.diff.newContent?.length || 0,
					diffType: astLspContext.diff.diffType || "unknown",
					additionsCount: astLspContext.diff.additions?.length || 0,
					deletionsCount: astLspContext.diff.deletions?.length || 0,
					modificationsCount: astLspContext.diff.modifications?.length || 0,
					affectedSymbols: astLspContext.diff.affectedSymbols?.length || 0,
					isComplete: astLspContext.diff.isComplete,
				})
			}

			suggestions = await this.performASTLSPAnalysis(astLspContext)
			console.log(`🌳 AST/LSP analysis completed: ${suggestions.length} suggestions generated`)
			console.log(
				`🌳 AST/LSP suggestions summary:`,
				suggestions.map((s) => ({
					type: s.type,
					confidence: s.confidence,
					category: s.category,
					source: s.source,
					description: s.description.substring(0, 100) + "...",
				})),
			)
		}

		const endTime = Date.now()
		const duration = endTime - startTime

		return {
			taskId: task.id,
			analysisType: task.analysisType,
			suggestions,
			duration,
			metadata: {
				analyzedLines: task.context.document.lineCount,
				issuesFound: suggestions.length,
				confidence: this.calculateAverageConfidence(suggestions),
			},
		}
	}

	/**
	 * 模型分析
	 */
	private async performModelAnalysis(context: ModelAnalysisContext): Promise<Suggestion[]> {
		if (this.modelAnalyzer) {
			return await this.modelAnalyzer.performDeepAnalysis(context)
		}

		// 降级处理：模拟分析
		await new Promise((resolve) => setTimeout(resolve, 100))
		return []
	}

	/**
	 * AST+LSP分析
	 */
	private async performASTLSPAnalysis(context: ASTLSPAnalysisContext): Promise<Suggestion[]> {
		if (this.astLspAnalyzer) {
			return await this.astLspAnalyzer.performFastAnalysis(context)
		}

		// 降级处理：模拟分析
		await new Promise((resolve) => setTimeout(resolve, 50))
		return []
	}

	private generateTaskId(): string {
		return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
	}

	private async buildModelAnalysisContext(baseContext: AnalysisContext): Promise<ModelAnalysisContext> {
		return {
			...baseContext,
			currentContent: baseContext.document.getText(),
			changeHistory: await this.collectChangeHistory(baseContext.document),
			projectContext: await this.collectProjectContext(baseContext.document),
			codebaseStats: await this.collectCodebaseStats(),
			analysisGoals: this.settings.modelRecommendation.analysisGoals,
		}
	}

	private async buildASTLSPAnalysisContext(
		baseContext: AnalysisContext,
		position: vscode.Position,
		diff: DocumentDiff,
	): Promise<ASTLSPAnalysisContext> {
		return {
			...baseContext,
			diff,
			position,
			symbolInfo: await this.collectSymbolInfo(baseContext.document, position),
			lspDiagnostics: await this.collectLSPDiagnostics(baseContext.document),
		}
	}

	private async collectProjectContext(document: vscode.TextDocument): Promise<any> {
		try {
			console.log(`📁 Collecting project context for ${document.uri.toString()}`)

			const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri)
			if (!workspaceFolder) {
				return {}
			}

			// 收集项目基本信息
			const projectContext = {
				workspaceName: workspaceFolder.name,
				workspaceUri: workspaceFolder.uri.toString(),
				language: document.languageId,
				fileCount: 0,
				importedFiles: await this.extractImportedFiles(document),
				packageInfo: await this.collectPackageInfo(workspaceFolder.uri),
			}

			console.log(`📁 Project context collected: ${JSON.stringify(projectContext, null, 2)}`)
			return projectContext
		} catch (error) {
			console.warn("Failed to collect project context:", error)
			return {}
		}
	}

	private async collectCodebaseStats(): Promise<any> {
		try {
			console.log("📊 Collecting codebase statistics")

			const workspaceFolders = vscode.workspace.workspaceFolders
			if (!workspaceFolders || workspaceFolders.length === 0) {
				return {}
			}

			// 收集代码库统计信息
			const stats = {
				totalFiles: 0,
				languageDistribution: {} as Record<string, number>,
				totalLines: 0,
				lastModified: Date.now(),
			}

			// 简化的统计收集
			for (const folder of workspaceFolders) {
				try {
					const files = await vscode.workspace.findFiles(
						new vscode.RelativePattern(folder, "**/*.{ts,js,py,rs,go,cpp,c,cs,java,php}"),
						"**/node_modules/**",
						1000, // 限制文件数量
					)

					stats.totalFiles += files.length

					for (const file of files) {
						const ext = file.path.split(".").pop()?.toLowerCase()
						if (ext) {
							stats.languageDistribution[ext] = (stats.languageDistribution[ext] || 0) + 1
						}
					}
				} catch (error) {
					console.warn(`Failed to collect stats for folder ${folder.name}:`, error)
				}
			}

			console.log(`📊 Codebase stats: ${JSON.stringify(stats, null, 2)}`)
			return stats
		} catch (error) {
			console.warn("Failed to collect codebase stats:", error)
			return {}
		}
	}

	private async extractImportedFiles(document: vscode.TextDocument): Promise<string[]> {
		try {
			console.log(`📦 Extracting imported files for ${document.uri.toString()}`)

			const text = document.getText()
			const imports: string[] = []

			// 根据语言类型提取导入
			if (document.languageId === "typescript" || document.languageId === "javascript") {
				// 匹配 import 语句
				const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g
				const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g

				let match
				while ((match = importRegex.exec(text)) !== null) {
					imports.push(match[1])
				}
				while ((match = requireRegex.exec(text)) !== null) {
					imports.push(match[1])
				}
			} else if (document.languageId === "python") {
				// 匹配 Python import 语句
				const importRegex = /(?:from\s+(\S+)\s+import|import\s+(\S+))/g
				let match
				while ((match = importRegex.exec(text)) !== null) {
					imports.push(match[1] || match[2])
				}
			}

			console.log(`📦 Found ${imports.length} imports: ${imports.join(", ")}`)
			return imports
		} catch (error) {
			console.warn("Failed to extract imported files:", error)
			return []
		}
	}

	private assessChangeImpact(changeType: string, diff?: string): any {
		try {
			console.log(`⚖️ Assessing change impact: ${changeType}`)

			// 基于变更类型和差异内容评估影响
			let impact = "low"

			if (changeType.includes("function") || changeType.includes("method")) {
				impact = "medium"
			}

			if (changeType.includes("interface") || changeType.includes("type") || changeType.includes("class")) {
				impact = "high"
			}

			if (diff) {
				const lines = diff.split("\n").length
				if (lines > 50) {
					impact = "high"
				} else if (lines > 10) {
					impact = "medium"
				}
			}

			console.log(`⚖️ Change impact assessed: ${impact}`)
			return impact
		} catch (error) {
			console.warn("Failed to assess change impact:", error)
			return "medium"
		}
	}

	private calculateAverageConfidence(suggestions: Suggestion[]): number {
		if (suggestions.length === 0) {
			return 0
		}
		const total = suggestions.reduce((sum, s) => sum + s.confidence, 0)
		return total / suggestions.length
	}

	/**
	 * 收集包信息
	 */
	private async collectPackageInfo(workspaceUri: vscode.Uri): Promise<any> {
		try {
			console.log(`📦 Collecting package info for ${workspaceUri.toString()}`)

			// 查找 package.json
			const packageJsonUri = vscode.Uri.joinPath(workspaceUri, "package.json")
			try {
				const packageJsonContent = await vscode.workspace.fs.readFile(packageJsonUri)
				const packageJson = JSON.parse(packageJsonContent.toString())

				return {
					name: packageJson.name,
					version: packageJson.version,
					dependencies: Object.keys(packageJson.dependencies || {}),
					devDependencies: Object.keys(packageJson.devDependencies || {}),
					scripts: Object.keys(packageJson.scripts || {}),
				}
			} catch {
				// 如果没有 package.json，尝试其他配置文件
				return await this.collectOtherPackageInfo(workspaceUri)
			}
		} catch (error) {
			console.warn("Failed to collect package info:", error)
			return {}
		}
	}

	/**
	 * 收集其他类型的包信息
	 */
	private async collectOtherPackageInfo(workspaceUri: vscode.Uri): Promise<any> {
		try {
			// 检查 Python requirements.txt
			const requirementsUri = vscode.Uri.joinPath(workspaceUri, "requirements.txt")
			try {
				const requirementsContent = await vscode.workspace.fs.readFile(requirementsUri)
				const requirements = requirementsContent
					.toString()
					.split("\n")
					.filter((line) => line.trim())
				return {
					type: "python",
					requirements: requirements,
				}
			} catch {
				// 继续检查其他文件
			}

			// 检查 Rust Cargo.toml
			const cargoUri = vscode.Uri.joinPath(workspaceUri, "Cargo.toml")
			try {
				const cargoContent = await vscode.workspace.fs.readFile(cargoUri)
				return {
					type: "rust",
					config: cargoContent.toString(),
				}
			} catch {
				// 继续检查其他文件
			}

			return { type: "unknown" }
		} catch (error) {
			console.warn("Failed to collect other package info:", error)
			return {}
		}
	}

	private notifyAnalysisStarted(taskId: string, analysisType: AnalysisType): void {
		this.onAnalysisStartedCallbacks.forEach((callback) => {
			try {
				callback(taskId, analysisType)
			} catch (error) {
				console.error("Error in analysis started callback:", error)
			}
		})
	}

	private notifyAnalysisCompleted(taskId: string, result: AnalysisResult): void {
		this.onAnalysisCompletedCallbacks.forEach((callback) => {
			try {
				callback(taskId, result)
			} catch (error) {
				console.error("Error in analysis completed callback:", error)
			}
		})
	}

	private notifyAnalysisError(taskId: string, error: Error): void {
		this.onAnalysisErrorCallbacks.forEach((callback) => {
			try {
				callback(taskId, error)
			} catch (error) {
				console.error("Error in analysis error callback:", error)
			}
		})
	}

	/**
	 * 收集变更历史
	 */
	private async collectChangeHistory(document: vscode.TextDocument): Promise<any[]> {
		try {
			// 从 Git 获取文件的变更历史
			const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri)
			if (!workspaceFolder) {
				return []
			}

			// 简化的变更历史收集
			const relativePath = vscode.workspace.asRelativePath(document.uri)
			console.log(`📚 Collecting change history for ${relativePath}`)

			// 这里可以集成 Git API 或其他版本控制系统
			// 目前返回模拟数据
			return [
				{
					timestamp: Date.now() - 3600000, // 1小时前
					type: "modification",
					linesChanged: 5,
					description: "Recent code changes",
				},
			]
		} catch (error) {
			console.warn("Failed to collect change history:", error)
			return []
		}
	}

	/**
	 * 收集符号信息
	 */
	private async collectSymbolInfo(document: vscode.TextDocument, position?: vscode.Position): Promise<any[]> {
		try {
			console.log(`🔍 Collecting symbol info for ${document.uri.toString()}`)

			// 使用 LSP 获取符号信息
			const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
				"vscode.executeDocumentSymbolProvider",
				document.uri,
			)

			if (!symbols) {
				return []
			}

			// 如果有位置信息，过滤相关符号
			if (position) {
				return symbols
					.filter((symbol) => {
						return symbol.range.contains(position)
					})
					.map((symbol) => ({
						name: symbol.name,
						kind: symbol.kind,
						range: symbol.range,
						detail: symbol.detail,
					}))
			}

			// 返回所有符号的简化信息
			return symbols.map((symbol) => ({
				name: symbol.name,
				kind: symbol.kind,
				range: symbol.range,
				detail: symbol.detail,
			}))
		} catch (error) {
			console.warn("Failed to collect symbol info:", error)
			return []
		}
	}

	/**
	 * 收集 LSP 诊断信息
	 */
	private async collectLSPDiagnostics(document: vscode.TextDocument): Promise<vscode.Diagnostic[]> {
		try {
			console.log(`🩺 Collecting LSP diagnostics for ${document.uri.toString()}`)

			// 获取当前文档的诊断信息
			const diagnostics = vscode.languages.getDiagnostics(document.uri)

			console.log(`🩺 Found ${diagnostics.length} diagnostics`)
			return diagnostics
		} catch (error) {
			console.warn("Failed to collect LSP diagnostics:", error)
			return []
		}
	}
}
