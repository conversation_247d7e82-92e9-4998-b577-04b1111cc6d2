# QAX Next Edit 最终实现指南

## 架构设计总结

基于对现有代码库的深入分析，QAX Next Edit 的完整架构设计已经完成。本文档提供最终的实现指导。

## 核心设计原则

### 1. 建议数组维护机制
- **每个文档独立的建议数组**：使用 `Map<string, DocumentSuggestionData>` 存储
- **文档变更时自动过滤**：通过 `anchor` 模式验证建议有效性
- **生命周期管理**：创建→验证→显示→执行/忽略→清理

### 2. 精确触发机制
```typescript
// 触发条件判断
if (isPositionAtLineEnd(document, position)) {
    // 触发 Autocomplete
    return triggerAutocomplete()
} else if (isPositionInLineMiddle(document, position)) {
    // 1秒防抖后触发 AST+LSP 分析
    return debounceASTLSPAnalysis()
} else if (isDocumentIdle(3000) && !hasUnexecutedSuggestions()) {
    // 触发模型推荐
    return triggerModelRecommendation()
}
```

### 3. 双模式分析引擎
- **AST+LSP 快速模式**：0.1秒响应，语法级别分析
- **AI 模型深度模式**：5秒响应，逻辑完整性分析

### 4. 建议JSON格式（严格按用户要求）
```json
{
  "type": "add|modify|delete",
  "description": "Change: xxx -> yyy | Change: Add xxx | Change: Del xxx",
  "location": {
    "anchor": "unique code pattern to locate the position",
    "position": "before|after|replace"
  },
  "patch": {
    "new_content": "new content to insert/replace with"
  }
}
```

## 与现有系统集成

### 1. AutocompleteProvider 集成
```typescript
// 复用现有防抖配置
const getDebounceMs = () => {
    const configManager = AutocompleteConfigManager.instance
    const settings = configManager.getSettings()
    return settings.debounceMs || 300
}

// 互斥触发控制
class TriggerManager {
    shouldTriggerAutocomplete(document, position): boolean {
        return this.isPositionAtLineEnd(document, position)
    }
    
    shouldTriggerNextEdit(document, position): boolean {
        return this.isPositionInLineMiddle(document, position) && 
               !this.autocompleteTaskManager.isTemporarilyDisabled()
    }
}
```

### 2. TreeSitter 服务集成
```typescript
// 复用现有解析器
import { getParserForFile, getAst } from '@services/tree-sitter'

class ASTLSPAnalyzer {
    async parseASTIncremental(document: vscode.TextDocument): Promise<ASTNode> {
        const parser = await getParserForFile(document.uri.fsPath)
        return parser ? parser.parse(document.getText()) : null
    }
}
```

### 3. 配置系统集成
```typescript
// 添加到 VSCode 配置
// package.json 中添加：
"contributes": {
    "configuration": {
        "title": "QAX Next Edit",
        "properties": {
            "qax-codegen.nextEdit.enabled": {
                "type": "boolean",
                "default": true,
                "description": "Enable QAX Next Edit suggestions"
            },
            "qax-codegen.nextEdit.modelRecommendation.debounceMs": {
                "type": "number",
                "default": 3000,
                "description": "Model recommendation debounce time in milliseconds"
            }
        }
    }
}
```

## 关键实现细节

### 1. 完整Diff生成器
```typescript
class IncrementalDiffGenerator {
    generateCompleteDiff(
        documentUri: string,
        changes: vscode.TextDocumentChangeEvent[]
    ): DocumentDiff {
        // 累积变更，生成语义完整的差异
        const accumulatedChanges = this.accumulateChanges(changes)
        
        return {
            additions: this.extractAdditions(accumulatedChanges),
            deletions: this.extractDeletions(accumulatedChanges),
            modifications: this.extractModifications(accumulatedChanges),
            isComplete: true,
            diffType: this.determineChangeType(accumulatedChanges),
            affectedSymbols: this.extractAffectedSymbols(accumulatedChanges)
        }
    }
}
```

### 2. 建议过滤机制
```typescript
class SuggestionManager {
    async filterObsoleteSuggestions(
        documentUri: string,
        changes: vscode.TextDocumentChangeEvent[]
    ): Promise<Suggestion[]> {
        const suggestions = this.getSuggestions(documentUri)
        const validSuggestions: Suggestion[] = []
        
        for (const suggestion of suggestions) {
            // 验证 anchor 是否仍然存在
            if (await this.validateAnchor(suggestion.location.anchor, documentUri)) {
                validSuggestions.push(suggestion)
            }
        }
        
        return validSuggestions
    }
}
```

### 3. 性能优化策略
```typescript
// AST 缓存优化
class ASTCache {
    private cache = new Map<string, ASTCacheEntry>()
    
    async updateIncremental(
        documentUri: string,
        changes: vscode.TextDocumentChangeEvent[]
    ): Promise<void> {
        const cached = this.cache.get(documentUri)
        if (!cached) {
            // 首次解析
            await this.parseFullAST(documentUri)
            return
        }
        
        // 判断是否需要增量更新
        if (this.shouldIncrementalUpdate(changes)) {
            await this.updateASTRange(cached, changes)
        } else {
            await this.parseFullAST(documentUri)
        }
    }
}
```

## 文件结构规划

```
src/qax/services/next-edit/
├── NextEditManager.ts              # 主管理器
├── managers/
│   ├── SuggestionManager.ts        # 建议管理器
│   ├── TriggerManager.ts           # 触发管理器
│   └── AnalysisEngine.ts           # 分析引擎
├── analyzers/
│   ├── ASTLSPAnalyzer.ts          # AST+LSP分析器
│   ├── ModelAnalyzer.ts           # 模型分析器
│   └── DiffGenerator.ts           # 差异生成器
├── cache/
│   ├── ASTCache.ts                # AST缓存
│   ├── LSPCache.ts                # LSP缓存
│   └── CacheManager.ts            # 缓存管理器
├── types/
│   ├── Suggestion.ts              # 建议数据结构
│   ├── AnalysisContext.ts         # 分析上下文
│   └── NextEditSettings.ts        # 配置接口
├── utils/
│   ├── PositionDetector.ts        # 位置检测工具
│   ├── DebounceManager.ts         # 防抖管理器
│   └── SuggestionValidator.ts     # 建议验证器
└── prompts/
    └── ModelPromptTemplate.ts     # 模型提示词模板
```

## 实现步骤

### 阶段一：基础架构（1-2周）
1. 创建核心数据结构和接口
2. 实现 SuggestionManager 建议管理器
3. 实现 TriggerManager 触发管理器
4. 基础的 VSCode 集成

### 阶段二：分析引擎（2-3周）
1. 实现 DiffGenerator 完整差异生成
2. 实现 ASTLSPAnalyzer 快速分析器
3. 实现 ModelAnalyzer 深度分析器
4. 集成 TreeSitter 和 LSP 服务

### 阶段三：优化和集成（1-2周）
1. 实现缓存系统
2. 性能优化和调试
3. 与 Autocomplete 完整集成
4. 配置系统和 UI 集成

### 阶段四：测试和完善（1周）
1. 单元测试和集成测试
2. 性能基准测试
3. 用户体验优化
4. 文档完善

## 关键技术挑战

### 1. 建议数组维护
**挑战**：文档变更时准确识别失效建议
**解决方案**：使用代码模式锚点 + 上下文验证

### 2. 0.1秒响应时间
**挑战**：AST+LSP 分析的极致性能优化
**解决方案**：增量解析 + 缓存优化 + 并行处理

### 3. 完整Diff生成
**挑战**：累积变更生成语义完整差异
**解决方案**：状态快照 + 变更累积算法

### 4. 触发精确性
**挑战**：与 autocomplete 的完美互斥
**解决方案**：精确位置检测 + 状态机管理

## 测试策略

### 单元测试
- 建议数组维护逻辑
- 触发条件判断准确性
- Diff 生成完整性
- 建议格式验证

### 集成测试
- 端到端工作流验证
- 与 autocomplete 集成测试
- 性能基准测试
- 多文档并发处理

### 用户体验测试
- 响应时间验证
- 建议质量评估
- UI 交互流畅性
- 错误处理友好性

## 部署和维护

### 配置管理
- 分层配置：全局、工作区、文件级
- 动态配置更新
- 性能参数调优

### 监控和调试
- 性能指标收集
- 错误日志记录
- 分析结果追踪

## 总结

QAX Next Edit 的架构设计充分考虑了：
1. **用户需求**：符合 JSON 格式要求，精确的触发机制
2. **性能要求**：0.1秒和5秒的响应时间目标
3. **系统集成**：与现有 Autocomplete、TreeSitter 等服务的无缝集成
4. **可维护性**：清晰的模块划分，完善的测试策略
5. **扩展性**：支持未来功能扩展和优化

该架构为实现高质量的智能代码编辑助手提供了坚实的技术基础。
