# QAX Next Edit 架构图详解

## 系统交互时序图

### 模型推荐流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant VSCode as VSCode编辑器
    participant NEM as NextEditManager
    participant TM as TriggerManager
    participant AE as AnalysisEngine
    participant MA as ModelAnalyzer
    participant API as ModelAPI
    participant SM as SuggestionManager
    
    User->>VSCode: 打开文档并停止编辑
    VSCode->>NEM: onDocumentChanged事件
    NEM->>TM: 检查触发条件
    
    Note over TM: 等待3秒空闲时间
    TM->>TM: 检查文档空闲状态
    TM->>SM: 检查未执行建议
    SM-->>TM: 无未执行建议
    
    TM->>AE: 触发模型分析
    AE->>AE: 收集最近变更
    AE->>AE: 构建分析上下文
    AE->>MA: 启动模型分析
    
    MA->>MA: 构建提示词
    MA->>API: 发送分析请求
    
    Note over API: 模型处理（目标5秒内）
    API-->>MA: 返回分析结果
    
    MA->>MA: 解析模型响应
    MA-->>AE: 返回建议列表
    
    AE->>AE: 过滤重复建议
    AE-->>SM: 保存建议
    
    SM->>SM: 验证建议有效性
    SM-->>NEM: 建议已保存
    
    NEM-->>VSCode: 显示建议通知
    VSCode-->>User: 展示代码建议
```

### AST+LSP分析流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant VSCode as VSCode编辑器
    participant NEM as NextEditManager
    participant TM as TriggerManager
    participant AE as AnalysisEngine
    participant ASTLSP as ASTLSPAnalyzer
    participant TreeSitter as TreeSitter解析器
    participant LSP as LSP客户端
    participant SM as SuggestionManager
    
    User->>VSCode: 在行中间输入代码
    VSCode->>NEM: onDocumentChanged事件
    NEM->>TM: 检查触发条件
    
    TM->>TM: 检查输入位置（行中间）
    Note over TM: 等待1秒防抖
    
    TM->>AE: 触发AST+LSP分析
    AE->>AE: 生成文档差异
    AE->>ASTLSP: 启动快速分析
    
    ASTLSP->>TreeSitter: 增量解析AST
    TreeSitter-->>ASTLSP: 返回AST节点
    
    ASTLSP->>ASTLSP: 检测变更类型
    
    alt 符号重命名
        ASTLSP->>LSP: 查找符号引用
        LSP-->>ASTLSP: 返回引用位置
        ASTLSP->>ASTLSP: 生成重命名建议
    else 函数签名变更
        ASTLSP->>LSP: 查找函数调用
        LSP-->>ASTLSP: 返回调用位置
        ASTLSP->>ASTLSP: 生成签名更新建议
    else 无法确定类型
        ASTLSP->>ASTLSP: 结束分析
    end
    
    ASTLSP-->>AE: 返回建议列表
    AE-->>SM: 保存建议
    SM-->>NEM: 建议已保存
    NEM-->>VSCode: 显示建议
    
    Note over VSCode: 目标响应时间：0.1秒
```

## 组件依赖关系图

```mermaid
graph TD
    subgraph "核心管理层"
        NEM[NextEditManager]
        SM[SuggestionManager]
        TM[TriggerManager]
    end
    
    subgraph "分析引擎层"
        AE[AnalysisEngine]
        MA[ModelAnalyzer]
        ASTLSP[ASTLSPAnalyzer]
    end
    
    subgraph "基础服务层"
        TS[TreeSitterService]
        LSP[LSPClient]
        API[ModelAPIClient]
        Cache[CacheManager]
    end
    
    subgraph "数据存储层"
        SugStore[SuggestionStore]
        ASTCache[ASTCache]
        LSPCache[LSPCache]
    end
    
    subgraph "外部集成"
        VSCode[VSCode API]
        ModelAPI[AI Model API]
        Autocomplete[AutocompleteProvider]
    end
    
    NEM --> SM
    NEM --> TM
    NEM --> AE
    
    SM --> SugStore
    TM --> Cache
    
    AE --> MA
    AE --> ASTLSP
    
    MA --> API
    ASTLSP --> TS
    ASTLSP --> LSP
    
    TS --> ASTCache
    LSP --> LSPCache
    API --> ModelAPI
    
    NEM --> VSCode
    TM -.-> Autocomplete
    
    Cache --> ASTCache
    Cache --> LSPCache
```

## 数据流向图

```mermaid
flowchart LR
    subgraph "输入层"
        A[用户输入]
        B[文档变更]
        C[文档打开]
    end
    
    subgraph "触发层"
        D[触发条件检查]
        E[防抖处理]
        F[互斥控制]
    end
    
    subgraph "分析层"
        G[差异生成]
        H[上下文收集]
        I[AST解析]
        J[LSP查询]
        K[模型调用]
    end
    
    subgraph "处理层"
        L[建议生成]
        M[建议过滤]
        N[建议验证]
        O[建议排序]
    end
    
    subgraph "存储层"
        P[建议数组]
        Q[AST缓存]
        R[LSP缓存]
    end
    
    subgraph "输出层"
        S[建议显示]
        T[用户交互]
        U[建议执行]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    E --> F
    F --> G
    F --> H
    
    G --> I
    G --> J
    H --> K
    
    I --> L
    J --> L
    K --> L
    
    L --> M
    M --> N
    N --> O
    
    O --> P
    I --> Q
    J --> R
    
    P --> S
    S --> T
    T --> U
    
    U --> B
```

## 缓存架构图

```mermaid
graph TB
    subgraph "缓存管理器"
        CM[CacheManager]
        CL[CacheLifecycle]
        CV[CacheValidator]
    end
    
    subgraph "AST缓存系统"
        AC[ASTCache]
        AP[ASTParser]
        AU[ASTUpdater]
        AM[ASTMetrics]
    end
    
    subgraph "LSP缓存系统"
        LC[LSPCache]
        LQ[LSPQuery]
        LU[LSPUpdater]
        LM[LSPMetrics]
    end
    
    subgraph "缓存存储"
        MS[MemoryStore]
        IS[IndexStore]
        TS[TempStore]
    end
    
    CM --> CL
    CM --> CV
    CM --> AC
    CM --> LC
    
    AC --> AP
    AC --> AU
    AC --> AM
    
    LC --> LQ
    LC --> LU
    LC --> LM
    
    AP --> MS
    AU --> MS
    LQ --> MS
    LU --> MS
    
    AM --> IS
    LM --> IS
    
    CV --> TS
```

## 性能监控架构

```mermaid
graph LR
    subgraph "性能采集"
        A[响应时间监控]
        B[内存使用监控]
        C[缓存命中率监控]
        D[API调用监控]
    end
    
    subgraph "指标聚合"
        E[MetricsCollector]
        F[MetricsAggregator]
        G[MetricsAnalyzer]
    end
    
    subgraph "性能优化"
        H[自动调优]
        I[资源管理]
        J[负载均衡]
    end
    
    subgraph "告警系统"
        K[阈值检查]
        L[告警触发]
        M[性能报告]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    
    G --> H
    G --> I
    G --> J
    
    G --> K
    K --> L
    L --> M
```
