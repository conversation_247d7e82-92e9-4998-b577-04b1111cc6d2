/**
 * QAX Next Edit - Lint 集成使用示例
 * 展示 ModelAnalyzer 如何收集和处理 VS Code lint 问题
 */

import * as vscode from "vscode"
import { ModelAnalyzer } from "../analyzers/ModelAnalyzer"
import { DEFAULT_NEXT_EDIT_SETTINGS } from "../types/NextEditSettings"
import { AnalysisGoal } from "../types/AnalysisContext"

/**
 * 示例：使用 ModelAnalyzer 分析包含 lint 问题的代码
 */
export async function demonstrateLintIntegration() {
	console.log("🔍 QAX Next Edit - Lint 集成示例\n")

	// 1. 创建 ModelAnalyzer 实例
	const analyzer = new ModelAnalyzer(DEFAULT_NEXT_EDIT_SETTINGS)

	// 2. 模拟一个包含 lint 问题的文档
	const problemCode = `
// 这个文件包含多个 lint 问题
function calculateTotal(items) {
    let total = 0;
    let unusedVariable = "not used";  // ESLint: no-unused-vars
    
    for (let i = 0; i < items.length; i++) {
        total += items[i].price
    }  // ESLint: missing semicolon
    
    let result = total;  // ESLint: prefer-const
    return result;
}

// TypeScript: missing type annotations
function processData(data) {
    if (data) {
        return data.map(item => item.value)
    }
    return []
}

// 未使用的导入
import { unused } from 'some-module';

export { calculateTotal, processData };
`

	// 3. 创建模拟文档
	const document = createMockDocument("file:///example/problem-code.ts", problemCode)

	// 4. 创建分析上下文
	const context = {
		document,
		currentContent: problemCode,
		changeHistory: [],
		projectContext: {
			packageInfo: { name: "example-project", version: "1.0.0" },
			dependencies: [
				{ name: "eslint", version: "8.0.0", type: "devDependency" as const },
				{ name: "typescript", version: "4.9.0", type: "devDependency" as const },
			],
			buildConfig: { tool: "webpack" },
			testFiles: [],
			documentationFiles: [],
		},
		codebaseStats: {
			totalFiles: 10,
			totalLines: 500,
			languageDistribution: { typescript: 0.8, javascript: 0.2 },
			complexityMetrics: {
				averageCyclomaticComplexity: 2.5,
				maxFunctionLength: 50,
				averageFunctionLength: 15,
			},
			qualityMetrics: {
				testCoverage: 85,
				duplicationRate: 5,
				technicalDebtIndex: 0.1,
			},
		},
		analysisGoals: [AnalysisGoal.LINT_ISSUES, AnalysisGoal.CODE_FORMATTING],
		recentChanges: [],
		relatedFiles: [],
		workspaceRoot: "/example",
		timestamp: Date.now(),
	}

	try {
		console.log("📝 分析包含 lint 问题的代码...")

		// 5. 生成包含 lint 问题的提示词
		const prompt = await analyzer.buildOptimizedPrompt(context)

		console.log("✅ 提示词生成成功！")
		console.log("\n📋 生成的提示词包含以下部分:")

		// 检查提示词内容
		if (prompt.includes("VS Code 检测到的 Lint 问题")) {
			console.log("   ✅ Lint 问题部分已包含")
		}

		if (prompt.includes("检测到") && prompt.includes("个 lint 问题")) {
			console.log("   ✅ Lint 问题统计已包含")
		}

		if (prompt.includes("eslint")) {
			console.log("   ✅ ESLint 问题已识别")
		}

		if (prompt.includes("警告") || prompt.includes("错误")) {
			console.log("   ✅ 问题严重程度已标注")
		}

		// 显示提示词的 lint 部分
		const lintSection = extractLintSection(prompt)
		if (lintSection) {
			console.log("\n📄 Lint 问题部分预览:")
			console.log("   " + lintSection.split("\n").slice(0, 5).join("\n   "))
			if (lintSection.split("\n").length > 5) {
				console.log("   ...")
			}
		}

		console.log("\n🎯 ModelAnalyzer 现在可以:")
		console.log("   • 自动收集 VS Code 检测到的 lint 问题")
		console.log("   • 识别多种 lint 工具 (ESLint, TSLint, Pylint 等)")
		console.log("   • 按严重程度过滤和排序问题")
		console.log("   • 将 lint 问题格式化为可读文本")
		console.log("   • 在 AI 提示词中包含 lint 问题上下文")
		console.log("   • 为 AI 模型提供具体的修复目标")
	} catch (error) {
		console.error("❌ 分析过程中出现错误:", error)
	} finally {
		// 6. 清理资源
		analyzer.dispose()
	}
}

/**
 * 创建模拟文档
 */
function createMockDocument(uri: string, content: string): vscode.TextDocument {
	const lines = content.split("\n")

	return {
		uri: vscode.Uri.parse(uri),
		fileName: uri,
		isUntitled: false,
		languageId: "typescript",
		version: 1,
		isDirty: false,
		isClosed: false,
		save: () => Promise.resolve(true),
		eol: vscode.EndOfLine.LF,
		lineCount: lines.length,
		lineAt: (lineOrPosition: number | vscode.Position) => {
			const lineNumber = typeof lineOrPosition === "number" ? lineOrPosition : lineOrPosition.line
			return {
				lineNumber,
				text: lines[lineNumber] || "",
				range: new vscode.Range(lineNumber, 0, lineNumber, (lines[lineNumber] || "").length),
				rangeIncludingLineBreak: new vscode.Range(lineNumber, 0, lineNumber + 1, 0),
				firstNonWhitespaceCharacterIndex: 0,
				isEmptyOrWhitespace: (lines[lineNumber] || "").trim().length === 0,
			}
		},
		offsetAt: (position: vscode.Position) => {
			let offset = 0
			for (let i = 0; i < position.line; i++) {
				offset += (lines[i] || "").length + 1
			}
			return offset + position.character
		},
		positionAt: (offset: number) => {
			let currentOffset = 0
			for (let line = 0; line < lines.length; line++) {
				const lineLength = (lines[line] || "").length + 1
				if (currentOffset + lineLength > offset) {
					return new vscode.Position(line, offset - currentOffset)
				}
				currentOffset += lineLength
			}
			return new vscode.Position(lines.length - 1, (lines[lines.length - 1] || "").length)
		},
		getText: () => content,
		getWordRangeAtPosition: () => undefined,
		validateRange: (range: vscode.Range) => range,
		validatePosition: (position: vscode.Position) => position,
	} as any as vscode.TextDocument
}

/**
 * 从提示词中提取 lint 部分
 */
function extractLintSection(prompt: string): string | null {
	const lintStart = prompt.indexOf("## VS Code 检测到的 Lint 问题")
	if (lintStart === -1) return null

	const nextSection = prompt.indexOf("##", lintStart + 1)
	if (nextSection === -1) {
		return prompt.substring(lintStart)
	}

	return prompt.substring(lintStart, nextSection).trim()
}

/**
 * 展示不同语言的 lint 工具支持
 */
export function demonstrateMultiLanguageLintSupport() {
	console.log("\n🌍 多语言 Lint 工具支持:")

	const languageSupport = {
		"TypeScript/JavaScript": ["ESLint", "TSLint", "JSHint", "StandardJS", "Prettier"],
		Python: ["Pylint", "Flake8", "MyPy", "Black", "isort"],
		Rust: ["rustc", "Clippy", "rustfmt"],
		Go: ["golint", "gofmt", "go vet", "staticcheck"],
		"C/C++": ["cppcheck", "clang-tidy", "clang-format"],
		"C#": ["Roslyn Analyzers", "StyleCop"],
		Java: ["Checkstyle", "SpotBugs", "PMD"],
		PHP: ["PHP_CodeSniffer", "Psalm", "PHPStan"],
		Swift: ["SwiftLint", "SwiftFormat"],
		Kotlin: ["ktlint", "detekt"],
	}

	Object.entries(languageSupport).forEach(([language, tools]) => {
		console.log(`   ${language}: ${tools.join(", ")}`)
	})
}

/**
 * 展示 lint 问题的处理流程
 */
export function demonstrateLintProcessingFlow() {
	console.log("\n🔄 Lint 问题处理流程:")
	console.log("   1. 📡 收集 VS Code 诊断信息")
	console.log("   2. 🔍 过滤 lint 相关问题")
	console.log("   3. 📊 按严重程度分类")
	console.log("   4. 📝 格式化为可读文本")
	console.log("   5. 🤖 集成到 AI 提示词")
	console.log("   6. 💡 生成针对性修复建议")
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
	demonstrateLintIntegration()
		.then(() => {
			demonstrateMultiLanguageLintSupport()
			demonstrateLintProcessingFlow()
			console.log("\n🎉 Lint 集成示例完成！")
		})
		.catch(console.error)
}
